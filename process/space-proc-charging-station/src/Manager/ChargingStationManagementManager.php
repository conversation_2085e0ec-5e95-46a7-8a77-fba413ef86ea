<?php

namespace App\Manager;

use App\Exception\UrlNotFoundException;
use App\Helper\ErrorResponse;
use App\Helper\IResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Model\AddressModel;
use App\Model\CoordinateModel;
use App\Model\SessionModel;
use App\Service\ChargingLocationService;
use App\Service\ChargingSessionService;
use App\Service\MongoAtlasQueryService;
use App\Service\UserService;
use App\Trait\LoggerTrait;
use App\Transformer\ShowRoomEligibilityTransformer;
use App\Transformer\WalletTransformer;
use Ramsey\Uuid\Uuid as UuidUuid;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class ChargingStationManagementManager
{
    use LoggerTrait;
    public const USER_COLLECTION = 'userData';

    public const PROVIDER = 'F2MC';
    public const CHARGING_STATION_MANAGEMENT = 'CHARGING_STATION_MANAGEMENT';
    public const NO_PAYEMENT_METHOD = 'noPaymentMethod';
    public const NO_ACCOUNT_LINKING = 'noAccountLinking';
    public const MISSING_PROFILE_DATA = 'missingProfileData';
    public const COMPLETE = 'complete';
    public const ACCEPTED = 'Accepted';
    const CSM_FEATURE_CODE = "CHARGING_STATION_MANAGEMENT";

    public function __construct(
        private ChargingSessionService $service,
        private UserService $userService,
        private MongoAtlasQueryService $mongoService,
        private VehicleManager $vehicleManager,
        private string $spaceB2bUrl,
        private ChargingLocationService $locationService
    ) {}

    public function getHistory(array $params, string $userId): IResponseArrayFormat
    {
        try {
            $this->logger->info(__METHOD__ . ": start calling f2mc get history for params ", $params);
            if (!$token = $this->getTokenByUserId($userId)) {
                return (new ErrorResponse())->setMessage('Token not found')->setCode(Response::HTTP_BAD_REQUEST);
            }
            $response = $this->service->getHistory($params, $token);
            if (Response::HTTP_OK == $response->getCode()) {
                $url = $response->getData()['success']['url'] ?? '';
                if (!$url) {
                    $this->logger->info(__METHOD__ . ": url not found ");
                    throw UrlNotFoundException::make();
                }
                $result['url'] = $url;

                return (new SuccessResponse())->setData($result);
            }
            $responseData = $response->getData()['error'] ?? [];
            $result = $responseData['message'] ?? 'Error while calling f2mc get history';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ': Catched Exception while calling f2mc get history' . $e->getMessage());

            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($e->getCode());
        }
    }

    public function getIshowroomEligibility(string $userId, string $email): IResponseArrayFormat
    {
        try {
            $this->logger->info(__METHOD__ . ": start calling f2mc get Ishowroom Eligibility");
            if (!$token = $this->getTokenByUserId($userId)) {
                return (new ErrorResponse())->setMessage('Token not found')->setCode(Response::HTTP_BAD_REQUEST);
            }
            $response = $this->service->getIshowroomEligibility($token, $email);
            if ($response->getCode() == Response::HTTP_OK) {
                $result = $response->getData()['success'] ?? [];
                $iShowRoomEligibility = ShowRoomEligibilityTransformer::mapIshowRoomData($result);

                return (new SuccessResponse())->setData($iShowRoomEligibility->getIshowRoomEligibilities())->setCode($response->getCode());
            }
            $responseData = $response->getData()['error'] ?? [];
            $result = $responseData['message'] ?? 'Error while calling f2mc get Ishowroom Eligibility';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ': Catched Exception while calling f2mc get Ishowroom Eligibility ' . $e->getMessage());

            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($e->getCode());
        }
    }

    public function getWalletDetail(string $userId): IResponseArrayFormat
    {
        try {
            $this->logger->info(__METHOD__ . ": start calling f2mc get Wallet details");
            if (!$token = $this->getTokenByUserId($userId)) {
                return (new ErrorResponse())->setMessage('Token not found')->setCode(Response::HTTP_BAD_REQUEST);
            }
            $response = $this->service->getWalletDetail($token);
            if (Response::HTTP_OK == $response->getCode()) {
                $result = $response->getData()['success'] ?? [];
                $walletData = WalletTransformer::mapWalletData($result);

                return (new SuccessResponse())->setData($walletData)->setCode($response->getCode());
            }
            $responseData = $response->getData()['error'] ?? [];
            $result = $responseData['message'] ?? 'Error while calling f2mc get Wallet details';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ': Catched Exception while calling f2mc get Wallet details ' . $e->getMessage());

            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($e->getCode());
        }
    }

    public function stopSession(string $userId, string $sessionId): IResponseArrayFormat
    {
        try {
            if (!$token = $this->getTokenByUserId($userId)) {
                return (new ErrorResponse())->setMessage('Token not found')->setCode(Response::HTTP_BAD_REQUEST);
            }
            $responseUrl = $this->buildResponseUrl($sessionId, $userId);
            $this->logger->info(__METHOD__ . ': start calling sys-f2mc stop session');
            $body = [
                'sessionId' => $sessionId,
                'response_url' => $responseUrl,
            ];
            $response = $this->service->stopSession($token, $body);
            if (Response::HTTP_OK == $response->getCode()) {
                $result = $response->getData()['success'] ?? false;
                if (true == $result) {
                    return (new SuccessResponse())->setData('Session stoped successfully')->setCode($response->getCode());
                } else {
                    return (new ErrorResponse())->setMessage('Session was not stoped')->setCode(Response::HTTP_BAD_REQUEST);
                }
            }
            $result = $response->getData()['error']['message'] ?? 'Error while stoping session';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ': Catched Exception while stoping session ' . $e->getMessage());

            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($e->getCode());
        }
    }

    public function startSession(string $userId, string $vin, array $payload): IResponseArrayFormat
    {
        try {
            $token = $this->getTokenByUserId($userId);

            if (!$token) {
                return $this->errorResponse('Token not found', Response::HTTP_BAD_REQUEST);
            }
            $user = $this->getUserByUserId($userId);

            if (isset($user['vehicle'])) {
                $userMongoDbVehicles = $this->getUserByUserId($userId)['vehicle'];
            } else {
                $userMongoDbVehicles = [];
            }

            $userVehicle = $this->findVehicleByVin($vin, $userMongoDbVehicles);
            $featureCodes = $userVehicle['featureCode'] ?? [];
            $chargingStationFeatureCode = $this->findInFeatureCodes($featureCodes, self::CHARGING_STATION_MANAGEMENT);
            $enrolmentStatus = $chargingStationFeatureCode['config']['enrolmentStatus'] ?? '';
            if ($enrolmentStatus == self::NO_PAYEMENT_METHOD) {
                return $this->errorResponse(self::NO_PAYEMENT_METHOD, Response::HTTP_BAD_REQUEST);
            } elseif ($enrolmentStatus == self::NO_ACCOUNT_LINKING) {
                return $this->errorResponse(self::NO_ACCOUNT_LINKING, Response::HTTP_BAD_REQUEST);
            } elseif ($enrolmentStatus == self::COMPLETE) {
                $vehicleId = null;
                if (!$userVehicle) {
                    return $this->errorResponse('Vehicle not found in database', Response::HTTP_NOT_FOUND);
                }

                if (isset($userVehicle['chargingStation']['vehicleId'])) {
                    $vehicleId = $userVehicle['chargingStation']['vehicleId'];
                } else {
                    $vehicles = $this->getUserVehicles($token);
                    $vehicle = $this->findVehicleByVin($vin, $vehicles);
                    if (!$vehicle) {
                        $addVehicleResponse = $this->addVehicle($token, $userVehicle);
                        if ($addVehicleResponse->getStatusCode() !== Response::HTTP_OK) {
                            return $this->errorResponse('Unable to add vehicle', Response::HTTP_BAD_REQUEST);
                        }
                        $vehicleData = json_decode($addVehicleResponse->getContent(), true)['content']["success"];
                        $vehicleId = $vehicleData['id'];
                        if (!$this->setDefaultVehicle($token, $vin, $vehicleData['id'])) {
                            return $this->errorResponse('Unable to set default vehicle', Response::HTTP_BAD_REQUEST);
                        }
                    } else {
                        $vehicleId = $vehicle['id'];
                    }
                }

                $internalSessionId = UuidUuid::uuid4()->toString();
                $responseUrl = $this->buildStartSessionUrl($internalSessionId, $userId);

                $body = [
                    "evseId" => $payload['evseId'],
                    'locationId' => $payload['locationId'],
                    'connectorId' => $payload['connectorId'],
                    'coordinate' => ['lng' => $payload['long'], 'lat' => $payload['lat']],
                    'response_url' => $responseUrl
                ];

                $startSessionResponse = $this->service->startSession($token, $body);

                if (!$this->isSuccessful($startSessionResponse)) {
                    return $this->errorResponse($startSessionResponse->getData()['error']['message'], Response::HTTP_BAD_REQUEST);
                }

                $sessionId = $startSessionResponse->getdata()['success']['data'];
                $chargingStationVehicleData = [
                    'Provider' => self::PROVIDER,
                    'vehicleId' => $vehicleId
                ];
                $chargingStationVehicleDataSession = [
                    'id' => $sessionId,
                    'internalId' => $internalSessionId,
                    'timestamp' => time(),
                    'evseId' => $payload['evseId'],
                    'locationId' => $payload['locationId'],
                    'connectorId' => $payload['connectorId'],
                ];

                $chargingStationVehicleData['session'] = $chargingStationVehicleDataSession;

                $this->userService->updateF2mcUserInMongoDb($chargingStationVehicleData, $userId, $vin);
                $data = [
                    'status' => self::ACCEPTED,
                    'sessionid' => $sessionId ?? ''
                ];
                return (new SuccessResponse())->setData($data)->setCode(Response::HTTP_OK);
            } else {
                return $this->errorResponse('Invalid Status', Response::HTTP_BAD_REQUEST);
            }
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ": Exception while starting session - " . $e->getMessage());

            return $this->errorResponse($e->getMessage(), $e->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function getUserVehicles(string $token): array
    {
        $response = $this->vehicleManager->getList($token)->getArrayFormat();

        return $response["content"]["success"] ?? [];
    }

    private function setDefaultVehicle(string $token, string $vin, string $vehicleId): bool
    {
        $data = [
            'params' => [
                "vin" => $vin
            ],
            'accessToken' => $token,
            "vehicleId" => $vehicleId
        ];

        $response = $this->vehicleManager->update($data)->getJsonFormat();

        return $response->getStatusCode() === Response::HTTP_OK;
    }

    private function addVehicle(string $token, array $vehicle): JsonResponse
    {
        $data = [
            'params' => [
                "vin" => $vehicle['vin']
            ],
            'accessToken' => $token
        ];

        $response = $this->vehicleManager->add($data)->getJsonFormat();

        return $response;
    }

    private function isSuccessful($response): bool
    {
        return $response->getCode() === Response::HTTP_OK && ($response->getData()['success'] ?? false);
    }

    private function errorResponse(string $message, int $code): ErrorResponse
    {
        return (new ErrorResponse())->setMessage($message)->setCode($code);
    }

    private function successResponse(string $message, int $code): SuccessResponse
    {
        return (new SuccessResponse())->setData($message)->setCode($code);
    }

    private function buildResponseUrl(string $sessionId, string $userId): string
    {
        $uri = '/v1/f2mc/charging-station/session-callback?action=stop&id=' . $sessionId . '&userId=' . $userId;

        return $this->spaceB2bUrl . $uri;
    }

    private function buildStartSessionUrl(string $internalSessionId, string $userId): string
    {
        $uri = '/v1/f2mc/charging-station/session-callback?action=start&internalId=' . $internalSessionId . '&userId=' . $userId;

        return $this->spaceB2bUrl . $uri;
    }

    private function findVehicleByVin(string $vin, array $cars = []): ?array
    {
        if ($cars == []) {
            return null;
        }

        return current(array_filter($cars, fn($car) => $car['vin'] === $vin)) ?: null;
    }

    private function getTokenByUserId(string $userId): ?string
    {
        $user = $this->userService->getUserByUserId($userId);
        $data = json_decode($user->getData(), true);

        return $data['documents'][0]['f2mc']['accessToken'] ?? null;
    }

    private function getUserByUserId(string $userId): ?array
    {
        $user = $this->userService->getUserByUserId($userId);
        $data = json_decode($user->getData(), true);

        return $data['documents'][0] ?? null;
    }

    public function applyIshowroomCredit(string $userId, string $id): IResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . "::" . __METHOD__ . ": start calling f2mc get Ishowroom Credit for id: " . $id);
            $user = $this->userService->getUserData($userId);
            $accessToken = $user['f2mc']['accessToken'] ?? null;
            if (!$accessToken) {
                return (new ErrorResponse())->setMessage('Token not found')->setCode(Response::HTTP_BAD_REQUEST);
            }

            $response = $this->service->applyIshowroomCredit($accessToken, $id);

            if ($response->getCode() == Response::HTTP_OK || $response->getCode() == Response::HTTP_CREATED) {
                $result = $response->getData()['success'] ?? [];

                return (new SuccessResponse())->setData($result)->setCode($response->getCode());
            }

            $responseData = $response->getData()['error'] ?? [];
            $result = $responseData['message'] ?? 'Error while calling f2mc get Ishowroom Credit';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ": Catched Exception while calling f2mc apply Ishowroom Credit" . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    public function getSession($userId, $sessionId): IResponseArrayFormat
    {
        try {
            $user = $this->userService->getUserData($userId);
            $accessToken = $user['f2mc']['accessToken'] ?? null;
            if (!$accessToken) {
                return (new ErrorResponse())->setMessage('Token not found')->setCode(Response::HTTP_BAD_REQUEST);
            }
            $this->logger->info(__CLASS__ . " : " . __METHOD__ . " with parameters : " . $userId);
            $response = $this->service->getSession($sessionId, $accessToken);
            if ($response->getCode() == Response::HTTP_OK) {
                $responseData = $response->getData()['success'];
                $responseData = $responseData['data'] ?? $responseData;
                $id = $responseData['locationId'] ?? null;
                $evsesId = $responseData['evseId'] ?? null;
                if ($id && $evsesId) {
                    $payload = [
                        "filters" => [
                            [
                                "field" => "id",
                                "operator" => "equal",
                                "value" => $id
                            ],
                            [
                                "field" => "evses.id",
                                "operator" => "equal",
                                "value" => $evsesId
                            ]
                        ]
                    ];
                    $locationResponse = $this->locationService->getLocations(
                        $payload,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null
                    );
                    $locationData = $locationResponse->getData()['success']['result'][0] ?? [];
                    $sessionData = array_merge($locationData, $responseData);
                    $sessionModel = $this->sessionMapper($sessionData);
                }
                $status = $responseData['status'] ?? null;
                $filter = ['userId' => $userId, 'vehicle.chargingStation.session.id' => $sessionId];
                $update = ['vehicle.$.chargingStation' => ['session' => ['updateDate' => new \DateTime(), 'remoteCommandStatus' => $status, 'id' => $sessionId]]];
                $this->mongoService->updateOne(self::USER_COLLECTION, $filter, $update);

                return (new SuccessResponse())->setData($sessionModel);
            }

            $responseData = $response->getData();
            $errorMessage = $responseData['message'] ?? "Operation failed";

            return (new ErrorResponse())->setMessage($errorMessage)->setCode($response->getCode());
        } catch (\Exception $e) {
            $this->logger->error("Catched Exception in " . __METHOD__ . " : " . __METHOD__ . $e->getMessage());
            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($response->getCode());
        }
    }

    public function getCdrData(string $userId, string $sessionId): IResponseArrayFormat
    {
        try {
            if (!$token = $this->getTokenByUserId($userId)) {
                return (new ErrorResponse())->setMessage('Token not found')->setCode(Response::HTTP_BAD_REQUEST);
            }

            $this->logger->info(__CLASS__ . "::" . __METHOD__ . ": start calling sys-f2mc get CDR Data");

            $response = $this->service->getCdrData($token, $sessionId);
            if ($response->getCode() == Response::HTTP_OK) {
                $cdrData = $response->getData()['success']['data'] ?? false;
                $locationId = $cdrData['locationId'] ?? null;
                $evseId = $cdrData['evseId'] ?? null;
                if ($locationId && $evseId) {
                    $payload = [
                        "filters" => [
                            [
                                "field" => "locationId",
                                "operator" => "equal",
                                "value" => $cdrData['locationId']
                            // ],
                            // [
                            //     "field" => "evses.evseId",
                            //     "operator" => "equal",
                            //     "value" => $cdrData['evseId']
                            ]
                        ]
                    ];
                    $locationResponse = $this->locationService->getLocations(
                        $payload,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null
                    );
                    $locationData = $locationResponse->getData()['success']['result'][0] ?? [];
                    $sessionData = array_merge($locationData, $cdrData);
                    $sessionModel = $this->sessionMapper($sessionData);
                }
                if ($cdrData) {
                    $status = $cdrData['status'];
                    $data['vehicle.$.chargingStation.session.updateDate'] = time();
                    $data['vehicle.$.chargingStation.session.remoteCommandStatus'] = $status;
                    $filter = ['userId' => $userId, 'vehicle.chargingStation.session.id' => $sessionId];

                    $this->userService->updateDBChargingStation($filter, $data);
                }

                return (new SuccessResponse())->setData($sessionModel)->setCode($response->getCode());
            }
            $result = $response->getData()['error']['message'] ?? 'Error for get CDR Data';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . "::" . __METHOD__ . ": Catched Exception while stoping session " . $e->getMessage());

            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($e->getCode());
        }
    }

    public function getPaymentMethodUrl(string $userId): IResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . " : " . __METHOD__ . " with parameters : " . $userId);
            $accessToken = $this->getTokenByUserId($userId);
            if (!$accessToken) {
                return $this->errorResponse('Token not found', Response::HTTP_BAD_REQUEST);
            }

            $response = $this->service->getPaymentMethodUrl($accessToken);

            if ($response->getCode() == Response::HTTP_OK) {
                $responseData = $response->getData();
                $responseData = $responseData['data'] ?? $responseData;

                $paymentData['paymentMethodUrl'] = $responseData['url'] ?? null;

                return (new SuccessResponse())->setData($paymentData);
            }
            $responseData = $response->getData();
            $errorMessage = $responseData['error']['message'] ?? "Error while getting payment method";

            return (new ErrorResponse())->setMessage($errorMessage)->setCode($response->getCode());
        } catch (\Exception $e) {
            $this->logger->error("Catched Exception in " . __METHOD__ . " : " . __METHOD__ . $e->getMessage());

            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($e->getCode());
        }
    }

    public function getPaymentHistory(string $userId): IResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . " : " . __METHOD__ . " with parameters : " . $userId);

            $user = $this->userService->getUserData($userId);
            $accessToken = $user['f2mc']['accessToken'] ?? null;
            if (!$accessToken) {
                return (new ErrorResponse())->setMessage('Token not found')->setCode(Response::HTTP_BAD_REQUEST);
            }

            $response = $this->service->getPaymentHistory($accessToken);

            if ($response->getCode() == Response::HTTP_OK) {
                $responseData = $response->getData()['success'] ?? [];

                return (new SuccessResponse())->setData($responseData);
            }
            $responseData = $response->getData();
            $errorMessage = isset($responseData['error'])
                ? $responseData['error']['message']
                : ($responseData['message'] ?? "operation failed");

            return (new ErrorResponse())->setMessage($errorMessage)->setCode($response->getCode());
        } catch (\Exception $e) {
            $this->logger->error("Caught Exception in " . __METHOD__ . " : " . $e->getMessage());

            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($e->getCode());
        }
    }

    private function findInFeatureCodes(?array $data = [], string $key = "")
    {
        return current(array_filter($data, function ($item) use ($key) {
            return $item['code'] == $key;
        }));
    }

    private function sessionMapper(array $data): SessionModel
    {
        $coordinate = new CoordinateModel();
        $coordinate->setLat($data['coordinates']['latitude'] ?? 0);
        $coordinate->setLon($data['coordinates']['longitude'] ?? 0);
        $address = new AddressModel();
        // $address->setStreetNumber($data['streetNumber'] ?? null);
        $address->setStreetName($data['addressDetail']['address'] ?? "");
        $address->setMunicipality($data['addressDetail']['city'] ?? "");
        $address->setPostalCode($data['addressDetail']['postalCode'] ?? "");
        $address->setCountry($data['addressDetail']['country'] ?? "");
        $address->setCountryCode($data['addressDetail']['country'] ?? "");
        $session = new SessionModel();
        $session->setId($data['id'] ?? "");
        $session->setFixedCommission($data['fixedCommission'] ?? "");
        $session->setCommissionPercentage($data['commissionPercentage'] ?? "");
        $session->setCost($data['cost'] ?? "");
        $session->setEnergy($data['energy'] ?? "");
        $session->setStatus($data['status'] ?? "");
        $session->setStartedAt($data['startedAt'] ?? "");
        $session->setChargingCompletedAt($data['chargingCompletedAt'] ?? "");
        $session->setCreatedAt($data['createdAt'] ?? "");
        $session->setUpdatedAt($data['updatedAt'] ?? "");
        $session->setConnectonId($data['evses'][0]['connectors'][0]['connectorId'] ?? "");
        $session->setTimeZone($data['timeZone'] ?? "");
        $session->setCurrency($data['currency'] ?? "");
        $session->setTotalTime($data['totalTime'] ?? null);
        $session->setUnitTime("ms");
        $session->setPriceText($data['priceText'] ?? "");
        $session->setPower($data['power'] ?? null);
        $session->setDeltaPower($data['deltaPower'] ?? null);
        $session->setName($data['name'] ?? "");
        $session->setLocationId($data['locationId'] ?? "");
        $session->setEvseId($data['evseId'] ?? "");
        $session->setType($data['evses'][0]['connectors'][0]['type'] ?? "");
        if (in_array(strtolower($data['evses'][0]['connectors'][0]['speed'] ?? ""), ['fast', 'regular', 'slow'])) {
            $session->setPowerLevel(strtolower($data['evses'][0]['connectors'][0]['speed'] ?? ""));
        } else {
            $session->setPowerLevel("unknown");
        }
        $session->setPosition($coordinate);
        $session->setAddress($address);
        return $session;
    }

    public function getActiveSession(string $userId, string $vin): IResponseArrayFormat
    {
        try {
            $this->logger->info('Getting active session', [
                'userId' => $userId,
                'vin' => $vin
            ]);

            $response = $this->userService->findUserVehicleByVin($userId, $vin);
            if (Response::HTTP_OK == $response->getCode()) {
                $responseData = $response->getData();
                $success = $responseData['success'] ?? false;
                $vehicle = $responseData['vehicle'] ?? null;
                $f2mc = $responseData['f2mc'] ?? null;

                if ($success && $vehicle && $f2mc) {
                    $accessToken = $f2mc['accessToken'] ?? null;
                    // Check if there's an active session in the vehicle data
                    if (
                        isset($vehicle['chargingStation']) && isset($vehicle['chargingStation']['session'])
                        && $vehicle['chargingStation']['session']['id'] && $accessToken
                    ) {
                        $activeSession = $vehicle['chargingStation']['session'];

                        $this->logger->info(__CLASS__ . " : " . __METHOD__ . " with parameters : " . $userId);
                        $response = $this->service->getSession($activeSession['id'], $accessToken);
                        if ($response->getCode() == Response::HTTP_OK) {
                            $responseData = $response->getData()['success'];
                            $responseData = $responseData['data'] ?? $responseData;
                            if ($responseData['status'] == 'ACTIVE') {
                                return (new SuccessResponse())->setData(['sessionId' => $activeSession['id']])->setCode(Response::HTTP_OK);
                            }
                        }
                    }
                    return (new SuccessResponse())->setData(['sessionId' => null])->setCode(Response::HTTP_OK);
                } else {
                    return (new ErrorResponse())->setMessage('Vehicle not found')->setCode(Response::HTTP_NOT_FOUND);
                }
            }
            $result = $response->getData()['error']['message'] ?? 'Error while getting active session';
            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ': Caught exception while getting active session ' . $e->getMessage());
            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($e->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
