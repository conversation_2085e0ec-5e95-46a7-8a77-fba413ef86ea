<?php

namespace App\Controller;

use App\Trait\ValidationResponseTrait;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\{Request, Response, JsonResponse};
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Manager\EvRoutingManager;
use App\Validator\{BrandValidator, VinValidator};

#[Route('/v1', name: 'ev_routing_')]
class EvRoutingController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/ev_routing', name: 'long_distance_routing', methods: [Request::METHOD_GET])]
    #[OA\Tag(name: 'EV Routing')]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'vin',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        required:true, 
        description: 'Brand',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        required:true,
        description: 'Country',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        required:true,
        description: 'Language',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'position',
        in: 'query',
        description: 'The current position (latitudeCurrent,longitudeCurrent:latitudeDestination,longitudeDestination)',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'minChargeAtDestination',
        in: 'query',
        description: 'minChargeAtDestination',
        required: true,
        schema: new OA\Schema(type: 'number')
    )]
    #[OA\Parameter(
        name: 'traffic',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'integer', enum: ['0', '1'])
    )]
    #[OA\Parameter(
        name: 'avoid_tolls',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'integer', enum: ['0', '1'])
    )]
    #[OA\Parameter(
        name: 'currentCharge',
        in: 'query',
        description: 'currentCharge',
        required: true,
        schema: new OA\Schema(type: 'number')
    )]
    #[OA\Parameter(
        name: 'plug_type',
        in: 'query',
        description: 'plug type. Example IEC_62196_Type_2_Outlet, Combo_to_IEC_62196_Type_2_Base',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'full_response',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'integer', enum: ['0', '1'])
    )]

    #[OA\Response(
        response: 200,
        description: 'OK',
        content: new OA\JsonContent(
            example: [
                "success" => [
                    "summary" => [
                        "lengthInMeters" => 599199,
                        "travelTimeInSeconds" => 25778,
                        "trafficDelayInSeconds" => 170,
                        "trafficLengthInMeters" => 4240,
                        "departureTime" => "2025-04-15T09:34:42+02:00",
                        "arrivalTime" => "2025-04-15T16:44:20+02:00",
                        "batteryConsumptionInkWh" => 55.545773000000004,
                        "remainingChargeAtArrivalInkWh" => 5.*********,
                        "totalChargingTimeInSeconds" => 5264,
                    ],
                    "legs" => [
                        [
                            "summary" => [
                                "lengthInMeters" => 4820,
                                "travelTimeInSeconds" => 733,
                                "trafficDelayInSeconds" => 0,
                                "trafficLengthInMeters" => 0,
                                "departureTime" => "2025-04-15T09:34:42+02:00",
                                "arrivalTime" => "2025-04-15T09:46:55+02:00",
                                "batteryConsumptionInkWh" => 0.28032,
                                "remainingChargeAtArrivalInkWh" => 9.71968,
                                "ChargingInformations" => [
                                    "ChargingConnectionInfo" => [
                                        "chargingVoltageInV" => 400,
                                        "chargingCurrentInA" => 32,
                                        "chargingCurrentType" => "Alternating_Current_3_Phase",
                                        "chargingPowerInkW" => 11,
                                        "chargingPlugType" => "IEC_62196_Type_2_Outlet"
                                    ],
                                    "targetChargeInkWh" => 27.68,
                                    "chargingTimeInSeconds" => 1637,
                                    "chargingParkUuid" => "e3c0a2f4-9daa-46e5-82fc-2a786fd4e657",
                                    "chargingParkExternalId" => "e3c0a2f4-9daa-46e5-82fc-2a786fd4e657",
                                    "chargingParkName" => "TankE GmbH",
                                    "chargingParkLocation" => [
                                        "city" => "Köln",
                                        "postalCode" => "50829",
                                        "countryCode" => "DE"
                                    ],
                                    "chargingParkPaymentOptions" => [
                                        [
                                            "method" => "Subscription",
                                            "brands" => [
                                                "Maserati Charge",
                                                "xx-TEST1",
                                                "MyEasyCharge",
                                                "eSolutions Charging"
                                            ]
                                        ]
                                    ],
                                    "chargingStopType" => "Auto_Generated"
                                ]
                            ],
                            "points" => [
                                [
                                    "latitude" => 51.00459,
                                    "longitude" => 6.90655
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Unprocessable Entity – validation failed',
        content: new OA\JsonContent(
                example: [
                    "error" => [
                        "message" => "validation_failed",
                        "errors" => [
                            "plug_type" => "This value should not be blank."
                        ]
                    ]
                ]
            )
        )]
        
    #[OA\Response(
        response: 400,
        description: 'Bad request'
    )]
    public function getEvRouting( Request $request, ValidatorInterface $validator, EvRoutingManager $evRoutingManager): JsonResponse
    {
        try {
            $queryParams = [
                'brand' => trim($request->query->get('brand')),
                'country' => trim($request->query->get('country')),
                'language' => trim($request->query->get('language')),
                'position' => trim($request->query->get('position')),
                'minChargeAtDestination' => trim($request->query->get('minChargeAtDestination')),
                'traffic' => $request->query->get('traffic'),
                'avoid_tolls' => $request->query->get('avoid_tolls'),
                'currentCharge' => trim($request->query->get('currentCharge')),
                'plug_type' => trim($request->query->get('plug_type')),
                'full_response' => $request->query->get('full_response'),
                'vin' => $request->headers->get('vin', '')
            ];

            // validate 
            $errors = $validator->validate([
                'brand' => $queryParams['brand'],
                'country' => $queryParams['country'],
                'language' => $queryParams['language'],
                'position' => $queryParams['position'],
                'minChargeAtDestination' => $queryParams['minChargeAtDestination'],
                'currentCharge' => $queryParams['currentCharge'],
                'vin' => $queryParams['vin'],
                'plug_type' => $queryParams['plug_type'],
            ], new Assert\Collection([
                'brand' => BrandValidator::getConstraints(),
                'country' => new Assert\NotBlank(),
                'language' => new Assert\NotBlank(),
                'position' => [new Assert\NotBlank()],
                'minChargeAtDestination' => [
                    new Assert\NotBlank(),
                    new Assert\Range([
                        'min' => 0,
                        'max' => 100,
                        'notInRangeMessage' => 'Value must be between {{ min }} and {{ max }}',
                    ]),
                ],
                'currentCharge' => [
                    new Assert\NotBlank(),
                    new Assert\Range([
                        'min' => 0,
                        'max' => 100,
                        'notInRangeMessage' => 'Value must be between {{ min }} and {{ max }}',
                    ]),
                ],
                'vin' => VinValidator::getConstraints(),
                'plug_type' => [new Assert\NotBlank()],
            ]));

            $messages = static::getValidationMessages($errors);

            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();
                return $this->json($response['content'], $response['code']);
            }

            $response = $evRoutingManager->getEvRouting($queryParams)->toArray();
            return $this->json($response['content'], $response['code']);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/ev_routing/charging-station-availability', name: 'charger_availability', methods: ['GET'])]
    #[OA\Tag(name: 'EV Routing')]
    #[OA\Parameter(
        name: 'chargingAvailability',
        in: 'query',
        description: 'A comma-separated list of chargingAvailability ID, previously retrieved from a Search request.',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]

    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'success',
                    properties: [
                        new OA\Property(
                            property: 'chargingStationsAvailability',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'chargingAvailability', type: 'string'),
                                    new OA\Property(
                                        property: 'connectors',
                                        type: 'array',
                                        items: new OA\Items(
                                            properties: [
                                                new OA\Property(property: 'type', type: 'string'),
                                                new OA\Property(property: 'total', type: 'integer'),
                                                new OA\Property(
                                                    property: 'availability',
                                                    properties: [
                                                        new OA\Property(
                                                            property: 'current',
                                                            properties: [
                                                                new OA\Property(property: 'available', type: 'integer'),
                                                                new OA\Property(property: 'occupied', type: 'integer'),
                                                                new OA\Property(property: 'reserved', type: 'integer'),
                                                                new OA\Property(property: 'unknown', type: 'integer'),
                                                                new OA\Property(property: 'outOfService', type: 'integer')
                                                            ],
                                                            type: 'object'
                                                        ),
                                                        new OA\Property(
                                                            property: 'perPowerLevel',
                                                            type: 'array',
                                                            items: new OA\Items(
                                                                properties: [
                                                                    new OA\Property(property: 'powerKW', type: 'number', format: 'float'),
                                                                    new OA\Property(property: 'available', type: 'integer'),
                                                                    new OA\Property(property: 'occupied', type: 'integer'),
                                                                    new OA\Property(property: 'reserved', type: 'integer'),
                                                                    new OA\Property(property: 'unknown', type: 'integer'),
                                                                    new OA\Property(property: 'outOfService', type: 'integer')
                                                                ]
                                                            )
                                                        )
                                                    ],
                                                    type: 'object'
                                                )
                                            ]
                                        )
                                    )
                                ]
                            )
                        )
                    ],
                    type: 'object'
                )
            ],
            type: 'object'
        )
    )]

    #[OA\Response(
        response: 404,
        description: 'Charging connector data not found',
        content: new OA\JsonContent(
            example: [
                "error" => [
                    "message" => "Skipped empty connector for chargingAvailability ID:"
                ]
            ]
        )
    )]

    #[OA\Response(
    response: 422,
    description: 'Unprocessable Entity – validation failed',
    content: new OA\JsonContent(
            example: [
                "error" => [
                    "message" => "validation_failed",
                    "errors" => [
                        "chargingAvailability" => "This value should not be blank."
                    ]
                ]
            ]
        )
    )]
    
    #[OA\Response(
        response: 403,
        description: 'Forbidden'
    )]
    public function getChargingStationsAvailability(Request $request, EvRoutingManager $evRoutingManager, ValidatorInterface $validator)
    {
        try {
            $chargingAvailability = trim($request->query->get('chargingAvailability'));

            $errors = $validator->validate(
                compact('chargingAvailability'),
                new Assert\Collection([
                    'chargingAvailability' => new Assert\NotBlank(),
                ])
                );
                
            $messages = $this->getValidationMessages($errors);

            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();
                return $this->json($response['content'], $response['code']);
            }

            $response = $evRoutingManager->getEvChargingAvailability($chargingAvailability)->toArray();
            return $this->json($response['content'], $response['code']);

        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}