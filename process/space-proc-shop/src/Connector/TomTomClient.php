<?php

namespace App\Connector;

use App\Trait\LoggerTrait;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use App\Helper\WSResponse;

class TomTomClient
{
    use LoggerTrait;
    protected HttpClientInterface $httpClient;
    protected string $baseUrl;
    protected string $apiKey;

    public function __construct(HttpClientInterface $httpClient, string $baseUrl, string $apiKey)
    {
        $this->httpClient = $httpClient;
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->apiKey = $apiKey;
    }

    protected function get(string $endpoint, array $queryParams = [])
    {
        $queryParams['key'] = $this->apiKey;

        try {
            $response = $this->httpClient->request('GET', $this->baseUrl . $endpoint, [
                'query' => $queryParams,
            ]);
            return new WSResponse($response->getStatusCode(), $response->getContent(false));
        } catch (TransportExceptionInterface $e) {
            $this->logger->error('Caught an exception in '.__CLASS__ . " : " . __METHOD__ .' '.$e->getMessage());
            return null;
        }
    }

    protected function post(string $endpoint, array $queryParams = [], array $body = [])
    {
        $queryParams['key'] = $this->apiKey;

        try {
            $response = $this->httpClient->request('POST', $this->baseUrl . $endpoint, [
                'query' => $queryParams,
                'json' => $body,
            ]);
            return new WSResponse($response->getStatusCode(), $response->getContent(false));
        } catch (TransportExceptionInterface $e) {
            $this->logger->error('Caught an exception in '.__CLASS__ . " : " . __METHOD__ .' '.$e->getMessage());
            return null;
        }
    }
}
