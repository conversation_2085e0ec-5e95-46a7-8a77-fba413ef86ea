<?php
namespace App\Service;

use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use App\Helper\ErrorResponse;
use App\Connector\TomTomClient;
class TomTomService extends TomTomClient
{
    use LoggerTrait;
    public function getLongDistanceEvRoute(array $queryParams, array $body, string $location)
    {
        $versionNumber = 1;
        $contentType = 'json';
        $endpoint = '/routing/'.$versionNumber.'/calculateLongDistanceEVRoute/'.$location.'/'.$contentType;
        $this->logger->info(__CLASS__ . '::' . __METHOD__, [
            'queryParams' => $queryParams,
            'body' => $body,
            'location' => $location,
            'endpoint' => $endpoint
        ]);
        
        $response = $this->post($endpoint, $queryParams, $body);
        return $response;
    }

    public function getChargingStationAvailability(string $chargingAvailabilityId)
    {
        try {
            $ext = "json";
            $versionNumber = 2;
            $endpoint = '/search/'.$versionNumber.'/chargingAvailability.'.$ext;

            $queryParams = [
                'chargingAvailability' => $chargingAvailabilityId,
            ];

            $this->logger->info(__CLASS__ . '::' . __METHOD__, [
                'payload' => $queryParams,
                'endpoint' => $endpoint
            ]);

            $response = $this->get($endpoint, $queryParams);
            return $response;

        } catch (\Throwable $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__."-Failed to fetch charging station data from TomTom", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return new ErrorResponse("Internal error while contacting TomTom", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}