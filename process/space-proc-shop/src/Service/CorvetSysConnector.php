<?php

namespace App\Service;

use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class CorvetSysConnector
{

    public function __construct(
        private string $url,
        private HttpClientInterface $client
    ) {
    }

    public function call(
        $method,
        $uri,
        $parameters,
        $headers = []
    ): ResponseInterface {
        return $this->client->request($method, $this->url . $uri, [
            'query'   => $parameters,
            'headers' => $headers,
            'timeout' => 20
        ]);
    }
}
