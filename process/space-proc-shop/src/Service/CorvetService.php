<?php

namespace App\Service;

use App\Exception\CorvetException;
use App\Exception\VehicleNotFoundException;
use App\Trait\LoggerTrait;
use Exception;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CorvetService
{
    const TYPES = [
        'DXD04CD' => 'ELECTRIC',
        'DXD03CD' => 'HYBRID',
        'DXD06CD' => 'HYDROGEN',
    ];

    const DRE_TYPES = [
        'DRE20CD' => 'heating_temp_regulation',
        'DRE22CD' => 'manual_temp_regulation',
        'DRE24CD' => 'automatic_temp_regulation',
    ];
    
    use LoggerTrait;

    /**
     * @param CorvetSysConnector $corvetSysConnector
     */
    public function __construct(
        private CorvetSysConnector $corvetSysConnector
    ) {
    }

    public function getCorvetResponse($brand, $country, $vin, $source = "APP")  
    {
        $this->logger->info("Call corvet for $vin");
        // $uri = "/v1/corvet/{$vin}/data";
        // $response = $this->corvetSysConnector->call(Request::METHOD_GET, $uri, compact('brand'), compact('brand'));

        // if ($response->getStatusCode() != Response::HTTP_OK) {
        //     throw CorvetException::make();
        // }

        // $corvetResponse = $response->toArray(false);
        // return $corvetResponse['success'] ?? [];

        // Mocked corvet data
        $corvetResponse = $this->mockCorvetData();

        if (
            isset($corvetResponse['VEHICULE']['DONNEES_VEHICULE']['LCDV_BASE'])
        ) {
            return $corvetResponse;
        } else {
            throw VehicleNotFoundException::make();
        }
    }

    /**
     * @param $brand
     * @param $country
     * @param $vin
     * @param string $source
     * @param bool $withAttributes
     * @return array
     */
    public function getLcdv($brand, $country, $vin, $source = "APP", $withAttributes = false)
    {
        try {
            $this->logger->info('Get vehicle Data for vin ' . $vin);
            $vin = strtoupper($vin);
            $brand = strtoupper($brand);
            $country = strtoupper($country);
            $source = strtoupper($source);
            $response = $this->getCorvetResponse($brand, $country, $vin, $source);

            if (!$this->checkVehicleExists($response) || !isset($response['VEHICULE']['DONNEES_VEHICULE']['LCDV_BASE'])) {
                $this->logger->error('Vehicle NOT FOUND ' . $vin . '(' . json_encode($response) . ')');
                return $this->_buildErrorResponse(Response::HTTP_NOT_FOUND, "Vehicle Not Found");
            }
            $allAttributes = $response['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            if (!is_array($allAttributes)) {
                $allAttributes = [$allAttributes];
            }
            $attributesData = $this->getDataFromAttributes($allAttributes);
            $attributes = $attributesData['managed_attributes'] ?? [];
            $vehicleData = $response['VEHICULE']['DONNEES_VEHICULE'];
            $getVehicleType =  $this->getType($attributes);
            $vehicleType = ($getVehicleType != -1) ? $getVehicleType : 0;
            $data =  [
                "lcdv" => $vehicleData['LCDV_BASE'],
                "date" => $vehicleData['DATE_ENTREE_COMMERCIALISATION'],
                "warranty_start_date" => $vehicleData['DATE_DEBUT_GARANTIE'] ? $vehicleData['DATE_DEBUT_GARANTIE'] : '',
                "types"  => $this->getVehicleTypes($attributes),
                'type'   =>  $vehicleType,
                'naPvPr' => $vehicleData['N_APV_PR'] ?? '',
                'modelYear' => $vehicleData['ANNEE_MODELE'] ?? '',
                'options' => $attributesData['vehicle_options'] ?? [],
                "additionnal_attributes" => $this->getAdditionalAttributes($vehicleData['LCDV_BASE'] , $allAttributes)
            ];
            if ($withAttributes) {
                $data['attributes'] = $attributes;
                $data['all_attributes'] = $allAttributes;
            }
            return $data;
        } catch (Exception $e) {
           
            $this->logger->error('Error: Get vehicle Data for vin ' . $vin . '(' . $e->getMessage() . ')');
            throw CorvetException::make();
        }
    }

    /**
     * @param array $response
     * @return bool
     */
    private function checkVehicleExists(array $response)
    {
        return ($response['VEHICULE']['@attributes']['Existe'] ?? '') == 'O';
    }

    private function getDataFromAttributes(array $attributes)
    {
        $managedAttributes = [];
        $response  = ['vehicle_options' => []];
        foreach ($attributes as $attribute) {
            if (preg_match('/^P(.{4})|^D(.{4})CP$/i', $attribute)) {
                $response['vehicle_options'][] = substr($attribute, 1, 4);
            }

            switch (substr($attribute, 0, 3)) {
                case 'DCX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DXD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DCD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRE':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRC':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DMW':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DVQ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DJY':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D7K':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DME':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DE2':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DZZ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DLX':
                    $managedAttributes[] = trim($attribute);
                    break;
                default:
                    break;
            }
        }
        $response['managed_attributes'] = $managedAttributes;

        return $response;
    }

    // VehicleType
    public static function getVehicleTypes($corvetAttributes)
    {
        $types = [];
        array_walk($corvetAttributes, function($value) use (&$types) {
            if (isset(self::TYPES[trim($value)])) {
                $types[] = self::TYPES[trim($value)];
            }
        });
        return array_unique($types);
    }

    /**
     * Get the number of the type of the vehicle
     * @param $corvetAttributes
     * @return int
     */
    public static function getType($corvetAttributes)
    {
        foreach ($corvetAttributes as $key => $value) {
            $rest = substr($value, 0, 3);
            if($rest === 'DXD'){
                return (int)substr($value, 3, 2);
            }
        }
        return -1;
    }
    
    /**
     * VehicleType
     * getAdditionalAttributes
     *
     * @param array $corvetAttributes
     * @return array
     */
    public static function getAdditionalAttributes(string $lcdv , array $corvetAttributes)
    {
        $supportedTypes = ['DVQ', 'DAR'];

        $result = [
            'b0f' => substr($lcdv, 7, 2)
        ];
        foreach ($corvetAttributes as $value) {
            $name = substr($value, 0, 3);
            if (in_array($name,$supportedTypes)) {
                $result[strtolower($name)] = substr($value, 3, 2);
            }
        }
        return $result;
    }

    /**
     * Build error response
     * @param $code
     * @param $content
     * @return array
     */
    private function _buildErrorResponse($code, $content)
    {
        return [
            'error' => [
                'code' => $code,
                'content' => $content
            ],
            'success' => false
        ];
    }

    public function mockCorvetData(): array
    {
        $array = [
            "ENTETE" => [
                "EMETTEUR" => "MYM_PREPROD"
            ],
            "VEHICULE" => [
                "@attributes" => [
                    "Existe" => "O"
                ],
                "DONNEES_VEHICULE" => [
                    "WMI" => "VYE",
                    "VDS" => "ATTEN0",
                    "VIS" => "SPU00024",
                    "VIN" => "ZARHBTTG3R9011445",
                    "LCDV_BASE" => "1JJPSYNZDFL0A0A0M0ZZGHFY",
                    "N_APV_PR" => [],
                    "ANNEE_MODELE" => "0A",
                    "MARQUE_COMMERCIALE" => "0J",
                    "DATE_DEBUT_GARANTIE" => [],
                    "DATE_ENTREE_COMMERCIALISATION" => "18/12/2024 17:23:00",
                    "LIGNE_DE_PRODUIT" => "JP"
                ],
                "LISTE_ATTRIBUTES_7" => [
                    "ATTRIBUT" => [
                        "D0000CD", "D0103CD", "D0202CD", "D0301CD", "D0400CD", "D0500CD", "D0600CD", "D0701CD", "D0800CD",
                        "D0900CD", "D1000CD", "D1100CD", "D1202CD", "D1301CD", "D1500CD", "D1701CD", "D1801CD", "D1901CD",
                        "D1C03CD", "D1D05CD", "D1E55CD", "D1F24CD", "D1G30CD", "D1H09CD", "D2501CD", "D2802CD", "D2900CD",
                        "D2A00CD", "D3201CD", "D4100CD", "D4401CD", "D4I00CD", "D4K00CD", "D5000CD", "D5102CD", "D5901CD",
                        "D5M04CD", "D5N08CD", "D5O01CD", "D6007CD", "D6100CD", "D6200CD", "D6302CD", "D6404CD", "D6508CD",
                        "D6602CD", "D6706CD", "D6803CD", "D6D03CD", "D6E01CD", "D6F02CD", "D6G03CD", "D6H05CD", "D6J00CD",
                        "D6K04CD", "D6L04CD", "D6N01CD", "D6O00CD", "D6Q01CD", "D6V07CD", "D6W02CD", "D6X03CD", "D7003CD",
                        "D7A01CD", "D7B02CD", "D7C02CD", "D7E00CD", "D7H01CD", "D7K02CD", "D7L00CD", "D7P02CD", "D7Q02CD",
                        "D7S02CD", "D7T02CD", "D7U01CD", "D7V02CD", "D7W02CD", "D7X00CD", "D7Z04CD", "DA300CD", "DA401CD",
                        "DA516CD", "DA639CD", "DA702CD", "DAB00CD", "DAE00CD", "DAF01CD", "DAGCDCD", "DAH02CD", "DAJ01CD",
                        "DAK06CD", "DAL45CD", "DAO05CD", "DAP01CD", "DAQ00CD", "DAR29CD", "DAS03CD", "DAU02CD", "DAZ10CD",
                        "DBF01CD", "DBJ03CD", "DBK60CD", "DBS00CD", "DBU11CD", "DCD00CD", "DCF14CD", "DCG18CD", "DCK04CD",
                        "DCL12CD", "DCN04CD", "DCO01CD", "DCP01CD", "DCQ06CD", "DCU22CD", "DCX01CD", "DD429CD", "DD606CD",
                        "DDA41CD", "DDC00CD", "DDD04CD", "DDE02CD", "DDG00CD", "DDH82CD", "DDI00CD", "DDJ03CD", "DDO01CD",
                        "DDR07CD", "DDT00CD", "DDX02CD", "DDY23CD", "DDZI7CD", "DE201CD", "DE301CD", "DE704CD", "DE803CD",
                        "DED44CD", "DEE37CD", "DEF00CD", "DEG23CD", "DEHEOCD", "DEJ06CD", "DEK08CD", "DEL01CD", "DENWWCD",
                        "DES03CD", "DEZZZCD", "DFG12CD", "DFH05CD", "DFI08CD", "DFT02CD", "DFU00CD", "DFX00CD", "DGA01CD",
                        "DGH01CD", "DGMAZCD", "DGQ22CD", "DGY08CD", "DGZ00CD", "DHB39CD", "DHE00CD", "DHG06CD", "DHJ00CD",
                        "DHM00CD", "DHU24CD", "DHY03CD", "DI202CD", "DI301CD", "DI402CD", "DI501CD", "DI604CD", "DI702CD",
                        "DI801CD", "DI901CD", "DIB01CD", "DIF10CD", "DIM18CD", "DIN07CD", "DIO02CD", "DIP03CD", "DIQ02CD",
                        "DIT14CD", "DIU16CD", "DIW00CD", "DJA25CD", "DJB04CD", "DJD02CD", "DJQ00CD", "DJY11CD", "DK303CD",
                        "DK906CD", "DKU03CD", "DKX41CD", "DL311CD", "DL600CD", "DL700CD", "DL801CD", "DL900CD", "DLA10CD",
                        "DLB13CD", "DLD00CD", "DLE12CD", "DLI16CD", "DLN03CD", "DLV02CD", "DLW02CD", "DLX54CD", "DLZ06CD",
                        "DMG08CD", "DMH00CD", "DMI61CD", "DMJAKCD", "DMO13CD", "DMW13CD", "DN100CD", "DN400CD", "DN510CD",
                        "DN706CD", "DN803CD", "DN904CD", "DNA09CD", "DNB08CD", "DNC05CD", "DNF15CD", "DNG00CD", "DNH01CD",
                        "DNK05CD", "DNM00CD", "DNN01CD", "DNR00CD", "DO103CD", "DO301CD", "DO409CD", "DO506CD", "DO813CD",
                        "DO906CD", "DOA04CD", "DOCADCD", "DOD02CD", "DOK00CD", "DOL11CD", "DOP01CD", "DOR03CD", "DOS01CD",
                        "DOY25CD", "DPDZCCD", "DPKADCD", "DPLNVCD", "DPQ02CD", "DPR04CD", "DPS02CD", "DPY13CD", "DQA05CD",
                        "DQB48CD", "DQC00CD", "DQF00CD", "DQH20CD", "DQJ04CD", "DQK15CD", "DQS10CD", "DQT00CD", "DQV03CD",
                        "DQX01CD", "DRA01CD", "DRC71CD", "DRE24CD", "DRG40CD", "DRH14CD", "DRI00CD", "DRJ05CD", "DRK05CD",
                        "DRP02CD", "DRQ01CD", "DRS19CD", "DRT21CD", "DRU20CD", "DRZ89CD", "DSB00CD", "DSD04CD", "DSH02CD",
                        "DSO01CD", "DSP16CD", "DTC00CD", "DTG09CD", "DTJ02CD", "DUB24CD", "DUC00CD", "DUE05CD", "DUF01CD",
                        "DUR00CD", "DUV37CD", "DUW00CD", "DUZ19CD", "DVD09CD", "DVF37CD", "DVH37CD", "DVKAICD", "DVO01CD",
                        "DVQ72CD", "DVS05CD", "DVU01CD", "DVW00CD", "DVX23CD", "DWAICCD", "DXA00CD", "DXC11CD", "DXD04CD",
                        "DXF00CD", "DXG24CD", "DXQAZCD", "DXU00CD", "DXZ01CD", "DYB01CD", "DYC00CD", "DYE01CD", "DYH02CD",
                        "DYI45CD", "DYK13CD", "DYM25CD", "DYP00CD", "DYQ02CD", "DYR22CD", "DYT31CD", "DYU03CD", "DYV19CD",
                        "DYW25CD", "DZE34CD", "DZICUCD", "DZJFVCD", "DZVNICD", "DZZ0JCD", "T1AADG", "T1BADG", "T9AADG",
                        "T9BADG", "T9CADG", "T9DADG", "NEW1CD", "NEW2CD"
                    ]
                ]
            ]
        ];
        return $array;
    }

}
