<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Connector\CustomHttpClient;
use Webmozart\Assert\Assert;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;

class EvRoutingService
{
    use LoggerTrait;

    public const USER_COLLECTION = 'userData';
    public const BO_EV_ROUTING_COLLECTION = 'boEvRouting';

    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private CustomHttpClient $client,
        private string $boApiBaseUrl
    ) {
    }

    public function getLcdvByVin($vin)
    {
        try {
            $filter = [
                'vehicle' => [
                    '$elemMatch' => [
                        'vin' => $vin
                    ],
                ],
            ];
            $response = $this->mongoService->find(self::USER_COLLECTION, $filter);

            if (Response::HTTP_OK === $response->getCode()) {
                $responseData = json_decode($response->getData(), true);
                $lcdv = '';

                if (
                    isset($responseData['documents'][0]['vehicle'][0]) &&
                    array_key_exists('versionId', $responseData['documents'][0]['vehicle'][0])
                ) {
                    $lcdv = $responseData['documents'][0]['vehicle'][0]['versionId'];
                }

                return $lcdv;
            }
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error fetching LCDV value', __METHOD__),
                [
                    'vin' => $vin
                ]
            );
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }   

    /**
     * Get BO EV Routing data
     * 
     * @param array $params Contains lcdv, dvq, dar, b0f, brand, country, language
     * @return array|null BO EV Routing data or null on error
     */
    public function getBoEvRouting($params) {
        try {
            $uri = $this->boApiBaseUrl . '/v1/ev_routing';
            $method = Request::METHOD_GET;

            $options = [
                'query' => [
                    'lcdv' => $params['lcdv'],
                    'dvq' => $params['dvq'],
                    'dar' => $params['dar'],
                    'b0f' => $params['b0f'],
                    'brand' => $params['brand'],
                    'country' => $params['country'],
                    'language' => $params['language']
                ],
            ];
            $this->logger->info(__METHOD__.':: '.$uri.' options '.json_encode($options));
            
            $response = $this->client->request($method, $uri, $options);
            if (Response::HTTP_OK === $response->getCode()) {
               return $response->getData()['data']?? null;
            }

            return null;
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error fetching BO EV Routing data', __METHOD__),
                [
                    'params' => $params,
                    'error' => $e->getMessage()
                ]
            );
            return null;
        }
    }    
}