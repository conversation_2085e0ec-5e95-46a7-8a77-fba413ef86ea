<?php

namespace App\Manager;

use App\Helper\{ResponseArrayFormat, ErrorResponse, SuccessResponse};
use App\Service\PHYDService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Web<PERSON>zart\Assert\Assert;
use App\Manager\UserManager;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;

/**
 * PHYD Manager.
 */
class PHYDManager
{
    use LoggerTrait;

    /**
     * @var PHYDService
     */
    private $PHYDService;

    function __construct(PHYDService $PHYDService,
    private UserManager $userManager)
    {
        $this->PHYDService = $PHYDService;
        $this->userManager = $userManager;
    }

    /**
     * PHYD - Getting Driving Score Data
     * @param string $userId
     * @param string $vin
     * @return ResponseArrayFormat
     */
    public function getDrivingScore(string $userId, string $vin): ResponseArrayFormat
    {
        try {
            $this->logger->info("PHYDManager::getDrivingScore for vin " . $vin);

            $userData = $this->userManager->getUserByUserId($userId);
            $userData = (array) json_decode($userData->getData(), true)['documents'] ?? [];

            if (empty($userData)) {
                $this->logger->error('No user found', ['userId' => $userId]);
                throw new UnrecoverableMessageHandlingException('No user found');
            }

            $vehicle = $this->findVehicleFromUserDataByVin($userData, $vin);
            if ($vehicle) {
                $PHYDfeatureCode = $this->getFeatureCode($userData, PHYDService::FEATURE_CODE_PHYD);
                
                if ($PHYDfeatureCode['status'] == PHYDService::FEATURE_CODE_STATUS_ENABLE) {
                    //Retrieve the record from drivingScore" with $vin
                    $drivingScore = $this->PHYDService->getDrivingScore( $vin);
                    $drivingScore = (array) json_decode($drivingScore->getData(), true)['documents'] ?? [];
                    
                    $drivingScore = $drivingScore[0] ?? null;
                    if (!empty($drivingScore) && !empty($drivingScore['stliPolicyNumber']) && $drivingScore['isValid'] == true) {
                        $result = [];
                        $result['featureCodes'] = $PHYDfeatureCode['code'];
                        $result['featureCodeStatus'] = $PHYDfeatureCode['status'];
                        $result['data'] = $drivingScore;
                        return new SuccessResponse($result);
                    } elseif ($drivingScore['isValid'] == false) {
                        // Update the featureCode “UBI_PHYD” status as “disable” in userData->vehicle
                        $wsResponse = $this->userManager->updateVehicleFeatureCode($userId, $vin, PHYDService::FEATURE_CODE_PHYD, PHYDService::FEATURE_CODE_STATUS_DISABLE);

                        if (Response::HTTP_OK != $wsResponse->getCode()) {
                            $this->logger->error(
                                sprintf('%s: Error calling user service', __METHOD__),
                                [
                                    'user_id' => $userId,
                                    'vin' => $vin,
                                    'response' => $wsResponse->getData(),
                                    'response_code' => $wsResponse->getCode(),
                                ]
                            );
                
                            return new ErrorResponse($wsResponse->getData(), $wsResponse->getCode());
                        }

                        $errorResponse = new ErrorResponse("Driving Score service not available for the vin");
                        $errorResponse->addData('featureCode', $PHYDfeatureCode['code'] ?? '');
                        $errorResponse->addData('featureCodeStatus', PHYDService::FEATURE_CODE_STATUS_DISABLE ?? '');
                        return $errorResponse;
                    }
                } else {
                    //Return error if the vehicle feature code is not present
                    $errorResponse = new ErrorResponse("Driving Score service not available for the vin");
                    $errorResponse->addData('featureCode', $PHYDfeatureCode['code'] ?? '');
                    $errorResponse->addData('featureCodeStatus', $PHYDfeatureCode['status'] ?? '');
                    return $errorResponse;
                }
            } else {
                $this->logger->error('Vehicle not found', ['vin' => $vin]);
                throw new UnrecoverableMessageHandlingException('No Vehicle found');             
            }
        } catch (UnrecoverableMessageHandlingException $e) {
            return new ErrorResponse($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            $this->logger->error("Caught Exception in PHYDManager::getDrivingScore: " . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Finds a vehicle from the given user data array based on a specified VIN.
     *
     * @param array $data       The array of user data containing vehicles.
     * @param string $vinToFind The VIN (Vehicle Identification Number) to search for.
     *
     * @return array|null Returns the vehicle details as an associative array if found,
     *                    or null if no matching vehicle is found.
     */
    function findVehicleFromUserDataByVin($data, $vinToFind) {
        foreach ($data as $entry) {
            if (isset($entry['vehicle'])) {
                foreach ($entry['vehicle'] as $vehicle) {
                    if (isset($vehicle['vin']) && $vehicle['vin'] === $vinToFind) {
                        return $vehicle;
                    }
                }
            }
        }
        return null;
    }

    /**
     * Retrieves the feature code details from the given user data array
     * based on the specified code.
     *
     * @param array $userData    The array of user data containing vehicles and feature codes.
     * @param string $codeToFind The feature code to search for.
     *
     * @return array|null Returns the feature code details if found, or null if not found.
     */
    function getFeatureCode(array $userData, string $codeToFind): ?array
    {
        foreach ($userData as $user) {
            foreach ($user['vehicle'] ?? [] as $vehicle) {
                foreach ($vehicle['featureCode'] ?? [] as $featureCode) {
                    if (($featureCode['code'] ?? null) === $codeToFind) {
                        return $featureCode;
                    }
                }
            }
        }
        return null;
    }

    /**
     * PHYD - Driving Score Activation
     * @param string $userId
     * @param string $vin
     * @param string $stliPolicyNumber
     * @return ResponseArrayFormat
     */
    public function activateDrivingScore(string $userId, string $vin, string $stliPolicyNumber): ResponseArrayFormat
    {
        try {
            $this->logger->info("PHYDManager::Driving Score Activation :: activateDrivingScorefor vin " . $vin);

            $userData = $this->userManager->getUserByUserId($userId);
            $userData = (array) json_decode($userData->getData(), true)['documents'] ?? [];

            if (empty($userData)) {
                $this->logger->error('No user found', ['userId' => $userId]);
                throw new UnrecoverableMessageHandlingException('No user found');
            }

            $vehicle = $this->findVehicleFromUserDataByVin($userData, $vin);
            if ($vehicle) {
                $PHYDfeatureCode = $this->getFeatureCode($userData, PHYDService::FEATURE_CODE_PHYD);
           
                $drivingScore = $this->PHYDService->getDrivingScore( $vin, $stliPolicyNumber);
                $drivingScore = (array) json_decode($drivingScore->getData(), true)['documents'] ?? [];
                $drivingScore = $drivingScore[0] ?? null;

                if ($drivingScore && !empty($drivingScore['isValid'])) {
                    if ($drivingScore['isValid'] == true) {

                        //Handle case when the driving score is valid, updating featureCode status for UBI_PHYD
                        $wsResponse = $this->userManager->updateVehicleFeatureCode($userId, $vin, PHYDService::FEATURE_CODE_PHYD, PHYDService::FEATURE_CODE_STATUS_ENABLE);

                        if (Response::HTTP_OK != $wsResponse->getCode()) {
                            $this->logger->error(
                                sprintf('%s: Error calling user service', __METHOD__),
                                [
                                    'user_id' => $userId,
                                    'vin' => $vin,
                                    'response' => $wsResponse->getData(),
                                    'response_code' => $wsResponse->getCode(),
                                ]
                            );
                
                            return new ErrorResponse($wsResponse->getData(), $wsResponse->getCode());
                        }

                        $result = [
                            'message' => "The service is successfully activated",
                            'featureCode' => $PHYDfeatureCode['code'],
                            'featureCodeStatus' => PHYDService::FEATURE_CODE_STATUS_ENABLE
                        ];
                        return new SuccessResponse($result);
                    } 
                }

                // Handle case when the driving score is invalid or non-existent
                $wsResponse = $this->userManager->updateVehicleFeatureCode($userId, $vin, PHYDService::FEATURE_CODE_PHYD, PHYDService::FEATURE_CODE_STATUS_DISABLE);

                if (Response::HTTP_OK != $wsResponse->getCode()) {
                    $this->logger->error(
                        sprintf('%s: Error calling user service', __METHOD__),
                        [
                            'user_id' => $userId,
                            'vin' => $vin,
                            'response' => $wsResponse->getData(),
                            'response_code' => $wsResponse->getCode(),
                        ]
                    );
        
                    return new ErrorResponse($wsResponse->getData(), $wsResponse->getCode());
                }

                $errorResponse = new ErrorResponse("Driving Score service not available for the vin");
                $errorResponse->setCode(Response::HTTP_NOT_FOUND);
                $errorResponse->addData('featureCode', $PHYDfeatureCode['code'] ?? '');
                $errorResponse->addData('featureCodeStatus', PHYDService::FEATURE_CODE_STATUS_DISABLE);
                return $errorResponse;
                
            } else {
                $this->logger->error('Vehicle not found', ['vin' => $vin]);
                throw new UnrecoverableMessageHandlingException('No Vehicle found');             
            }
        } catch (UnrecoverableMessageHandlingException $e) {
            return new ErrorResponse($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            $this->logger->error("Caught Exception in PHYDManager::getDrivingScore: " . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
