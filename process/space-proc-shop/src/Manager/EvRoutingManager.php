<?php

namespace App\Manager;

use App\Helper\{ErrorResponse, SuccessResponse};
use App\Service\{EvRoutingService, TomTomService, CorvetService};
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;

/**
 * Ev Routing Manager.
 */
class EvRoutingManager
{
    use LoggerTrait;

    /**
     * @var EvRoutingService
     */
    private $evRoutingService;
    
    private $corvetService;

    /**
     * @var TomTomService
     */    private $tomTomService;

    function __construct(EvRoutingService $evRoutingService, TomTomService $tomTomService, CorvetService $corvetService)
    {
        $this->corvetService = $corvetService;
        $this->evRoutingService = $evRoutingService;
        $this->tomTomService = $tomTomService;
    }

    public function getEvChargingAvailability(string $chargingAvailability)
    {
        try {
            $availabilityIds = explode(',', $chargingAvailability);
            $responses = [];
            $noConnectorIds = [];
            $errorFetchingIds = [];

            foreach ($availabilityIds as $id) {
                $id = trim($id);
                if (empty($id)) {
                    continue;
                }

                $tomtomResponse = $this->tomTomService->getChargingStationAvailability($id);
                // Check if response is successful
                if ($tomtomResponse && $tomtomResponse->getCode() === Response::HTTP_OK) {
                    $data = $tomtomResponse->getData();
                    // If getData() returns a string, decode it; otherwise use it directly
                    if (is_string($data)) {
                        $data = json_decode($data, true);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            $this->logger->error(__CLASS__ . "::" . __METHOD__ . " : JSON decode error: " . json_last_error_msg());
                            return new ErrorResponse("Invalid JSON response from TomTom", Response::HTTP_INTERNAL_SERVER_ERROR);
                        }
                    }
                    
                    // Check if connectors array exists and is non-empty
                    if (isset($data['connectors']) && !empty($data['connectors'])) {
                        // Add the chargingAvailability ID to the response
                        $data['chargingAvailability'] = $id;
                        $responses[] = $data;
                    } else {
                        $this->logger->info(__CLASS__ . "::" . __METHOD__ . " : Skipped empty connector for chargingAvailability ID: {$id}");
                        $noConnectorIds[] = $id;
                    }
                } else {
                    $this->logger->info(__CLASS__ . "::" . __METHOD__ . " : Error fetching charging station availability for ID: {$id}");
                    $errorFetchingIds[] = $id;
                }
            }

            if (empty($responses) && !empty($errorFetchingIds)) {
                $errorParts[] = "Error fetching charging station availability for ID: " . implode(', ', $errorFetchingIds);
                
                return new ErrorResponse(implode('; ', $errorParts), Response::HTTP_NOT_FOUND);
            }
            
            return new SuccessResponse(['chargingStationsAvailability' => $responses]);

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . "::" . __METHOD__ . ' : Caught Exception - ' . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    public function getEvRouting(array $params)
    {
        try {
            // Get LCDV and related data from the corvet data API
            $corvetData = $this->corvetService->getLcdv($params['brand'], $params['country'], $params['vin']);

            $lcdv = $corvetData['lcdv'] ?? null;
            if (!$lcdv) {
                $this->logger->error(__CLASS__ . "::" . __METHOD__ . ' : Error lcdv not found while calling corvet');
                return new ErrorResponse('Vehicle LCDV Not Found', Response::HTTP_NOT_FOUND);
            }

            // Extract additional attributes
            $additionalAttrs = $corvetData['additionnal_attributes'] ?? [];
            $dvq = $additionalAttrs['dvq'] ?? '';
            $dar = $additionalAttrs['dar'] ?? '';
            $b0f = $additionalAttrs['b0f'] ?? '';

            if (!$dvq || !$dar || !$b0f) {
                $this->logger->error(__CLASS__ . "::" . __METHOD__ . ' : DVQ or DAR or B0F are not found in corvet response');
                return new ErrorResponse('Required vehicle attributes (DVQ or DAR or B0F) not found', Response::HTTP_NOT_FOUND);
            }

            // Prepare BO query parameters
            $boQuery = [
                'lcdv'     => $lcdv,
                'dvq'      => $dvq,
                'dar'      => $dar,
                'b0f'      => $b0f,
                'brand'    => $params['brand'],
                'country'  => $params['country'],
                'language' => $params['language']
            ];

            // Get BO EV routing data
            $boEvRouting = $this->evRoutingService->getBoEvRouting($boQuery);
            if (!$boEvRouting) {
                $this->logger->error(__CLASS__ . "::" . __METHOD__ . ' : Error BO EV Routing data not found');
                return new ErrorResponse('BO EV Routing Data Not Found', Response::HTTP_NOT_FOUND);
            }

            // Filter charging connectors by plug type

            $chargingCurveArray = $boEvRouting['chargingCurveArray'] ?? null;
            
            if (empty($chargingCurveArray)) {
                $this->logger->error(__CLASS__ . "::" . __METHOD__ . ' : Error charging curve array is empty');
                return new ErrorResponse('Charging Curve Data Not Found', Response::HTTP_NOT_FOUND);
            }
            
            // If it's a JSON string, decode it
            if (is_string($chargingCurveArray)) {
                $chargingCurveArray = json_decode($chargingCurveArray, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->logger->error(__CLASS__ . "::" . __METHOD__ . ' : Error decoding charging curve array: ' . json_last_error_msg());
                    return new ErrorResponse('Invalid Charging Curve Data Format', Response::HTTP_INTERNAL_SERVER_ERROR);
                }
            }
            
            // Final check to ensure we have an array
            if (!is_array($chargingCurveArray)) {
                $this->logger->error(__CLASS__ . "::" . __METHOD__ . ' : Charging curve array is not an array');
                return new ErrorResponse('Invalid Charging Curve Data Format', Response::HTTP_INTERNAL_SERVER_ERROR);
            }

            $this->filterChargingConnectorsByPlugType($chargingCurveArray, $params['plug_type']);

            // Prepare TomTom API options
            $options = $this->prepareTomTomOptions($params, $boEvRouting, $chargingCurveArray);

            // Call TomTom API
            $tomtomResponse = $this->tomTomService->getLongDistanceEvRoute(
                $options['query'], 
                $options['body'], 
                $params['position']
            );
            
            // Process response
            return $this->processTomTomLongDistanceResponse($tomtomResponse);
            
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . "::" . __METHOD__ . ' : Caught Exception - ' . $e->getMessage());
            return new ErrorResponse('Internal server error: ' . $e->getMessage(), 
                $e->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * Filter charging connectors by plug type
     */
    private function filterChargingConnectorsByPlugType(array &$chargingCurveArray, string $plugTypes): void
    {
        $plugTypesArray = explode(',', $plugTypes);
        $chargingConnectors = $chargingCurveArray['chargingParameters']['chargingConnectors'] ?? [];
        $filteredConnectors = [];
        
        foreach ($chargingConnectors as $connector) {
            foreach ($plugTypesArray as $plugType) {
                if (in_array(strtoupper($plugType), array_map('strtoupper', $connector['plugTypes']))) {
                    $filteredConnectors[] = $connector;
                    break; // Once we've added this connector, no need to check other plug types
                }
            }
        }
        
        $chargingCurveArray['chargingParameters']['chargingConnectors'] = $filteredConnectors;
    }
    
    /**
     * Prepare options for TomTom API call
     */
    private function prepareTomTomOptions(array $params, array $boEvRouting, array $chargingCurveArray): array
    {
        $options = [
            'query' => [
                'traffic'                                   => $params['traffic'] == 1 ? 'true' : 'false',
                'vehicleMaxSpeed'                           => $boEvRouting['vehicleMaxSpeed'],
                'vehicleWeight'                             => $boEvRouting['vehicleWeight'],
                'vehicleAxleWeight'                         => $boEvRouting['vehicleAxleWeight'],
                'vehicleLength'                             => $boEvRouting['vehicleLength'],
                'vehicleWidth'                              => $boEvRouting['vehicleWidth'],
                'vehicleHeight'                             => $boEvRouting['vehicleHeight'],
                'vehicleEngineType'                         => $boEvRouting['engineType'],
                'accelerationEfficiency'                    => $boEvRouting['accelerationEfficiency'],
                'decelerationEfficiency'                    => $boEvRouting['decelerationEfficiency'],
                'uphillEfficiency'                          => $boEvRouting['uphillEfficiency'],
                'downhillEfficiency'                        => $boEvRouting['downhillEfficiency'],
                'constantSpeedConsumptionInkWhPerHundredkm' => $boEvRouting['constantSpeedConsumptionInkWhPerHundredkm'],
                'currentChargeInkWh'                        => ($params['currentCharge'] / 100) * $boEvRouting['maxChargeInkWh'],
                'maxChargeInkWh'                            => $boEvRouting['maxChargeInkWh'],
                'minChargeAtDestinationInkWh'               => ($params['minChargeAtDestination'] / 100) * $boEvRouting['maxChargeInkWh'],
                'minChargeAtChargingStopsInkWh'             => (20 / 100) * $boEvRouting['maxChargeInkWh'],
            ],
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'body' => $chargingCurveArray
        ];

        if ($params['avoid_tolls'] == 1) {
            $options['query']['avoid'] = 'tollRoads';
        }
        
        return $options;
    }
    
    /**
     * Process TomTom API response
     * 
     * @param mixed $tomtomResponse The response from TomTom API
     * @return ErrorResponse|SuccessResponse The processed response
     */
    private function processTomTomLongDistanceResponse($tomtomResponse)
    {
        // If response is successful
        if ($tomtomResponse->getCode() === Response::HTTP_OK) {
            // Get the data and ensure it's in array format
            $responseData = $tomtomResponse->getData();
            if (is_string($responseData)) {
                $responseData = json_decode($responseData, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->logger->error(__CLASS__ . "::" . __METHOD__ . " : JSON decode error: " . json_last_error_msg());
                    return new ErrorResponse("Invalid JSON response from TomTom", Response::HTTP_INTERNAL_SERVER_ERROR);
                }
            }
            $data = $this->transformLongdistanceEvRouteResponse($responseData);
            return new SuccessResponse($data);
        }

        // Handle error responses
        $errorData = $tomtomResponse->getData();
        if (is_string($errorData)) {
            $errorData = json_decode($errorData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logger->error(__CLASS__ . "::" . __METHOD__ . " : JSON decode error in error response: " . json_last_error_msg());
                return new ErrorResponse("Invalid JSON error response from TomTom", Response::HTTP_INTERNAL_SERVER_ERROR);
            }
        }
        
        $errorMessage = $errorData['detailedError']['message'] ?? 'Bad request';
        $errorCode = $errorData['detailedError']['code'] ?? Response::HTTP_BAD_REQUEST;

        // If response is 400 with detailedError
        if ($tomtomResponse->getCode() === Response::HTTP_BAD_REQUEST) {
            $this->logger->warning(__CLASS__ . "::" . __METHOD__ . " : TomTom BAD_REQUEST: $errorCode - $errorMessage");
            return new ErrorResponse("TomTom BAD_REQUEST::" . $errorMessage, Response::HTTP_BAD_REQUEST);
        }

        // Other error responses
        $this->logger->error(__CLASS__ . "::" . __METHOD__ . " : Unexpected TomTom response code: " . $tomtomResponse->getCode());
        return new ErrorResponse('Unexpected response from TomTom', Response::HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * Transforms the TomTom long distance routing API response into the MyMarque API response structure.
     *
     * @param array $tomtomData The original TomTom long distance routing response.
     * @return array The formatted response compatible with the MyMarque API structure.
     */
    public function transformLongdistanceEvRouteResponse(array $tomtomData): array
    {
        $route = $tomtomData['routes'][0] ?? null;

        if (!$route) {
            return [];
        }

        $summary = $route['summary'];
        $legs = $route['legs'];

        $formatted = ['legs' => []];

        // Only add summary fields if they exist
        if (!empty($summary)) {
            $formatted['summary'] = [];
            $summaryFields = [
                'lengthInMeters',
                'travelTimeInSeconds',
                'trafficDelayInSeconds',
                'trafficLengthInMeters',
                'departureTime',
                'arrivalTime',
                'batteryConsumptionInkWh',
                'remainingChargeAtArrivalInkWh',
                'totalChargingTimeInSeconds'
            ];

            foreach ($summaryFields as $field) {
                if (isset($summary[$field])) {
                    $formatted['summary'][$field] = $summary[$field];
                }
            }
        }

        foreach ($legs as $leg) {
            $legSummary = $leg['summary'];
            $chargingInfo = $legSummary['chargingInformationAtEndOfLeg'] ?? null;

            $legData = [];

            // Only add summary fields if they exist
            if (!empty($legSummary)) {
                $legData['summary'] = [];
                $legSummaryFields = [
                    'lengthInMeters',
                    'travelTimeInSeconds',
                    'trafficDelayInSeconds',
                    'trafficLengthInMeters',
                    'departureTime',
                    'arrivalTime',
                    'batteryConsumptionInkWh',
                    'remainingChargeAtArrivalInkWh'
                ];

                foreach ($legSummaryFields as $field) {
                    if (isset($legSummary[$field])) {
                        $legData['summary'][$field] = $legSummary[$field];
                    }
                }
            }

            // Only add points if they exist
            if (isset($leg['points'])) {
                $legData['points'] = $leg['points'];
            }

            if ($chargingInfo) {
                $chargingInfoData = [];

                // Handle ChargingConnectionInfo
                if (isset($chargingInfo['chargingConnectionInfo'])) {
                    $connectionInfo = [];
                    $connectionFields = [
                        'chargingVoltageInV',
                        'chargingCurrentInA',
                        'chargingCurrentType',
                        'chargingPowerInkW',
                        'chargingPlugType'
                    ];

                    foreach ($connectionFields as $field) {
                        if (isset($chargingInfo['chargingConnectionInfo'][$field])) {
                            $connectionInfo[$field] = $chargingInfo['chargingConnectionInfo'][$field];
                        }
                    }

                    if (!empty($connectionInfo)) {
                        $chargingInfoData['ChargingConnectionInfo'] = $connectionInfo;
                    }
                }

                // Add other charging info fields if they exist
                $chargingFields = [
                    'targetChargeInkWh',
                    'chargingTimeInSeconds',
                    'chargingParkUuid',
                    'chargingParkExternalId',
                    'chargingParkName',
                    'chargingStopType'
                ];

                foreach ($chargingFields as $field) {
                    if (isset($chargingInfo[$field])) {
                        $chargingInfoData[$field] = $chargingInfo[$field];
                    }
                }

                // Handle chargingParkLocation
                if (isset($chargingInfo['chargingParkLocation'])) {
                    $locationData = [];
                    $locationFields = ['city', 'postalCode', 'countryCode'];

                    foreach ($locationFields as $field) {
                        if (isset($chargingInfo['chargingParkLocation'][$field])) {
                            $locationData[$field] = $chargingInfo['chargingParkLocation'][$field];
                        }
                    }

                    if (!empty($locationData)) {
                        $chargingInfoData['chargingParkLocation'] = $locationData;
                    }
                }

                // Handle chargingParkPaymentOptions
                if (isset($chargingInfo['chargingParkPaymentOptions'])) {
                    $chargingInfoData['chargingParkPaymentOptions'] = array_map(function ($option) {
                        $result = [];
                        if (isset($option['method'])) {
                            $result['method'] = $option['method'];
                        }
                        if (isset($option['brands'])) {
                            $result['brands'] = $option['brands'];
                        }
                        return $result;
                    }, $chargingInfo['chargingParkPaymentOptions']);
                }

                if (!empty($chargingInfoData)) {
                    $legData['summary']['ChargingInformations'] = $chargingInfoData;
                }
            }

            if (!empty($legData)) {
                $formatted['legs'][] = $legData;
            }
        }
        return $formatted;
    }

    private function getMockBoEvRoutingData(): array
    {
        return [
            'enabled' => true,
            'label' => 'CR3 - DAR11 - DVQ72',
            'constantSpeedConsumptionInkWhPerHundredkm' => "40,8.19:50,8.99:60,10.07:70,11.4:80,12.97:90,14.75:100,16.74:110,18.92:120,21.32:130,23.95:140,26.8:150,29.91",
            'engineType' => 'electric',
            'maxChargeInkWh' => '74',
            'vehicleMaxSpeed' => '170',
            'vehicleWeight' => '2240',
            'vehicleAxleWeight' => '1120',
            'vehicleLength' => '4650',
            'vehicleWidth' => '1902',
            'vehicleHeight' => '1690',
            'accelerationEfficiency' => '0.85',
            'decelerationEfficiency' => '0.85',
            'uphillEfficiency' => '0.9',
            'downhillEfficiency' => '0.9',
            'chargingCurveArray' => [
                'chargingParameters' => [
                    'batteryCurve' => [
                        ['stateOfChargeInkWh' => 3.7, 'maxPowerInkW' => 140.0],
                        ['stateOfChargeInkWh' => 18.5, 'maxPowerInkW' => 145.0],
                        ['stateOfChargeInkWh' => 23.7, 'maxPowerInkW' => 95.0],
                        ['stateOfChargeInkWh' => 26.6, 'maxPowerInkW' => 84.0],
                        ['stateOfChargeInkWh' => 45.1, 'maxPowerInkW' => 75.0],
                        ['stateOfChargeInkWh' => 48.8, 'maxPowerInkW' => 68.0],
                        ['stateOfChargeInkWh' => 59.9, 'maxPowerInkW' => 59.0],
                        ['stateOfChargeInkWh' => 71.0, 'maxPowerInkW' => 38.0],
                        ['stateOfChargeInkWh' => 74.0, 'maxPowerInkW' => 30.0],
                    ],
                    'chargingConnectors' => [
                        [
                            'currentType' => 'AC3',
                            'plugTypes' => [
                                'IEC_62196_Type_2_Outlet',
                                'IEC_62196_Type_2_Connector_Cable_Attached'
                            ],
                            'efficiency' => 0.88,
                            'baseLoadInkW' => 0.25,
                            'maxPowerInkW' => 145.0
                        ],
                        [
                            'currentType' => 'DC',
                            'plugTypes' => [
                                'Combo_to_IEC_62196_Type_2_Base'
                            ],
                            'voltageRange' => [
                                'minVoltageInV' => 0,
                                'maxVoltageInV' => 396.7
                            ],
                            'efficiency' => 0.88,
                            'baseLoadInkW' => 0.25,
                            'maxPowerInkW' => 145.0
                        ],
                        [
                            'currentType' => 'AC1',
                            'plugTypes' => [
                                'IEC_62196_Type_2_Outlet',
                                'IEC_62196_Type_2_Connector_Cable_Attached'
                            ],
                            'voltageRange' => [
                                'minVoltageInV' => 0,
                                'maxVoltageInV' => 396.7
                            ],
                            'efficiency' => 0.88,
                            'baseLoadInkW' => 0.25
                        ]
                    ],
                    'chargingTimeOffsetInSec' => 120
                ]
            ]
        ];
    }
}
