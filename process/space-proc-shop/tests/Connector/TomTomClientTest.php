<?php

namespace App\Tests\Connector;

use App\Connector\TomTomClient;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class TomTomClientTest extends TestCase
{
    private TomTomClient $tomTomClient;
    private HttpClientInterface $httpClient;
    private LoggerInterface $logger;
    private string $baseUrl = 'https://api.tomtom.com';
    private string $apiKey = 'test-api-key';
    
    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->tomTomClient = new TomTomClient(
            $this->httpClient,
            $this->baseUrl,
            $this->apiKey
        );
        
        // Set logger
        $reflection = new \ReflectionClass($this->tomTomClient);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->tomTomClient, $this->logger);
    }
    
    public function testGetSuccess(): void
    {
        // Test data
        $endpoint = '/test/endpoint';
        $queryParams = ['param1' => 'value1'];
        $expectedQueryParams = array_merge($queryParams, ['key' => $this->apiKey]);
        
        // Mock HTTP response
        $httpResponse = $this->createMock(ResponseInterface::class);
        $httpResponse->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(200);
        $httpResponse->expects($this->once())
            ->method('getContent')
            ->willReturn('{"success": true}');
            
        // Mock HTTP client
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with(
                'GET',
                $this->baseUrl . $endpoint,
                ['query' => $expectedQueryParams]
            )
            ->willReturn($httpResponse);
            
        // Call the method using reflection to access protected method
        $reflection = new \ReflectionClass($this->tomTomClient);
        $method = $reflection->getMethod('get');
        $method->setAccessible(true);
        $result = $method->invokeArgs($this->tomTomClient, [$endpoint, $queryParams]);
        
        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(200, $result->getCode());
        $this->assertEquals('{"success": true}', $result->getData());
    }
    
    public function testGetWithTransportException(): void
    {
        // Test data
        $endpoint = '/test/endpoint';
        $queryParams = ['param1' => 'value1'];
        
        // Mock HTTP client to throw exception
        $this->httpClient->expects($this->once())
            ->method('request')
            ->willThrowException($this->createMock(TransportExceptionInterface::class));
            
        // Expect logger to be called
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Caught an exception'));
            
        // Call the method using reflection to access protected method
        $reflection = new \ReflectionClass($this->tomTomClient);
        $method = $reflection->getMethod('get');
        $method->setAccessible(true);
        $result = $method->invokeArgs($this->tomTomClient, [$endpoint, $queryParams]);
        
        // Assertions
        $this->assertNull($result);
    }
    
    public function testPostSuccess(): void
    {
        // Test data
        $endpoint = '/test/endpoint';
        $queryParams = ['param1' => 'value1'];
        $body = ['data' => 'test'];
        $expectedQueryParams = array_merge($queryParams, ['key' => $this->apiKey]);
        
        // Mock HTTP response
        $httpResponse = $this->createMock(ResponseInterface::class);
        $httpResponse->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(200);
        $httpResponse->expects($this->once())
            ->method('getContent')
            ->willReturn('{"success": true}');
            
        // Mock HTTP client
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with(
                'POST',
                $this->baseUrl . $endpoint,
                [
                    'query' => $expectedQueryParams,
                    'json' => $body
                ]
            )
            ->willReturn($httpResponse);
            
        // Call the method using reflection to access protected method
        $reflection = new \ReflectionClass($this->tomTomClient);
        $method = $reflection->getMethod('post');
        $method->setAccessible(true);
        $result = $method->invokeArgs($this->tomTomClient, [$endpoint, $queryParams, $body]);
        
        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(200, $result->getCode());
        $this->assertEquals('{"success": true}', $result->getData());
    }
    
    public function testPostWithTransportException(): void
    {
        // Test data
        $endpoint = '/test/endpoint';
        $queryParams = ['param1' => 'value1'];
        $body = ['data' => 'test'];
        
        // Mock HTTP client to throw exception
        $this->httpClient->expects($this->once())
            ->method('request')
            ->willThrowException($this->createMock(TransportExceptionInterface::class));
            
        // Expect logger to be called
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Caught an exception'));
            
        // Call the method using reflection to access protected method
        $reflection = new \ReflectionClass($this->tomTomClient);
        $method = $reflection->getMethod('post');
        $method->setAccessible(true);
        $result = $method->invokeArgs($this->tomTomClient, [$endpoint, $queryParams, $body]);
        
        // Assertions
        $this->assertNull($result);
    }
}
