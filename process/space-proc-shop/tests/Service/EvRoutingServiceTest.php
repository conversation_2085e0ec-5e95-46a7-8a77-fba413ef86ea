<?php

namespace App\Tests\Service;

use App\Service\EvRoutingService;
use App\Service\MongoAtlasQueryService;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class EvRoutingServiceTest extends TestCase
{
    private EvRoutingService $evRoutingService;
    private MongoAtlasQueryService $mongoAtlasQueryService;
    private LoggerInterface $logger;
    
    protected function setUp(): void
    {
        $this->mongoAtlasQueryService = $this->createMock(MongoAtlasQueryService::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->evRoutingService = new EvRoutingService($this->mongoAtlasQueryService);
        
        // Set logger
        $reflection = new \ReflectionClass($this->evRoutingService);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->evRoutingService, $this->logger);
    }
    
    /**
     * Test the getLcdvByVin method with successful response
     */
    public function testGetLcdvByVinSuccess(): void
    {
        // Test data
        $vin = 'VIN123456789';
        $expectedLcdv = 'LCDV123';
        
        // Mock MongoDB response
        $mockResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [
                [
                    'vehicle' => [
                        [
                            'vin' => $vin,
                            'versionId' => $expectedLcdv
                        ]
                    ]
                ]
            ]
        ]));
        
        // Configure MongoDB service mock
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('find')
            ->with(
                EvRoutingService::USER_COLLECTION,
                [
                    'vehicle' => [
                        '$elemMatch' => [
                            'vin' => $vin
                        ],
                    ],
                ]
            )
            ->willReturn($mockResponse);
        
        // Call the method
        $result = $this->evRoutingService->getLcdvByVin($vin);
        
        // Assertions
        $this->assertEquals($expectedLcdv, $result);
    }
    
    /**
     * Test the getLcdvByVin method with empty response
     */
    public function testGetLcdvByVinEmptyResponse(): void
    {
        // Test data
        $vin = 'VIN123456789';
        
        // Mock MongoDB response with no vehicle data
        $mockResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [
                []
            ]
        ]));
        
        // Configure MongoDB service mock
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('find')
            ->willReturn($mockResponse);
        
        // Call the method
        $result = $this->evRoutingService->getLcdvByVin($vin);
        
        // Assertions
        $this->assertEquals('', $result);
    }
    
    /**
     * Test the getLcdvByVin method with error response
     */
    public function testGetLcdvByVinError(): void
    {
        // Test data
        $vin = 'VIN123456789';
        $errorCode = Response::HTTP_INTERNAL_SERVER_ERROR;
        $errorMessage = 'Database error';
        
        // Configure MongoDB service mock to throw exception
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('find')
            ->willThrowException(new \Exception($errorMessage, $errorCode));
        
        // Expect logger to be called
        $this->logger->expects($this->once())
            ->method('error')
            ->with(
                $this->stringContains('Error fetching LCDV value'),
                $this->arrayHasKey('vin')
            );
        
        // Call the method
        $result = $this->evRoutingService->getLcdvByVin($vin);
        
        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($errorCode, $result->getCode());
        $this->assertEquals($errorMessage, $result->getData());
    }
    
    /**
     * Test the getLcdvByVin method with non-200 response
     */
    public function testGetLcdvByVinNon200Response(): void
    {
        // Test data
        $vin = 'VIN123456789';
        
        // Mock MongoDB response with non-200 status
        $mockResponse = new WSResponse(Response::HTTP_NOT_FOUND, json_encode([
            'error' => 'Not found'
        ]));
        
        // Configure MongoDB service mock
        $this->mongoAtlasQueryService->expects($this->once())
            ->method('find')
            ->willReturn($mockResponse);
        
        // Call the method
        $result = $this->evRoutingService->getLcdvByVin($vin);
        
        // Assertions - should return null or empty string based on implementation
        $this->assertNull($result);
    }
}
