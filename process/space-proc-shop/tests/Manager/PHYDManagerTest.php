<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\PHYDManager;
use App\Service\PHYDService;
use App\Manager\UserManager;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;

class PHYDManagerTest extends TestCase
{
    private $phydService;
    private $userManager;
    private $logger;

    protected function setUp(): void
    {
        $this->phydService = $this->createMock(PHYDService::class);
        $this->userManager = $this->createMock(UserManager::class);
        $this->logger = $this->createMock(LoggerInterface::class);
    }

    private function getManager(): PHYDManager
    {
        $manager = new PHYDManager($this->phydService, $this->userManager);
        $manager->setLogger($this->logger);

        return $manager;
    }

    public function testGetDrivingScoreWithValidData(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'VR3UPHNKSKT101603';

        $userData = json_encode([
            'documents' => [[
                'vehicle' => [[
                    'vin' => $vin,
                    'featureCode' => [[
                        'code' => 'UBI_PHYD',
                        'status' => 'enable'
                    ]]
                ]]
            ]]
        ]);

        $drivingScore = json_encode([
            'documents' => [[
                'vin' => $vin,
                'stliPolicyNumber' => 'PNO' . $vin,
                'isValid' => true
            ]]
        ]);

        $this->userManager->method('getUserByUserId')
            ->with($userId)
            ->willReturn(new SuccessResponse($userData));

        $this->phydService->method('getDrivingScore')
            ->with($vin)
            ->willReturn(new SuccessResponse($drivingScore));

        $manager = $this->getManager();
        $response = $manager->getDrivingScore($userId, $vin);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertNotEmpty($response->getData());
    }

    public function testGetDrivingScoreWithInvalidFeatureCode(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'VR3UPHNKSKT101603';

        $userData = json_encode([
            'documents' => [[
                'vehicle' => [[
                    'vin' => $vin,
                    'featureCode' => [[
                        'code' => 'UBI_PHYD',
                        'status' => 'disable'
                    ]]
                ]]
            ]]
        ]);

        $this->userManager->method('getUserByUserId')
            ->with($userId)
            ->willReturn(new SuccessResponse($userData));

        $manager = $this->getManager();
        $response = $manager->getDrivingScore($userId, $vin);
        
        $this->assertInstanceOf(ErrorResponse::class, $response);
        
    }


    public function testGetDrivingScoreWhenNoVehicleFound(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'NONEXISTENT';

        $userData = json_encode([
            'documents' => [[
                'vehicle' => [[
                    'vin' => 'SOMEOTHER123'
                ]]
            ]]
        ]);

        $this->userManager->method('getUserByUserId')
            ->with($userId)
            ->willReturn(new SuccessResponse($userData));

        $manager = $this->getManager();
        $response = $manager->getDrivingScore($userId, $vin);
        $this->assertInstanceOf(ErrorResponse::class, $response);
    }

    public function testGetDrivingScoreHandlesExceptions(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'VR3UPHNKSKT101603';

        $this->userManager->method('getUserByUserId')
            ->willThrowException(new \Exception('Unexpected error'));

        $manager = $this->getManager();
        $response = $manager->getDrivingScore($userId, $vin);

        $this->assertInstanceOf(ErrorResponse::class, $response);
    }

    // Test for a valid driving score activation
    public function testActivateDrivingScoreWithValidData(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'VR3UPHNKSKT101603';
        $stliPolicyNumber = 'PNO123';

        $userData = json_encode([
            'documents' => [[
                'vehicle' => [[
                    'vin' => $vin,
                    'featureCode' => [[
                        'code' => 'UBI_PHYD',
                        'status' => 'disable'
                    ]]
                ]]
            ]]
        ]);

        $drivingScore = json_encode([
            'documents' => [[
                'vin' => $vin,
                'stliPolicyNumber' => $stliPolicyNumber,
                'isValid' => true
            ]]
        ]);

        $this->userManager->method('getUserByUserId')
            ->with($userId)
            ->willReturn(new SuccessResponse($userData));

        $this->phydService->method('getDrivingScore')
            ->with($vin, $stliPolicyNumber)
            ->willReturn(new SuccessResponse($drivingScore));

        $this->userManager->method('updateVehicleFeatureCode')
            ->with($userId, $vin, PHYDService::FEATURE_CODE_PHYD, PHYDService::FEATURE_CODE_STATUS_ENABLE)
            ->willReturn(new SuccessResponse('FeatureCode enabled'));

        $manager = $this->getManager();
        $response = $manager->activateDrivingScore($userId, $vin, $stliPolicyNumber);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertArrayHasKey('message', $response->getData());
        $this->assertEquals('The service is successfully activated', $response->getData()['message']);
    }

    // Test for invalid driving score or unavailability of the service
    public function testActivateDrivingScoreWithInvalidDrivingScore(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'VR3UPHNKSKT101603';
        $stliPolicyNumber = 'PNO123';

        $userData = json_encode([
            'documents' => [[
                'vehicle' => [[
                    'vin' => $vin,
                    'featureCode' => [[
                        'code' => 'UBI_PHYD',
                        'status' => 'disable'
                    ]]
                ]]
            ]]
        ]);

        // Invalid driving score response
        $drivingScore = json_encode([
            'documents' => [[
                'vin' => $vin,
                'stliPolicyNumber' => $stliPolicyNumber,
                'isValid' => false
            ]]
        ]);

        $this->userManager->method('getUserByUserId')
            ->with($userId)
            ->willReturn(new SuccessResponse($userData));

        $this->phydService->method('getDrivingScore')
            ->with($vin, $stliPolicyNumber)
            ->willReturn(new SuccessResponse($drivingScore));

        $this->userManager->method('updateVehicleFeatureCode')
            ->with($userId, $vin, PHYDService::FEATURE_CODE_PHYD, PHYDService::FEATURE_CODE_STATUS_DISABLE)
            ->willReturn(new SuccessResponse('FeatureCode disabled'));

        $manager = $this->getManager();
        $response = $manager->activateDrivingScore($userId, $vin, $stliPolicyNumber);
        $this->assertInstanceOf(ErrorResponse::class, $response);
    }

    // Test for vehicle not found (VIN does not match)
    public function testActivateDrivingScoreWithVehicleNotFound(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'NONEXISTENT_VIN';
        $stliPolicyNumber = 'PNO123';

        $userData = json_encode([
            'documents' => [[
                'vehicle' => [[
                    'vin' => 'SOMEOTHER_VIN'
                ]]
            ]]
        ]);

        $this->userManager->method('getUserByUserId')
            ->with($userId)
            ->willReturn(new SuccessResponse($userData));

        $manager = $this->getManager();
        $response = $manager->activateDrivingScore($userId, $vin, $stliPolicyNumber);

        $this->assertInstanceOf(ErrorResponse::class, $response);
    }

    // Test for an exception being thrown
    public function testActivateDrivingScoreHandlesExceptions(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'VR3UPHNKSKT101603';
        $stliPolicyNumber = 'PNO123';

        $this->userManager->method('getUserByUserId')
            ->willThrowException(new \Exception('Unexpected error'));

        $manager = $this->getManager();
        $response = $manager->activateDrivingScore($userId, $vin, $stliPolicyNumber);

        $this->assertInstanceOf(ErrorResponse::class, $response);
    }
}
