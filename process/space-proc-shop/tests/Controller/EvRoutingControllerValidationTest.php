<?php

namespace App\Tests\Controller;

use App\Controller\EvRoutingController;
use App\Manager\EvRoutingManager;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\DependencyInjection\Container;

#[CoversClass(EvRoutingController::class)]
class EvRoutingControllerValidationTest extends TestCase
{
    private EvRoutingController $controller;
    private ValidatorInterface $validator;
    private EvRoutingManager $evRoutingManager;
    
    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->evRoutingManager = $this->createMock(EvRoutingManager::class);
        $this->controller = new EvRoutingController();
        
        // Set up container for AbstractController
        $container = new Container();
        $containerReflection = new \ReflectionClass($this->controller);
        $containerProperty = $containerReflection->getProperty('container');
        $containerProperty->setAccessible(true);
        $containerProperty->setValue($this->controller, $container);
    }
    
    public function testGetEvRoutingWithValidationErrors(): void
    {
        // Setup test data
        $vin = 'ABCDEFGHIJKLMNOPQ';
        $request = new Request([
            'brand' => '',  // Empty brand to trigger validation error
            'country' => 'TestCountry',
            'language' => 'en',
            'position' => '10.0,20.0:30.0,40.0',
            'minChargeAtDestination' => '20',
            'currentCharge' => '50',
            'plug_type' => 'IEC_62196_Type_2_Outlet'
        ]);
        
        // Create a validation error
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                'This value should not be blank.',
                [],
                null,
                'brand',
                ''
            )
        ]);
        
        // Mock validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);
            
        $response = $this->controller->getEvRouting(
            $vin, 
            $request, 
            $this->validator, 
            $this->evRoutingManager
        );
        
        $this->assertEquals(422, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $content);
        $this->assertArrayHasKey('errors', $content['error']);
        $this->assertArrayHasKey('brand', $content['error']['errors']);
    }
    
    public function testGetChargingStationsAvailabilityWithValidationErrors(): void
    {
        // Setup test data with empty chargingAvailability
        $request = new Request(['chargingAvailability' => '']);
        
        // Create a validation error
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                'This value should not be blank.',
                [],
                null,
                'chargingAvailability',
                ''
            )
        ]);
        
        // Mock validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);
            
        $response = $this->controller->getChargingStationsAvailability(
            $request, 
            $this->evRoutingManager, 
            $this->validator
        );
        
        $this->assertEquals(422, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $content);
        $this->assertArrayHasKey('errors', $content['error']);
        $this->assertArrayHasKey('chargingAvailability', $content['error']['errors']);
    }
}
