<?php

namespace App\Tests\Controller;

use App\Controller\EvRoutingController;
use App\Manager\EvRoutingManager;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\DependencyInjection\Container;

#[CoversClass(EvRoutingController::class)]
class EvRoutingControllerExceptionTest extends TestCase
{
    private EvRoutingController $controller;
    private ValidatorInterface $validator;
    private EvRoutingManager $evRoutingManager;
    
    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->evRoutingManager = $this->createMock(EvRoutingManager::class);
        $this->controller = new EvRoutingController();
        
        // Set up container for AbstractController
        $container = new Container();
        $containerReflection = new \ReflectionClass($this->controller);
        $containerProperty = $containerReflection->getProperty('container');
        $containerProperty->setAccessible(true);
        $containerProperty->setValue($this->controller, $container);
    }
    
    public function testGetEvRoutingWithException(): void
    {
        // Setup test data
        $vin = 'ABCDEFGHIJKLMNOPQ';
        $request = new Request([
            'brand' => 'TestBrand',
            'country' => 'TestCountry',
            'language' => 'en',
            'position' => '10.0,20.0:30.0,40.0',
            'minChargeAtDestination' => '20',
            'currentCharge' => '50',
            'plug_type' => 'IEC_62196_Type_2_Outlet'
        ]);
        
        // Mock validator to return empty violations list
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());
            
        // Mock manager to throw exception
        $this->evRoutingManager->expects($this->once())
            ->method('getEvRouting')
            ->willThrowException(new \Exception('Service unavailable'));
            
        $response = $this->controller->getEvRouting(
            $vin, 
            $request, 
            $this->validator, 
            $this->evRoutingManager
        );
        
        $this->assertEquals(500, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $content);
        $this->assertEquals('Service unavailable', $content['error']);
    }
    
    public function testGetChargingStationsAvailabilityWithException(): void
    {
        // Setup test data
        $request = new Request(['chargingAvailability' => 'station1,station2']);
        
        // Mock validator to return empty violations list
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());
            
        // Mock manager to throw exception
        $this->evRoutingManager->expects($this->once())
            ->method('getEvChargingAvailability')
            ->willThrowException(new \Exception('Service unavailable'));
            
        $response = $this->controller->getChargingStationsAvailability(
            $request, 
            $this->evRoutingManager, 
            $this->validator
        );
        
        $this->assertEquals(500, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $content);
        $this->assertEquals('Service unavailable', $content['error']);
    }
}
