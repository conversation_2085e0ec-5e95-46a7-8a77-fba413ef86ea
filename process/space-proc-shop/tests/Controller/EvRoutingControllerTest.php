<?php

namespace App\Tests\Controller;

use App\Controller\EvRoutingController;
use App\Manager\EvRoutingManager;
use App\Helper\{SuccessResponse, ErrorResponse};
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\DependencyInjection\Container;

class EvRoutingControllerTest extends TestCase
{
    private EvRoutingController $controller;
    private ValidatorInterface $validator;
    private EvRoutingManager $evRoutingManager;
    
    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->evRoutingManager = $this->createMock(EvRoutingManager::class);
        $this->controller = new EvRoutingController();
        
        // Set up container for AbstractController
        $container = new Container();
        $containerReflection = new \ReflectionClass($this->controller);
        $containerProperty = $containerReflection->getProperty('container');
        $containerProperty->setAccessible(true);
        $containerProperty->setValue($this->controller, $container);
    }
    
    // Test successful EV routing request
    public function testGetEvRoutingSuccess(): void
    {
        // Setup test data
        $vin = 'ABCDEFGHIJKLMNOPQ';
        $request = new Request([
            'brand' => 'TestBrand',
            'country' => 'TestCountry',
            'language' => 'en',
            'position' => '10.0,20.0:30.0,40.0',
            'minChargeAtDestination' => '20',
            'traffic' => '1',
            'avoid_tolls' => '0',
            'currentCharge' => '50',
            'plug_type' => 'IEC_62196_Type_2_Outlet',
            'full_response' => '1'
        ]);
        
        // Mock validator to return empty violations list
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());
            
        // Create a success response
        $successResponse = new SuccessResponse([
            'summary' => [
                'lengthInMeters' => 599199,
                'travelTimeInSeconds' => 25778,
                'batteryConsumptionInkWh' => 55.54
            ],
            'legs' => []
        ]);
        
        $this->evRoutingManager->expects($this->once())
            ->method('getEvRouting')
            ->willReturn($successResponse);
            
        $response = $this->controller->getEvRouting(
            $vin, 
            $request, 
            $this->validator, 
            $this->evRoutingManager
        );
        
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertArrayHasKey('summary', $responseData['success']);
    }
    
    // Test EV routing with validation errors
    public function testGetEvRoutingValidationError(): void
    {
        // Setup test data with missing required fields
        $vin = 'ABCDEFGHIJKLMNOPQ';
        $request = new Request([
            'brand' => '',  // Empty brand to trigger validation error
            'country' => 'TestCountry',
            'language' => 'en',
            'position' => '10.0,20.0:30.0,40.0',
            'minChargeAtDestination' => '20',
            'currentCharge' => '50',
            'plug_type' => 'IEC_62196_Type_2_Outlet'
        ]);
        
        // Create a validation error
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                'This value should not be blank.',
                [],
                null,
                'brand',
                ''
            )
        ]);
        
        // Mock validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);
            
        $response = $this->controller->getEvRouting(
            $vin, 
            $request, 
            $this->validator, 
            $this->evRoutingManager
        );
        
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertArrayHasKey('errors', $responseData['error']);
        $this->assertArrayHasKey('brand', $responseData['error']['errors']);
    }
    
    // Test EV routing with exception
    public function testGetEvRoutingException(): void
    {
        // Setup test data
        $vin = 'ABCDEFGHIJKLMNOPQ';
        $request = new Request([
            'brand' => 'TestBrand',
            'country' => 'TestCountry',
            'language' => 'en',
            'position' => '10.0,20.0:30.0,40.0',
            'minChargeAtDestination' => '20',
            'currentCharge' => '50',
            'plug_type' => 'IEC_62196_Type_2_Outlet'
        ]);
        
        // Mock validator to return empty violations list
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());
            
        // Mock manager to throw exception
        $this->evRoutingManager->expects($this->once())
            ->method('getEvRouting')
            ->willThrowException(new \Exception('Service unavailable'));
            
        $response = $this->controller->getEvRouting(
            $vin, 
            $request, 
            $this->validator, 
            $this->evRoutingManager
        );
        
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('Service unavailable', $responseData['error']);
    }
    
    // Test successful charging stations availability request
    public function testGetChargingStationsAvailabilitySuccess(): void
    {
        // Setup test data
        $request = new Request(['chargingAvailability' => 'station1,station2']);
        
        // Mock validator to return empty violations list
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());
            
        // Create a success response
        $successResponse = new SuccessResponse([
            'chargingStationsAvailability' => [
                [
                    'chargingAvailability' => 'station1',
                    'connectors' => []
                ],
                [
                    'chargingAvailability' => 'station2',
                    'connectors' => []
                ]
            ]
        ]);
        
        $this->evRoutingManager->expects($this->once())
            ->method('getEvChargingAvailability')
            ->willReturn($successResponse);
            
        $response = $this->controller->getChargingStationsAvailability(
            $request, 
            $this->evRoutingManager, 
            $this->validator
        );
        
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertArrayHasKey('chargingStationsAvailability', $responseData['success']);
    }
    
    // Test charging stations availability with validation errors
    public function testGetChargingStationsAvailabilityValidationError(): void
    {
        // Setup test data with empty chargingAvailability
        $request = new Request(['chargingAvailability' => '']);
        
        // Create a validation error
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                'This value should not be blank.',
                [],
                null,
                'chargingAvailability',
                ''
            )
        ]);
        
        // Mock validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);
            
        $response = $this->controller->getChargingStationsAvailability(
            $request, 
            $this->evRoutingManager, 
            $this->validator
        );
        
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertArrayHasKey('errors', $responseData['error']);
        $this->assertArrayHasKey('chargingAvailability', $responseData['error']['errors']);
    }
    
    // Test charging stations availability with exception
    public function testGetChargingStationsAvailabilityException(): void
    {
        // Setup test data
        $request = new Request(['chargingAvailability' => 'station1,station2']);
        
        // Mock validator to return empty violations list
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());
            
        // Mock manager to throw exception
        $this->evRoutingManager->expects($this->once())
            ->method('getEvChargingAvailability')
            ->willThrowException(new \Exception('Service unavailable'));
            
        $response = $this->controller->getChargingStationsAvailability(
            $request, 
            $this->evRoutingManager, 
            $this->validator
        );
        
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('Service unavailable', $responseData['error']);
    }
}
