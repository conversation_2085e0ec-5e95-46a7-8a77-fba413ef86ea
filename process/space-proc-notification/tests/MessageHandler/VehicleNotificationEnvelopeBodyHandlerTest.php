<?php

namespace App\Tests\MessageHandler;

use App\Manager\UserManager;
use App\Message\VehicleNotificationEnvelopeBody;
use App\MessageHandler\VehicleNotificationEnvelopeBodyHandler;
use App\Service\SystemFirebaseClient;
use App\Helper\WSResponse;
use App\Service\NotificationService;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\HttpFoundation\Response;

/**
 * Tests for the VehicleNotificationEnvelopeBodyHandler class
 *
 * This test suite covers all scenarios for the VehicleNotificationEnvelopeBodyHandler:
 * - Successful message handling
 * - Validation errors
 * - No users found
 * - Missing notification title
 * - Missing notification body
 */
class VehicleNotificationEnvelopeBodyHandlerTest extends KernelTestCase
{
    private LoggerInterface $logger;
    private ValidatorInterface $validatorMock;
    private UserManager $userManager;
    private ConstraintViolationListInterface $constraintListMock;
    private SystemFirebaseClient $sysFirebaseClient;
    private NotificationService $notificationService;
    private VehicleNotificationEnvelopeBodyHandler $handler;
    private string $userId;
    private string $vin;
    private string $sessionId;
    private string $xApiKey;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->validatorMock = $this->createMock(ValidatorInterface::class);
        $this->constraintListMock = $this->createMock(ConstraintViolationListInterface::class);
        $this->userManager = $this->createMock(UserManager::class);
        $this->sysFirebaseClient = $this->createMock(SystemFirebaseClient::class);
        $this->notificationService = $this->createMock(NotificationService::class);

        $this->handler = new VehicleNotificationEnvelopeBodyHandler(
            $this->validatorMock,
            $this->logger,
            $this->userManager,
            $this->sysFirebaseClient,
            $this->notificationService
        );

        // Common test data
        $this->userId = '66b5eca07bbc4739adc88342';
        $this->vin = '1C4RJFAG0CC';
        $this->sessionId = 'APA91bEjICQ';
        $this->xApiKey = '1PP2A5HMT1B0A4B0';
    }

    /**
     * Test successful message handling
     */
    public function testHandleMessageWithSuccess(): void
    {
        // Mock validator to return no violations
        $this->validatorMock->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock user manager to return users
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"100000000f0b4dce874859657ae00100","push":{"pushToken":["APA91bEjICQ"]}}]}'
        );
        $this->userManager->expects($this->once())
            ->method('getUsersBySessionId')
            ->with($this->userId, $this->vin, $this->sessionId)
            ->willReturn($serviceResult);

        // Mock Firebase client to verify notification is sent
        $this->sysFirebaseClient->expects($this->once())
            ->method('sendNotification');

        // Mock notification service to verify notification is saved
        $this->notificationService->expects($this->once())
            ->method('saveNotification')
            ->with(
                VehicleNotificationEnvelopeBodyHandler::NOTIFICATION_TYPE,
                $this->userId,
                $this->anything(),
                $this->vin
            );

        // Verify logger is used correctly
        $this->logger->expects($this->exactly(2))
            ->method('info');

        $this->logger->expects($this->never())
            ->method('error');

        // Call the handler
        $message = $this->getVehicleNotificationEnvelopeBodyFixture();
        $this->handler->__invoke($message);
    }

    /**
     * Test handling with validation errors
     */
    public function testHandleMessageWithValidationErrors(): void
    {
        // Create a constraint violation
        $violation = $this->createMock(ConstraintViolation::class);
        $violation->method('getPropertyPath')->willReturn('userId');
        $violation->method('getMessage')->willReturn('User ID cannot be empty');

        // Create a constraint violation list with one violation
        $violations = new ConstraintViolationList([$violation]);

        // Mock validator to return violations
        $this->validatorMock->expects($this->once())
            ->method('validate')
            ->willReturn($violations);

        // Verify logger is used correctly
        $this->logger->expects($this->once())
            ->method('info');

        // Call the handler and expect exception
        $message = $this->getVehicleNotificationEnvelopeBodyFixture();
        $this->expectException(UnrecoverableMessageHandlingException::class);
        $this->handler->__invoke($message);
    }

    /**
     * Test handling with no users found
     */
    public function testHandleMessageWithNoUsersFound(): void
    {
        // Mock validator to return no violations
        $this->validatorMock->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock user manager to return empty result
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[]}'
        );
        $this->userManager->expects($this->once())
            ->method('getUsersBySessionId')
            ->with($this->userId, $this->vin, $this->sessionId)
            ->willReturn($serviceResult);

        // Verify logger is used correctly
        $this->logger->expects($this->once())
            ->method('info');

        $this->logger->expects($this->once())
            ->method('error')
            ->with('No users found', ['userId' => $this->userId, 'vin' => $this->vin, 'sessionId' => $this->sessionId]);

        // Call the handler and expect exception
        $message = $this->getVehicleNotificationEnvelopeBodyFixture();
        $this->expectException(UnrecoverableMessageHandlingException::class);
        $this->expectExceptionMessage('No users found');
        $this->handler->__invoke($message);
    }

    /**
     * Test handling with missing notification title
     */
    public function testHandleMessageWithMissingNotificationTitle(): void
    {
        // Mock validator to return no violations
        $this->validatorMock->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock user manager to return users
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"100000000f0b4dce874859657ae00100","push":{"pushToken":["APA91bEjICQ"]}}]}'
        );
        $this->userManager->expects($this->once())
            ->method('getUsersBySessionId')
            ->with($this->userId, $this->vin, $this->sessionId)
            ->willReturn($serviceResult);

        // Verify logger is used correctly
        $this->logger->expects($this->atLeastOnce())
            ->method('info');

        $this->logger->expects($this->once())
            ->method('error')
            ->with('Vehicle Notification error: Title is required and cannot be empty.');

        // Mock Firebase client to verify notification is NOT sent
        $this->sysFirebaseClient->expects($this->never())
            ->method('sendNotification');

        // Mock notification service to verify notification is NOT saved
        $this->notificationService->expects($this->never())
            ->method('saveNotification');

        // Create message with empty title
        $message = $this->getVehicleNotificationEnvelopeBodyFixture(['notification' => ['title' => '', 'body' => 'Vehicle is in India']]);

        // Call the handler
        $this->handler->__invoke($message);
    }

    /**
     * Test handling with missing notification body
     */
    public function testHandleMessageWithMissingNotificationBody(): void
    {
        // Mock validator to return no violations
        $this->validatorMock->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock user manager to return users
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"100000000f0b4dce874859657ae00100","push":{"pushToken":["APA91bEjICQ"]}}]}'
        );
        $this->userManager->expects($this->once())
            ->method('getUsersBySessionId')
            ->with($this->userId, $this->vin, $this->sessionId)
            ->willReturn($serviceResult);

        // Verify logger is used correctly
        $this->logger->expects($this->atLeastOnce())
            ->method('info');

        $this->logger->expects($this->once())
            ->method('error')
            ->with('Vehicle Notification error: Notification body is required and cannot be empty.');

        // Mock Firebase client to verify notification is NOT sent
        $this->sysFirebaseClient->expects($this->never())
            ->method('sendNotification');

        // Mock notification service to verify notification is NOT saved
        $this->notificationService->expects($this->never())
            ->method('saveNotification');

        // Create message with empty body
        $message = $this->getVehicleNotificationEnvelopeBodyFixture(['notification' => ['title' => 'Vehicle location', 'body' => '']]);

        // Call the handler
        $this->handler->__invoke($message);
    }

    /**
     * Test handling with missing notification field
     */
    public function testHandleMessageWithMissingNotificationField(): void
    {
        // Mock validator to return no violations
        $this->validatorMock->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock user manager to return users
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"100000000f0b4dce874859657ae00100","push":{"pushToken":["APA91bEjICQ"]}}]}'
        );
        $this->userManager->expects($this->once())
            ->method('getUsersBySessionId')
            ->with($this->userId, $this->vin, $this->sessionId)
            ->willReturn($serviceResult);

        // Verify logger is used correctly
        $this->logger->expects($this->atLeastOnce())
            ->method('info');

        $this->logger->expects($this->once())
            ->method('error')
            ->with('Vehicle Notification error: Title is required and cannot be empty.');

        // Mock Firebase client to verify notification is NOT sent
        $this->sysFirebaseClient->expects($this->never())
            ->method('sendNotification');

        // Mock notification service to verify notification is NOT saved
        $this->notificationService->expects($this->never())
            ->method('saveNotification');

        // Create message without notification field
        $messageData = [
            'data' => ['serviceData' => ['EventID' => 'GenericNotificationEvent']],
            'criticality' => 'high'
        ];

        $envelope = new VehicleNotificationEnvelopeBody(
            $this->userId,
            $this->vin,
            $this->sessionId,
            $this->xApiKey,
            $messageData
        );

        // Call the handler
        $this->handler->__invoke($envelope);
    }

    /**
     * Helper method to create a VehicleNotificationEnvelopeBody with custom message data
     */
    private function getVehicleNotificationEnvelopeBodyFixture(array $customMessageData = []): VehicleNotificationEnvelopeBody
    {
        $defaultMessage = [
            'notification' => ['title' => 'Vehicle location', 'body' => 'Vehicle is in India'],
            'data' => [
                'serviceData' => [
                    'EventID' => 'GenericNotificationEvent',
                    'Version' => '1.0',
                    'VehicleId' => 'v38uihwnjsfns',
                    'Timestamp' => 1539179391201,
                    'Data' => [
                        'notificationId' => 'S12_RO_START_FAILED_TIMEOUT',
                        'eventName' => 'HORN_AND_LIGHTS',
                        'flatAttrA' => '18.58483868',
                        'flatAttrB' => 'Boundary 9 Oct',
                        'flatAttrC' => '3bca9be2-fb5d-430a-9bb7-4ce6c7f9a474',
                        'flatAttrD' => '73.73466107'
                    ]
                ],
                'context' => [
                    'nickname' => 'MY CAR',
                    'model' => 'AR',
                    'brandMarketingName' => 'ALFA ROMEO',
                    'firstName' => 'John'
                ]
            ],
            'criticality' => 'high',
            'apiPushConfig' => [
                'staticConfig' => [
                    'silent' => true,
                    'inApp' => true,
                    'push' => true,
                    'android' => [
                        'ttl' => 'string',
                        'notification' => ['click_action' => 'string'],
                        'priority' => 'string'
                    ],
                    'apns' => [
                        'headers' => ['apns-priority' => 'string'],
                        'payload' => ['aps' => ['category' => 'string']]
                    ]
                ]
            ]
        ];

        // Merge custom message data with default message
        $message = array_replace_recursive($defaultMessage, $customMessageData);

        $envelope = new VehicleNotificationEnvelopeBody(
            $this->userId,
            $this->vin,
            $this->sessionId,
            $this->xApiKey,
            $message
        );

        return $envelope;
    }
}