<?php

namespace App\Tests\Model;

use App\Model\MongoQueryModel;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;

/**
 * Tests for the MongoQueryModel class
 *
 * This test suite covers all scenarios for the MongoQueryModel:
 * - Constructor and getters/setters for all properties
 * - Method chaining for setters
 * - Serialization with different serialization groups
 */
class MongoQueryModelTest extends KernelTestCase
{
    private Serializer $serializer;
    private string $collection;
    private string $database;
    private string $dataSource;
    private array $filter;
    private array $document;
    private array $update;
    private array $pipeline;
    private array $documents;
    private bool $upsert;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->serializer = new Serializer([new ObjectNormalizer()], [new JsonEncoder()]);

        // Common test data
        $this->collection = 'users';
        $this->database = 'app_db';
        $this->dataSource = 'atlas-cluster';
        $this->filter = ['userId' => '123456'];
        $this->document = ['name' => 'John Doe', 'email' => '<EMAIL>'];
        $this->update = ['$set' => ['name' => 'Jane Doe']];
        $this->pipeline = [['$match' => ['active' => true]], ['$sort' => ['createdAt' => -1]]];
        $this->documents = [
            ['name' => 'John Doe', 'email' => '<EMAIL>'],
            ['name' => 'Jane Doe', 'email' => '<EMAIL>']
        ];
        $this->upsert = true;
    }

    /**
     * Test all getters and setters
     */
    public function testGettersAndSetters(): void
    {
        $model = new MongoQueryModel();

        // Set all properties
        $model->setCollection($this->collection);
        $model->setDatabase($this->database);
        $model->setDataSource($this->dataSource);
        $model->setFilter($this->filter);
        $model->setDocument($this->document);
        $model->setUpdate($this->update);
        $model->setPipeline($this->pipeline);
        $model->setDocuments($this->documents);
        $model->setUpsert($this->upsert);

        // Verify all properties
        $this->assertEquals($this->collection, $model->getCollection());
        $this->assertEquals($this->database, $model->getDatabase());
        $this->assertEquals($this->dataSource, $model->getDataSource());
        $this->assertEquals($this->filter, $model->getFilter());
        $this->assertEquals($this->document, $model->getDocument());
        $this->assertEquals($this->update, $model->getUpdate());
        $this->assertEquals($this->pipeline, $model->getPipeline());
        $this->assertEquals($this->documents, $model->getDocuments());
        $this->assertEquals($this->upsert, $model->getUpsert());
    }

    /**
     * Test method chaining for setters
     */
    public function testMethodChaining(): void
    {
        $model = new MongoQueryModel();

        // Use method chaining
        $result = $model
            ->setCollection($this->collection)
            ->setDatabase($this->database)
            ->setDataSource($this->dataSource)
            ->setFilter($this->filter)
            ->setDocument($this->document)
            ->setUpdate($this->update)
            ->setPipeline($this->pipeline)
            ->setDocuments($this->documents)
            ->setUpsert($this->upsert);

        // Verify that method chaining returns the model instance
        $this->assertSame($model, $result);

        // Verify all properties
        $this->assertEquals($this->collection, $model->getCollection());
        $this->assertEquals($this->database, $model->getDatabase());
        $this->assertEquals($this->dataSource, $model->getDataSource());
        $this->assertEquals($this->filter, $model->getFilter());
        $this->assertEquals($this->document, $model->getDocument());
        $this->assertEquals($this->update, $model->getUpdate());
        $this->assertEquals($this->pipeline, $model->getPipeline());
        $this->assertEquals($this->documents, $model->getDocuments());
        $this->assertEquals($this->upsert, $model->getUpsert());
    }

    /**
     * Test serialization with default group
     */
    public function testSerializationWithDefaultGroup(): void
    {
        $model = new MongoQueryModel();
        $model->setCollection($this->collection);
        $model->setDatabase($this->database);
        $model->setDataSource($this->dataSource);

        // Serialize with default group
        $json = $this->serializer->serialize($model, 'json', ['groups' => ['default']]);
        $data = json_decode($json, true);

        // Verify that only default group properties are included
        $this->assertArrayHasKey('collection', $data);
        $this->assertArrayHasKey('database', $data);
        $this->assertArrayHasKey('dataSource', $data);
        $this->assertArrayNotHasKey('filter', $data);
        $this->assertArrayNotHasKey('document', $data);
        $this->assertArrayNotHasKey('update', $data);
        $this->assertArrayNotHasKey('pipeline', $data);
        $this->assertArrayNotHasKey('documents', $data);
        $this->assertArrayNotHasKey('upsert', $data);

        // Verify values
        $this->assertEquals($this->collection, $data['collection']);
        $this->assertEquals($this->database, $data['database']);
        $this->assertEquals($this->dataSource, $data['dataSource']);
    }

    /**
     * Test serialization with findOne group
     */
    public function testSerializationWithFindOneGroup(): void
    {
        $model = new MongoQueryModel();
        $model->setCollection($this->collection);
        $model->setDatabase($this->database);
        $model->setDataSource($this->dataSource);
        $model->setFilter($this->filter);

        // Serialize with findOne group
        $json = $this->serializer->serialize($model, 'json', ['groups' => ['default', 'findOne']]);
        $data = json_decode($json, true);

        // Verify that default and findOne group properties are included
        $this->assertArrayHasKey('collection', $data);
        $this->assertArrayHasKey('database', $data);
        $this->assertArrayHasKey('dataSource', $data);
        $this->assertArrayHasKey('filter', $data);
        $this->assertArrayNotHasKey('document', $data);
        $this->assertArrayNotHasKey('update', $data);
        $this->assertArrayNotHasKey('pipeline', $data);
        $this->assertArrayNotHasKey('documents', $data);
        $this->assertArrayNotHasKey('upsert', $data);

        // Verify values
        $this->assertEquals($this->collection, $data['collection']);
        $this->assertEquals($this->database, $data['database']);
        $this->assertEquals($this->dataSource, $data['dataSource']);
        $this->assertEquals($this->filter, $data['filter']);
    }

    /**
     * Test serialization with insertOne group
     */
    public function testSerializationWithInsertOneGroup(): void
    {
        $model = new MongoQueryModel();
        $model->setCollection($this->collection);
        $model->setDatabase($this->database);
        $model->setDataSource($this->dataSource);
        $model->setDocument($this->document);

        // Serialize with insertOne group
        $json = $this->serializer->serialize($model, 'json', ['groups' => ['default', 'insertOne']]);
        $data = json_decode($json, true);

        // Verify that default and insertOne group properties are included
        $this->assertArrayHasKey('collection', $data);
        $this->assertArrayHasKey('database', $data);
        $this->assertArrayHasKey('dataSource', $data);
        $this->assertArrayNotHasKey('filter', $data);
        $this->assertArrayHasKey('document', $data);
        $this->assertArrayNotHasKey('update', $data);
        $this->assertArrayNotHasKey('pipeline', $data);
        $this->assertArrayNotHasKey('documents', $data);
        $this->assertArrayNotHasKey('upsert', $data);

        // Verify values
        $this->assertEquals($this->collection, $data['collection']);
        $this->assertEquals($this->database, $data['database']);
        $this->assertEquals($this->dataSource, $data['dataSource']);
        $this->assertEquals($this->document, $data['document']);
    }

    /**
     * Test serialization with updateOne group
     */
    public function testSerializationWithUpdateOneGroup(): void
    {
        $model = new MongoQueryModel();
        $model->setCollection($this->collection);
        $model->setDatabase($this->database);
        $model->setDataSource($this->dataSource);
        $model->setFilter($this->filter);
        $model->setUpdate($this->update);
        $model->setUpsert($this->upsert);

        // Serialize with updateOne group
        $json = $this->serializer->serialize($model, 'json', ['groups' => ['default', 'updateOne']]);
        $data = json_decode($json, true);

        // Verify that default and updateOne group properties are included
        $this->assertArrayHasKey('collection', $data);
        $this->assertArrayHasKey('database', $data);
        $this->assertArrayHasKey('dataSource', $data);
        $this->assertArrayHasKey('filter', $data);
        $this->assertArrayNotHasKey('document', $data);
        $this->assertArrayHasKey('update', $data);
        $this->assertArrayNotHasKey('pipeline', $data);
        $this->assertArrayNotHasKey('documents', $data);
        $this->assertArrayHasKey('upsert', $data);

        // Verify values
        $this->assertEquals($this->collection, $data['collection']);
        $this->assertEquals($this->database, $data['database']);
        $this->assertEquals($this->dataSource, $data['dataSource']);
        $this->assertEquals($this->filter, $data['filter']);
        $this->assertEquals($this->update, $data['update']);
        $this->assertEquals($this->upsert, $data['upsert']);
    }

    /**
     * Test serialization with aggregate group
     */
    public function testSerializationWithAggregateGroup(): void
    {
        $model = new MongoQueryModel();
        $model->setCollection($this->collection);
        $model->setDatabase($this->database);
        $model->setDataSource($this->dataSource);
        $model->setPipeline($this->pipeline);

        // Serialize with aggregate group
        $json = $this->serializer->serialize($model, 'json', ['groups' => ['default', 'aggregate']]);
        $data = json_decode($json, true);

        // Verify that default and aggregate group properties are included
        $this->assertArrayHasKey('collection', $data);
        $this->assertArrayHasKey('database', $data);
        $this->assertArrayHasKey('dataSource', $data);
        $this->assertArrayNotHasKey('filter', $data);
        $this->assertArrayNotHasKey('document', $data);
        $this->assertArrayNotHasKey('update', $data);
        $this->assertArrayHasKey('pipeline', $data);
        $this->assertArrayNotHasKey('documents', $data);
        $this->assertArrayNotHasKey('upsert', $data);

        // Verify values
        $this->assertEquals($this->collection, $data['collection']);
        $this->assertEquals($this->database, $data['database']);
        $this->assertEquals($this->dataSource, $data['dataSource']);
        $this->assertEquals($this->pipeline, $data['pipeline']);
    }

    /**
     * Test serialization with insertMany group
     */
    public function testSerializationWithInsertManyGroup(): void
    {
        $model = new MongoQueryModel();
        $model->setCollection($this->collection);
        $model->setDatabase($this->database);
        $model->setDataSource($this->dataSource);
        $model->setDocuments($this->documents);

        // Serialize with insertMany group
        $json = $this->serializer->serialize($model, 'json', ['groups' => ['default', 'insertMany']]);
        $data = json_decode($json, true);

        // Verify that default and insertMany group properties are included
        $this->assertArrayHasKey('collection', $data);
        $this->assertArrayHasKey('database', $data);
        $this->assertArrayHasKey('dataSource', $data);
        $this->assertArrayNotHasKey('filter', $data);
        $this->assertArrayNotHasKey('document', $data);
        $this->assertArrayNotHasKey('update', $data);
        $this->assertArrayNotHasKey('pipeline', $data);
        $this->assertArrayHasKey('documents', $data);
        $this->assertArrayNotHasKey('upsert', $data);

        // Verify values
        $this->assertEquals($this->collection, $data['collection']);
        $this->assertEquals($this->database, $data['database']);
        $this->assertEquals($this->dataSource, $data['dataSource']);
        $this->assertEquals($this->documents, $data['documents']);
    }
}
