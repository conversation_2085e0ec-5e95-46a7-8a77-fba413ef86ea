<?php

namespace App\Tests\Model;

use App\Model\FcmTokenRegisterModel;
use App\Helper\BrandProvider;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Tests for the FcmTokenRegisterModel class
 *
 * This test suite covers all scenarios for the FcmTokenRegisterModel:
 * - Constructor and getters
 * - Setters
 * - Validation constraints
 */
class FcmTokenRegisterModelTest extends KernelTestCase
{
    private ValidatorInterface $validator;
    private string $deviceId;
    private string $brand;
    private string $country;
    private string $pushToken;
    private string $appId;
    private int $timestamp;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        self::bootKernel();
        $this->validator = self::getContainer()->get('validator');

        // Common test data
        $this->deviceId = 'device123';
        $this->brand = 'AR'; // Alfa Romeo from the brands list
        $this->country = 'IT';
        $this->pushToken = 'fcm-token-123456';
        $this->appId = 'com.example.app';
        $this->timestamp = time();
    }

    /**
     * Test constructor and getters
     */
    public function testConstructorAndGetters(): void
    {
        $model = new FcmTokenRegisterModel(
            $this->deviceId,
            $this->brand,
            $this->country,
            $this->pushToken,
            $this->appId,
            $this->timestamp
        );

        // Test getters
        $this->assertEquals($this->deviceId, $model->getDeviceId());
        $this->assertEquals($this->brand, $model->getBrand());
        $this->assertEquals($this->country, $model->getCountry());
        $this->assertEquals($this->pushToken, $model->getPushToken());
        $this->assertEquals($this->appId, $model->getAppId());
        $this->assertEquals($this->timestamp, $model->getTimestamp());
    }

    /**
     * Test setters
     */
    public function testSetters(): void
    {
        $model = new FcmTokenRegisterModel(
            $this->deviceId,
            $this->brand,
            $this->country,
            $this->pushToken,
            $this->appId,
            $this->timestamp
        );

        // New values
        $newDeviceId = 'new-device-456';
        $newBrand = 'DS';
        $newCountry = 'FR';
        $newPushToken = 'new-fcm-token-789';
        $newAppId = 'com.example.newapp';
        $newTimestamp = $this->timestamp + 3600;

        // Set new values
        $model->setDeviceID($newDeviceId);
        $model->setBrand($newBrand);
        $model->setCountry($newCountry);
        $model->setPushToken($newPushToken);
        $model->setAppId($newAppId);
        $model->setTimestamp($newTimestamp);

        // Verify new values
        $this->assertEquals($newDeviceId, $model->getDeviceId());
        $this->assertEquals($newBrand, $model->getBrand());
        $this->assertEquals($newCountry, $model->getCountry());
        $this->assertEquals($newPushToken, $model->getPushToken());
        $this->assertEquals($newAppId, $model->getAppId());
        $this->assertEquals($newTimestamp, $model->getTimestamp());
    }

    /**
     * Test validation with valid data
     */
    public function testValidationWithValidData(): void
    {
        $model = new FcmTokenRegisterModel(
            $this->deviceId,
            $this->brand,
            $this->country,
            $this->pushToken,
            $this->appId,
            $this->timestamp
        );

        $violations = $this->validator->validate($model, null, ['fcmTokenRegister']);

        $this->assertCount(0, $violations);
    }

    /**
     * Test validation with empty deviceId
     */
    public function testValidationWithEmptyDeviceId(): void
    {
        $model = new FcmTokenRegisterModel(
            '',
            $this->brand,
            $this->country,
            $this->pushToken,
            $this->appId,
            $this->timestamp
        );

        $violations = $this->validator->validate($model, null, ['fcmTokenRegister']);

        $this->assertGreaterThanOrEqual(1, $violations->count());

        // Find the violation for deviceId
        $deviceIdViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'deviceId') {
                $deviceIdViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($deviceIdViolation, "Should have a violation for deviceId");
        $this->assertEquals('This value should not be blank.', $deviceIdViolation->getMessage());
    }

    /**
     * Test validation with empty brand
     */
    public function testValidationWithEmptyBrand(): void
    {
        $model = new FcmTokenRegisterModel(
            $this->deviceId,
            '',
            $this->country,
            $this->pushToken,
            $this->appId,
            $this->timestamp
        );

        $violations = $this->validator->validate($model, null, ['fcmTokenRegister']);

        $this->assertGreaterThanOrEqual(1, $violations->count());

        // Find the violation for brand
        $brandViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'brand') {
                $brandViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($brandViolation, "Should have a violation for brand");
        $this->assertEquals('This value should not be blank.', $brandViolation->getMessage());
    }

    /**
     * Test validation with invalid brand
     */
    public function testValidationWithInvalidBrand(): void
    {
        $model = new FcmTokenRegisterModel(
            $this->deviceId,
            'INVALID_BRAND',
            $this->country,
            $this->pushToken,
            $this->appId,
            $this->timestamp
        );

        $violations = $this->validator->validate($model, null, ['fcmTokenRegister']);

        $this->assertGreaterThanOrEqual(1, $violations->count());

        // Find the violation for brand
        $brandViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'brand') {
                $brandViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($brandViolation, "Should have a violation for brand");
        $this->assertEquals('Choose a valid brand.', $brandViolation->getMessage());
    }

    /**
     * Test validation with empty country
     */
    public function testValidationWithEmptyCountry(): void
    {
        $model = new FcmTokenRegisterModel(
            $this->deviceId,
            $this->brand,
            '',
            $this->pushToken,
            $this->appId,
            $this->timestamp
        );

        $violations = $this->validator->validate($model, null, ['fcmTokenRegister']);

        $this->assertGreaterThanOrEqual(1, $violations->count());

        // Find the violation for country
        $countryViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'country') {
                $countryViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($countryViolation, "Should have a violation for country");
        $this->assertEquals('This value should not be blank.', $countryViolation->getMessage());
    }

    /**
     * Test validation with empty pushToken
     */
    public function testValidationWithEmptyPushToken(): void
    {
        $model = new FcmTokenRegisterModel(
            $this->deviceId,
            $this->brand,
            $this->country,
            '',
            $this->appId,
            $this->timestamp
        );

        $violations = $this->validator->validate($model, null, ['fcmTokenRegister']);

        $this->assertGreaterThanOrEqual(1, $violations->count());

        // Find the violation for pushToken
        $pushTokenViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'pushToken') {
                $pushTokenViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($pushTokenViolation, "Should have a violation for pushToken");
        $this->assertEquals('This value should not be blank.', $pushTokenViolation->getMessage());
    }

    /**
     * Test validation with empty appId
     */
    public function testValidationWithEmptyAppId(): void
    {
        $model = new FcmTokenRegisterModel(
            $this->deviceId,
            $this->brand,
            $this->country,
            $this->pushToken,
            '',
            $this->timestamp
        );

        $violations = $this->validator->validate($model, null, ['fcmTokenRegister']);

        $this->assertGreaterThanOrEqual(1, $violations->count());

        // Find the violation for appId
        $appIdViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'appId') {
                $appIdViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($appIdViolation, "Should have a violation for appId");
        $this->assertEquals('This value should not be blank.', $appIdViolation->getMessage());
    }

    /**
     * Test validation with empty timestamp
     */
    public function testValidationWithEmptyTimestamp(): void
    {
        // We can't pass an empty int, so we'll use 0 as a proxy for "empty"
        $model = new FcmTokenRegisterModel(
            $this->deviceId,
            $this->brand,
            $this->country,
            $this->pushToken,
            $this->appId,
            0
        );

        $violations = $this->validator->validate($model, null, ['timestamp']);

        $this->assertCount(0, $violations, "Timestamp 0 should be valid for the timestamp group");
    }

    /**
     * Test validation with multiple empty fields
     */
    public function testValidationWithMultipleEmptyFields(): void
    {
        $model = new FcmTokenRegisterModel(
            '',
            '',
            '',
            '',
            '',
            $this->timestamp
        );

        $violations = $this->validator->validate($model, null, ['fcmTokenRegister']);

        // We expect at least 5 violations (one for each empty field)
        $this->assertGreaterThanOrEqual(5, $violations->count());

        // Check that we have violations for each field
        $fieldViolations = [
            'deviceId' => false,
            'brand' => false,
            'country' => false,
            'pushToken' => false,
            'appId' => false
        ];

        foreach ($violations as $violation) {
            if (array_key_exists($violation->getPropertyPath(), $fieldViolations)) {
                $fieldViolations[$violation->getPropertyPath()] = true;
            }
        }

        // Verify that we have at least one violation for each field
        foreach ($fieldViolations as $field => $hasViolation) {
            $this->assertTrue($hasViolation, "Should have a violation for $field");
        }
    }
}
