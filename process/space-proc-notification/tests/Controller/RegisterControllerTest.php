<?php

namespace App\Tests\Controller;

use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\DependencyInjection\Container;

use App\Controller\RegisterController;
use App\Helper\SuccessResponse;
use App\Manager\RegisterManager;
use App\Helper\ErrorResponse;
use App\Model\FcmTokenRegisterModel;


class RegisterControllerTest extends KernelTestCase
{
    private $validator;
    private $registerManager;
    private $controller;
    private $container;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        // Create mocks instead of using the real services
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->registerManager = $this->createMock(RegisterManager::class);

        // Create a new controller
        $this->controller = new RegisterController();

        // Create a container and set it on the controller
        $this->container = new Container();
        $this->container->set('json', new class() {
            public function encode($data): string
            {
                return json_encode($data);
            }
        });
        $this->controller->setContainer($this->container);
    }

    protected function tearDown(): void
    {
        unset($this->validator);
        unset($this->registerManager);
        unset($this->controller);
        unset($this->container);
    }

    /**
     * Test registering FCM token with missing userId in header
     */
    public function testRegisterFcmTokenWithMissingUserId(): void
    {
        $content = json_encode([
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'AP', // Valid brand from space_params.yaml
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        // Create a request with no userId in header
        $request = Request::create('/v1/notification/enrollment', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag([]);

        // Mock validator to return validation errors for userId
        $violation = $this->createMock(ConstraintViolation::class);
        $violation->method('getPropertyPath')->willReturn('userId');
        $violation->method('getMessage')->willReturn('This value should not be blank.');

        $violationList = new ConstraintViolationList([$violation]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        // Call the controller method
        $response = $this->controller->registerFcmToken($request, $this->validator, $this->registerManager);

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('validation_failed', $responseData['error']['message']);
        $this->assertEquals('This value should not be blank.', $responseData['error']['errors']['userId']);
    }

    /**
     * Test registering FCM token with missing required fields in request body
     */
    public function testRegisterFcmTokenWithMissingFields(): void
    {
        // Missing required fields in the request body
        $content = json_encode([
            'deviceID' => '', // Empty deviceID
            'country' => 'India',
            // Missing brand and pushToken
            'appId' => 'spaceapp',
        ]);

        // Create a request with userId in header
        $request = Request::create('/v1/notification/enrollment', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);

        // Mock validator to return no validation errors for userId
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock registerManager to return validation error
        $this->registerManager->expects($this->once())
            ->method('registerFcmToken')
            ->willReturn(new ErrorResponse([
                'deviceId' => 'This value should not be blank.',
                'brand' => 'This value should not be blank.',
                'pushToken' => 'This value should not be blank.',
            ], Response::HTTP_UNPROCESSABLE_ENTITY));

        // Call the controller method
        $response = $this->controller->registerFcmToken($request, $this->validator, $this->registerManager);

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    /**
     * Test registering FCM token with invalid brand
     */
    public function testRegisterFcmTokenWithInvalidBrand(): void
    {
        $content = json_encode([
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'InvalidBrand', // Invalid brand not in space_params.yaml
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        // Create a request with userId in header
        $request = Request::create('/v1/notification/enrollment', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);

        // Mock validator to return no validation errors for userId
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock registerManager to return validation error for brand
        $this->registerManager->expects($this->once())
            ->method('registerFcmToken')
            ->willReturn(new ErrorResponse([
                'brand' => 'Choose a valid brand.',
            ], Response::HTTP_UNPROCESSABLE_ENTITY));

        // Call the controller method
        $response = $this->controller->registerFcmToken($request, $this->validator, $this->registerManager);

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    /**
     * Test registering FCM token with valid data
     */
    public function testRegisterFcmTokenWithValidData(): void
    {
        $content = json_encode([
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'AP', // Valid brand from space_params.yaml
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        // Create a request with userId in header
        $request = Request::create('/v1/notification/enrollment', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);

        // Mock validator to return no validation errors for userId
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock registerManager to return success
        $this->registerManager->expects($this->once())
            ->method('registerFcmToken')
            ->willReturn(new SuccessResponse(['message' => 'fcm token registered successfully'], Response::HTTP_OK));

        // Call the controller method
        $response = $this->controller->registerFcmToken($request, $this->validator, $this->registerManager);

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertEquals('fcm token registered successfully', $responseData['success']['message']);
    }

    /**
     * Test registering FCM token with user not found
     */
    public function testRegisterFcmTokenWithUserNotFound(): void
    {
        $content = json_encode([
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'AP',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        // Create a request with userId in header
        $request = Request::create('/v1/notification/enrollment', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => 'non-existent-user-id']);

        // Mock validator to return no validation errors for userId
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock registerManager to return user not found error
        $this->registerManager->expects($this->once())
            ->method('registerFcmToken')
            ->willReturn(new ErrorResponse('User not found', Response::HTTP_NOT_FOUND));

        // Call the controller method
        $response = $this->controller->registerFcmToken($request, $this->validator, $this->registerManager);

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('User not found', $responseData['error']['message']);
    }

    /**
     * Test registering FCM token with exception thrown
     */
    public function testRegisterFcmTokenWithException(): void
    {
        $content = json_encode([
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'AP',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        // Create a request with userId in header
        $request = Request::create('/v1/notification/enrollment', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);

        // Mock validator to throw exception
        $this->validator->expects($this->once())
            ->method('validate')
            ->willThrowException(new \Exception('Unexpected error occurred'));

        // Call the controller method
        $response = $this->controller->registerFcmToken($request, $this->validator, $this->registerManager);

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('Unexpected error occurred', $responseData['error']['message']);
    }

    /**
     * Test registering remote session with missing userId in header
     */
    public function testRegisterRemoteSessionWithMissingUserId(): void
    {
        $content = json_encode([
            'remoteSessionId' => '2C8EA9CE5BD1FA1',
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'AP',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        // Create a request with no userId in header
        $request = Request::create('/v1/remote/session', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag([]);

        // Mock validator to return validation errors for userId
        $violation = $this->createMock(ConstraintViolation::class);
        $violation->method('getPropertyPath')->willReturn('userId');
        $violation->method('getMessage')->willReturn('This value should not be blank.');

        $violationList = new ConstraintViolationList([$violation]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        // Call the controller method
        $response = $this->controller->registerRemoteSession($request, $this->validator, $this->registerManager);

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('validation_failed', $responseData['error']['message']);
        $this->assertEquals('This value should not be blank.', $responseData['error']['errors']['userId']);
    }

    /**
     * Test registering remote session with missing remoteSessionId
     */
    public function testRegisterRemoteSessionWithMissingSessionId(): void
    {
        $content = json_encode([
            // Missing remoteSessionId
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'AP',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        // Create a request with userId in header
        $request = Request::create('/v1/remote/session', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);

        // Mock validator to return validation errors for remoteSessionId
        $violation = $this->createMock(ConstraintViolation::class);
        $violation->method('getPropertyPath')->willReturn('remoteSessionId');
        $violation->method('getMessage')->willReturn('This value should not be blank.');

        $violationList = new ConstraintViolationList([$violation]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        // Call the controller method
        $response = $this->controller->registerRemoteSession($request, $this->validator, $this->registerManager);

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('validation_failed', $responseData['error']['message']);
        $this->assertEquals('This value should not be blank.', $responseData['error']['errors']['remoteSessionId']);
    }

    /**
     * Test registering remote session with valid data
     */
    public function testRegisterRemoteSessionWithValidData(): void
    {
        $content = json_encode([
            'remoteSessionId' => '2C8EA9CE5BD1FA1',
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'AP',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        // Create a request with userId in header
        $request = Request::create('/v1/remote/session', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);

        // Mock validator to return no validation errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock registerManager to return success
        $this->registerManager->expects($this->once())
            ->method('registerSessionId')
            ->willReturn(new SuccessResponse(['message' => 'sessionId registered successfully'], Response::HTTP_OK));

        // Call the controller method
        $response = $this->controller->registerRemoteSession($request, $this->validator, $this->registerManager);

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertEquals('sessionId registered successfully', $responseData['success']['message']);
    }

    /**
     * Test registering remote session with exception thrown
     */
    public function testRegisterRemoteSessionWithException(): void
    {
        $content = json_encode([
            'remoteSessionId' => '2C8EA9CE5BD1FA1',
            'deviceID' => 'F0CE5BD1-DFA1-458A-8449-1832C8EA9047',
            'brand' => 'AP',
            'country' => 'India',
            'pushToken' => 'AIzaSyDzpKEgI5Lcri5Zf5Wg5lRz',
            'appId' => 'spaceapp',
        ]);

        // Create a request with usecreaterId in header
        $request = Request::create('/v1/remote/session', 'POST', [], [], [], [], $content);
        $request->headers = new HeaderBag(['userId' => '6736320116974042a60293d16xasd2y2823']);

        // Mock validator to throw exception
        $this->validator->expects($this->once())
            ->method('validate')
            ->willThrowException(new \Exception('Database connection error'));

        // Call the controller method
        $response = $this->controller->registerRemoteSession($request, $this->validator, $this->registerManager);

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('Database connection error', $responseData['error']['message']);
    }
}