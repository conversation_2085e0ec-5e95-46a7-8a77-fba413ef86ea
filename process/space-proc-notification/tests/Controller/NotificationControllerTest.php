<?php

namespace App\Tests\Controller;

use App\Controller\NotificationController;
use App\Manager\NotificationManager;
use PHPUnit\Framework\TestCase;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Tests for the NotificationController class
 *
 * This test suite covers all endpoints and scenarios for the NotificationController:
 * - vehicleNotifications endpoint (GET /v1/accounts/{userid}/vehicles/{vin}/notifications)
 * - userVehicleNotifications endpoint (GET /v2/accounts/{userid}/notifications)
 *
 * For each endpoint, we test:
 * - Success scenarios with different query parameters
 * - Validation error scenarios
 * - Exception handling
 */
class NotificationControllerTest extends TestCase
{
    private NotificationManager $notificationManager;
    private ValidatorInterface $validator;
    private NotificationController $controller;
    private Container $container;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->notificationManager = $this->createMock(NotificationManager::class);
        $this->validator = $this->createMock(ValidatorInterface::class);

        // Create a new controller
        $this->controller = new NotificationController();

        // Create a container and set it on the controller
        $this->container = new Container();
        $this->container->set('json', new class() {
            public function encode($data): string
            {
                return json_encode($data);
            }
        });
        $this->controller->setContainer($this->container);
    }

    protected function tearDown(): void
    {
        unset($this->notificationManager);
        unset($this->validator);
        unset($this->controller);
        unset($this->container);
    }

    /**
     * Test successful vehicle notifications retrieval with all query parameters
     */
    public function testVehicleNotificationsSuccessWithAllParams(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';
        $request = new Request();
        $request->query->set('since', '1549457649000');
        $request->query->set('till', '1649457649000');
        $request->query->set('limit', '20');
        $request->query->set('offset', '10');
        $request->query->set('includeLocalNotif', '1');

        // Expected response data structure based on OpenAPI documentation
        $responseData = [
            'userId' => $userId,
            'startTS' => 1549457649000,
            'endTS' => 1649457649000,
            'notifications' => [
                'totalResults' => 6,
                'offset' => 10,
                'count' => 1,
                'vin' => $vin,
                'items' => [
                    [
                        'id' => '9954eab6-c81f-481c-8597-ae80809bffdd',
                        'notification' => [
                            'context' => [
                                'brandMarketingName' => 'FORD FIGO',
                                'firstName' => 'Tilak',
                                'model' => 'FIAT',
                                'nickname' => 'QA_EMEA_TS_VIN'
                            ],
                            'in-app' => [
                                'criticality' => 'LOW',
                                'body' => 'IMI_S12_BODY_15',
                                'subtitle' => 'IMI_S12_SUB_15',
                                'title' => 'IMI_S12_TITLE_15'
                            ],
                            'data' => [
                                'notificationId' => 'S12_RO_START_FAILED_TIMEOUT',
                                'userId' => '100000000f0b4dce874859657ae00100'
                            ],
                            'click_action' => 'OPEN_ACTIVITY_1'
                        ],
                        'eventName' => 'HORN_AND_LIGHTS',
                        'timestamp' => 1549457649131,
                        'vin' => $vin
                    ]
                ]
            ]
        ];

        // Mock validator to return no errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager response
        $this->notificationManager->expects($this->once())
            ->method('getVehicleNotifications')
            ->with(
                $userId,
                $this->callback(function($data) {
                    return $data['since'] === '1549457649000' &&
                           $data['till'] === '1649457649000' &&
                           $data['limit'] === '20' &&
                           $data['offset'] === '10' &&
                           $data['includeLocalNotif'] === '1';
                }),
                $vin
            )
            ->willReturn(['content' => $responseData, 'code' => Response::HTTP_OK]);

        $response = $this->controller->vehicleNotifications($userId, $vin, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertEquals(json_encode($responseData), $response->getContent());
    }

    /**
     * Test successful vehicle notifications retrieval with default parameters
     */
    public function testVehicleNotificationsSuccessWithDefaultParams(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';
        $request = new Request();

        // Mock validator to return no errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager response with expected default parameters
        $this->notificationManager->expects($this->once())
            ->method('getVehicleNotifications')
            ->with(
                $userId,
                $this->callback(function($data) {
                    return $data['since'] === null &&
                           $data['till'] === null &&
                           $data['limit'] === null &&
                           $data['offset'] === null &&
                           $data['includeLocalNotif'] === 0;
                }),
                $vin
            )
            ->willReturn(['content' => ['success' => true], 'code' => Response::HTTP_OK]);

        $response = $this->controller->vehicleNotifications($userId, $vin, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertEquals('{"success":true}', $response->getContent());
    }

    /**
     * Test validation error for missing userId in vehicle notifications
     */
    public function testVehicleNotificationsValidationErrorForUserId(): void
    {
        $userId = '';
        $vin = 'test-vin';
        $request = new Request();

        // Create a validation error for userId
        $violation = $this->createMock(ConstraintViolation::class);
        $violation->method('getPropertyPath')->willReturn('userid');
        $violation->method('getMessage')->willReturn('This value should not be blank.');

        $violationList = new ConstraintViolationList([$violation]);

        // Mock validator to return errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        $response = $this->controller->vehicleNotifications($userId, $vin, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('validation_failed', $responseData['error']['message']);
        $this->assertEquals('This value should not be blank.', $responseData['error']['errors']['userid']);
    }

    /**
     * Test validation error for missing VIN in vehicle notifications
     */
    public function testVehicleNotificationsValidationErrorForVin(): void
    {
        $userId = 'test-user-id';
        $vin = '';
        $request = new Request();

        // Create a validation error for VIN
        $violation = $this->createMock(ConstraintViolation::class);
        $violation->method('getPropertyPath')->willReturn('vin');
        $violation->method('getMessage')->willReturn('This value should not be blank.');

        $violationList = new ConstraintViolationList([$violation]);

        // Mock validator to return errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        $response = $this->controller->vehicleNotifications($userId, $vin, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('validation_failed', $responseData['error']['message']);
        $this->assertEquals('This value should not be blank.', $responseData['error']['errors']['vin']);
    }

    /**
     * Test validation error for multiple missing fields in vehicle notifications
     */
    public function testVehicleNotificationsValidationErrorForMultipleFields(): void
    {
        $userId = '';
        $vin = '';
        $request = new Request();

        // Create validation errors for both userId and VIN
        $violationUserId = $this->createMock(ConstraintViolation::class);
        $violationUserId->method('getPropertyPath')->willReturn('userid');
        $violationUserId->method('getMessage')->willReturn('This value should not be blank.');

        $violationVin = $this->createMock(ConstraintViolation::class);
        $violationVin->method('getPropertyPath')->willReturn('vin');
        $violationVin->method('getMessage')->willReturn('This value should not be blank.');

        $violationList = new ConstraintViolationList([$violationUserId, $violationVin]);

        // Mock validator to return errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        $response = $this->controller->vehicleNotifications($userId, $vin, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('validation_failed', $responseData['error']['message']);
        $this->assertEquals('This value should not be blank.', $responseData['error']['errors']['userid']);
        $this->assertEquals('This value should not be blank.', $responseData['error']['errors']['vin']);
    }

    /**
     * Test exception handling in vehicle notifications
     */
    public function testVehicleNotificationsException(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';
        $request = new Request();

        // Mock validator to return no errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager to throw exception
        $this->notificationManager->expects($this->once())
            ->method('getVehicleNotifications')
            ->willThrowException(new \Exception('Test exception'));

        $response = $this->controller->vehicleNotifications($userId, $vin, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Test exception', $responseData['error']['message']);
    }

    /**
     * Test successful user vehicle notifications retrieval with all query parameters
     */
    public function testUserVehicleNotificationsSuccessWithAllParams(): void
    {
        $userId = 'test-user-id';
        $request = new Request();
        $request->query->set('since', '1539179391000');
        $request->query->set('till', '1639179391000');
        $request->query->set('limit', '20');
        $request->query->set('offset', '10');
        $request->query->set('includeLocalNotif', '1');

        // Expected response data structure based on OpenAPI documentation
        $responseData = [
            'userId' => $userId,
            'startTS' => 1539179391000,
            'endTS' => 1639179391000,
            'notifications' => [
                [
                    'count' => 2,
                    'items' => [
                        [
                            'id' => 'c2454d1e-0799-4efc-b0f4-c5bfbe820e87',
                            'notification' => [
                                'context' => [
                                    'brandMarketingName' => 'ALFA ROMEO',
                                    'firstName' => 'John',
                                    'model' => 'AR',
                                    'nickname' => 'MY CAR'
                                ],
                                'in-app' => [
                                    'criticality' => 'HIGH',
                                    'body' => 'VEHICLE',
                                    'subtitle' => 'VEHICLE',
                                    'title' => 'TEST NEW STRUCTURE'
                                ],
                                'data' => [
                                    'notificationId' => 'S12_RO_START_FAILED_TIMEOUT',
                                    'userId' => '100000000f0b4dce874859657ae00100'
                                ],
                                'click_action' => 'OPEN_ACTIVITY_1'
                            ],
                            'eventName' => 'HORN_AND_LIGHTS',
                            'timestamp' => 1539179391201,
                            'vin' => 'VR3UPHNKSKT101603ZZ'
                        ]
                    ],
                    'vin' => 'VR3UPHNKSKT101603ZZ'
                ]
            ]
        ];

        // Mock validator to return no errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager response
        $this->notificationManager->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->with(
                $userId,
                $this->callback(function($data) {
                    return $data['since'] === '1539179391000' &&
                           $data['till'] === '1639179391000' &&
                           $data['limit'] === '20' &&
                           $data['offset'] === '10' &&
                           $data['includeLocalNotif'] === '1';
                })
            )
            ->willReturn(['content' => $responseData, 'code' => Response::HTTP_OK]);

        $response = $this->controller->userVehicleNotifications($userId, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertEquals(json_encode($responseData), $response->getContent());
    }

    /**
     * Test successful user vehicle notifications retrieval with default parameters
     */
    public function testUserVehicleNotificationsSuccessWithDefaultParams(): void
    {
        $userId = 'test-user-id';
        $request = new Request();

        // Mock validator to return no errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager response with expected default parameters
        $this->notificationManager->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->with(
                $userId,
                $this->callback(function($data) {
                    return $data['since'] === null &&
                           $data['till'] === null &&
                           $data['limit'] === null &&
                           $data['offset'] === null &&
                           $data['includeLocalNotif'] === 0;
                })
            )
            ->willReturn(['content' => ['success' => true], 'code' => Response::HTTP_OK]);

        $response = $this->controller->userVehicleNotifications($userId, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertEquals('{"success":true}', $response->getContent());
    }

    /**
     * Test validation error for missing userId in user vehicle notifications
     */
    public function testUserVehicleNotificationsValidationError(): void
    {
        $userId = '';
        $request = new Request();

        // Create a validation error
        $violation = $this->createMock(ConstraintViolation::class);
        $violation->method('getPropertyPath')->willReturn('userid');
        $violation->method('getMessage')->willReturn('This value should not be blank.');

        $violationList = new ConstraintViolationList([$violation]);

        // Mock validator to return errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        $response = $this->controller->userVehicleNotifications($userId, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('validation_failed', $responseData['error']['message']);
        $this->assertEquals('This value should not be blank.', $responseData['error']['errors']['userid']);
    }

    /**
     * Test exception handling in user vehicle notifications
     */
    public function testUserVehicleNotificationsException(): void
    {
        $userId = 'test-user-id';
        $request = new Request();

        // Mock validator to return no errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager to throw exception
        $this->notificationManager->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->willThrowException(new \Exception('Test exception'));

        $response = $this->controller->userVehicleNotifications($userId, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Test exception', $responseData['error']['message']);
    }

    /**
     * Test bad request response from notification manager for vehicle notifications
     */
    public function testVehicleNotificationsBadRequestFromManager(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';
        $request = new Request();

        // Mock validator to return no errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager to return a bad request response
        $this->notificationManager->expects($this->once())
            ->method('getVehicleNotifications')
            ->willReturn([
                'content' => ['error' => ['message' => 'No users found']],
                'code' => Response::HTTP_BAD_REQUEST
            ]);

        $response = $this->controller->vehicleNotifications($userId, $vin, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('No users found', $responseData['error']['message']);
    }

    /**
     * Test bad request response from notification manager for user vehicle notifications
     */
    public function testUserVehicleNotificationsBadRequestFromManager(): void
    {
        $userId = 'test-user-id';
        $request = new Request();

        // Mock validator to return no errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager to return a bad request response
        $this->notificationManager->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->willReturn([
                'content' => ['error' => ['message' => 'No users found']],
                'code' => Response::HTTP_BAD_REQUEST
            ]);

        $response = $this->controller->userVehicleNotifications($userId, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('No users found', $responseData['error']['message']);
    }

    /**
     * Test vehicle notifications with invalid date format for 'since' parameter
     */
    public function testVehicleNotificationsWithInvalidSinceFormat(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';
        $request = new Request();
        $request->query->set('since', 'invalid-date-format');

        // Mock validator to return no errors for userId and vin
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager to return a bad request response due to invalid date
        $this->notificationManager->expects($this->once())
            ->method('getVehicleNotifications')
            ->willReturn([
                'content' => ['error' => ['message' => 'Invalid date format for since parameter']],
                'code' => Response::HTTP_BAD_REQUEST
            ]);

        $response = $this->controller->vehicleNotifications($userId, $vin, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Invalid date format for since parameter', $responseData['error']['message']);
    }

    /**
     * Test user vehicle notifications with invalid includeLocalNotif parameter
     */
    public function testUserVehicleNotificationsWithInvalidIncludeLocalNotif(): void
    {
        $userId = 'test-user-id';
        $request = new Request();
        $request->query->set('includeLocalNotif', 'invalid-value'); // Should be 0 or 1

        // Mock validator to return no errors for userId
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager to return a bad request response due to invalid parameter
        $this->notificationManager->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->willReturn([
                'content' => ['error' => ['message' => 'Invalid includeLocalNotif parameter, must be 0 or 1']],
                'code' => Response::HTTP_BAD_REQUEST
            ]);

        $response = $this->controller->userVehicleNotifications($userId, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Invalid includeLocalNotif parameter, must be 0 or 1', $responseData['error']['message']);
    }

    /**
     * Test vehicle notifications with negative limit parameter
     */
    public function testVehicleNotificationsWithNegativeLimit(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';
        $request = new Request();
        $request->query->set('limit', '-10'); // Negative limit is invalid

        // Mock validator to return no errors for userId and vin
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock notification manager to return a bad request response due to invalid limit
        $this->notificationManager->expects($this->once())
            ->method('getVehicleNotifications')
            ->willReturn([
                'content' => ['error' => ['message' => 'Limit parameter must be a positive integer']],
                'code' => Response::HTTP_BAD_REQUEST
            ]);

        $response = $this->controller->vehicleNotifications($userId, $vin, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Limit parameter must be a positive integer', $responseData['error']['message']);
    }

    /**
     * Test vehicle notifications with empty response (no notifications found)
     */
    public function testVehicleNotificationsWithEmptyResponse(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';
        $request = new Request();

        // Mock validator to return no errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Empty response structure based on the API documentation
        $emptyResponse = [
            'userId' => $userId,
            'startTS' => 0,
            'endTS' => 0,
            'notifications' => [
                'totalResults' => 0,
                'offset' => 0,
                'count' => 0,
                'vin' => $vin,
                'items' => []
            ]
        ];

        // Mock notification manager to return an empty response
        $this->notificationManager->expects($this->once())
            ->method('getVehicleNotifications')
            ->willReturn([
                'content' => $emptyResponse,
                'code' => Response::HTTP_OK
            ]);

        $response = $this->controller->vehicleNotifications($userId, $vin, $request, $this->notificationManager, $this->validator);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals($userId, $responseData['userId']);
        $this->assertEquals(0, $responseData['notifications']['totalResults']);
        $this->assertEquals(0, $responseData['notifications']['count']);
        $this->assertEmpty($responseData['notifications']['items']);
    }
}
