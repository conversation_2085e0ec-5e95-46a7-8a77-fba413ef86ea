<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\SystemFirebaseConnector;
use App\Helper\WSResponse;
use Exception;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;


class SystemFirebaseConnectorTest extends TestCase
{
    private CustomHttpClient $client;
    private SystemFirebaseConnector $connector;
    private LoggerInterface $logger;
    private string $baseUrl = 'https://example.com';

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->client = $this->createMock(CustomHttpClient::class);
        $this->connector = new SystemFirebaseConnector($this->client, $this->baseUrl);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->connector->setLogger($this->logger);
    }

    protected function tearDown(): void
    {
        unset($this->client);
        unset($this->connector);
        unset($this->logger);
        parent::tearDown();
    }

    /**
     * Test successful POST request with notification payload
     */
    public function testSuccessfulPostRequestWithNotificationPayload(): void
    {
        // Test data
        $method = 'POST';
        $uri = '/v1/push/notification';
        $expectedUrl = $this->baseUrl . $uri;
        $successResponse = ['success' => ["message" => "success"]];

        // Create notification message
        $message = [
            'notification' => [
                'title' => 'Vehicle location',
                'body' => 'Vehicle is in India'
            ],
            'data' => [
                'serviceData' => [
                    'EventID' => 'GenericNotificationEvent',
                    'Version' => '1.0',
                    'VehicleId' => 'v38uihwnjsfns',
                    'Timestamp' => 1539179391201,
                    'Data' => [
                        'notificationId' => 'S12_RO_START_FAILED_TIMEOUT',
                        'eventName' => 'HORN_AND_LIGHTS',
                        'flatAttrA' => '18.58483868',
                        'flatAttrB' => 'Boundary 9 Oct',
                        'flatAttrC' => '3bca9be2-fb5d-430a-9bb7-4ce6c7f9a474',
                        'flatAttrD' => '73.73466107'
                    ]
                ],
                'context' => [
                    'nickname' => 'MY CAR',
                    'model' => 'AR',
                    'brandMarketingName' => 'ALFA ROMEO',
                    'firstName' => 'John'
                ]
            ],
            'criticality' => 'high',
            'apiPushConfig' => [
                'staticConfig' => [
                    'silent' => true,
                    'inApp' => true,
                    'push' => true,
                    'android' => [
                        'ttl' => 'string',
                        'notification' => [
                            'click_action' => 'string'
                        ],
                        'priority' => 'string'
                    ],
                    'apns' => [
                        'headers' => [
                            'apns-priority' => 'string'
                        ],
                        'payload' => [
                            'aps' => [
                                'category' => 'string'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Request options
        $options = [
            'headers' => [
                'pushToken' => "token",
            ],
            'json' => [
                'message' => $message
            ],
        ];

        // Mock logger to verify logging
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('call ' . $expectedUrl));

        // Mock client to return success response
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $options)
            ->willReturn(new WSResponse(Response::HTTP_OK, $successResponse));

        // Execute the method
        $response = $this->connector->call($method, $uri, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals($successResponse, $response->getData());
    }

    /**
     * Test successful GET request with query parameters
     */
    public function testSuccessfulGetRequestWithQueryParameters(): void
    {
        // Test data
        $method = 'GET';
        $uri = '/v1/notifications';
        $expectedUrl = $this->baseUrl . $uri;
        $successResponse = [
            'notifications' => [
                [
                    'id' => '123',
                    'title' => 'Test Notification',
                    'body' => 'This is a test notification',
                    'timestamp' => 1639179391201
                ]
            ],
            'count' => 1,
            'total' => 1
        ];

        // Request options with query parameters
        $options = [
            'query' => [
                'userId' => 'user123',
                'limit' => 10,
                'offset' => 0
            ]
        ];

        // Mock client to return success response
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $options)
            ->willReturn(new WSResponse(Response::HTTP_OK, $successResponse));

        // Execute the method
        $response = $this->connector->call($method, $uri, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals($successResponse, $response->getData());
    }

    /**
     * Test successful DELETE request
     */
    public function testSuccessfulDeleteRequest(): void
    {
        // Test data
        $method = 'DELETE';
        $uri = '/v1/notifications/123';
        $expectedUrl = $this->baseUrl . $uri;
        $successResponse = ['success' => true];

        // Mock client to return success response
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, [])
            ->willReturn(new WSResponse(Response::HTTP_OK, $successResponse));

        // Execute the method
        $response = $this->connector->call($method, $uri);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals($successResponse, $response->getData());
    }

    /**
     * Test request that returns a 4xx client error
     */
    public function testRequestWithClientError(): void
    {
        // Test data
        $method = 'POST';
        $uri = '/v1/push/notification';
        $expectedUrl = $this->baseUrl . $uri;
        $errorResponse = ['error' => 'Invalid request parameters'];

        // Request options
        $options = [
            'headers' => [
                'pushToken' => "invalid-token",
            ],
            'json' => [
                'message' => ['invalid' => 'payload']
            ],
        ];

        // Mock client to return error response
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $options)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponse));

        // Execute the method
        $response = $this->connector->call($method, $uri, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
        $this->assertEquals($errorResponse, $response->getData());
    }

    /**
     * Test request that throws an exception with HTTP status code
     */
    public function testRequestWithHttpException(): void
    {
        // Test data
        $method = 'POST';
        $uri = '/v1/push/notification';
        $expectedUrl = $this->baseUrl . $uri;
        $errorMessage = 'Unprocessable Content';
        $errorCode = Response::HTTP_UNPROCESSABLE_ENTITY;

        // Request options
        $options = [
            'headers' => [
                'pushToken' => "token",
            ],
            'json' => [
                'title' => 'notification',
            ],
        ];

        // Mock client to throw exception
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $options)
            ->willThrowException(new Exception($errorMessage, $errorCode));

        // Mock logger to verify error logging
        $this->logger->expects($this->once())
            ->method('error')
            ->with(
                $this->stringContains('Cached Exception ' . $errorMessage),
                $this->callback(function($context) use ($method, $expectedUrl) {
                    return isset($context[0]) &&
                           isset($context['url']) &&
                           $context[0] === $method &&
                           $context['url'] === $expectedUrl;
                })
            );

        // Execute the method
        $response = $this->connector->call($method, $uri, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals($errorCode, $response->getCode());
        $this->assertEquals($errorMessage, $response->getData());
    }

    /**
     * Test request that throws an exception with non-HTTP status code
     */
    public function testRequestWithGenericException(): void
    {
        // Test data
        $method = 'GET';
        $uri = '/v1/notifications';
        $expectedUrl = $this->baseUrl . $uri;
        $errorMessage = 'Connection timeout';
        $errorCode = 0; // Non-HTTP status code

        // Mock client to throw exception
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, [])
            ->willThrowException(new Exception($errorMessage, $errorCode));

        // Execute the method
        $response = $this->connector->call($method, $uri);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals($errorCode, $response->getCode());
        $this->assertEquals($errorMessage, $response->getData());
    }

    /**
     * Test request with empty options array
     */
    public function testRequestWithEmptyOptions(): void
    {
        // Test data
        $method = 'GET';
        $uri = '/v1/status';
        $expectedUrl = $this->baseUrl . $uri;
        $successResponse = ['status' => 'ok', 'version' => '1.0.0'];

        // Mock client to return success response
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, [])
            ->willReturn(new WSResponse(Response::HTTP_OK, $successResponse));

        // Execute the method
        $response = $this->connector->call($method, $uri);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals($successResponse, $response->getData());
    }

    /**
     * Test constructor sets properties correctly
     */
    public function testConstructorSetsPropertiesCorrectly(): void
    {
        // Create a new instance with different values
        $customClient = $this->createMock(CustomHttpClient::class);
        $customUrl = 'https://custom-example.com';
        $customConnector = new SystemFirebaseConnector($customClient, $customUrl);

        // Use reflection to access private properties
        $reflector = new \ReflectionClass($customConnector);

        $clientProperty = $reflector->getProperty('client');
        $clientProperty->setAccessible(true);

        $urlProperty = $reflector->getProperty('url');
        $urlProperty->setAccessible(true);

        // Assert that properties were set correctly
        $this->assertSame($customClient, $clientProperty->getValue($customConnector));
        $this->assertSame($customUrl, $urlProperty->getValue($customConnector));
    }

    /**
     * Test PUT request with JSON payload
     */
    public function testPutRequestWithJsonPayload(): void
    {
        // Test data
        $method = 'PUT';
        $uri = '/v1/notifications/123';
        $expectedUrl = $this->baseUrl . $uri;
        $successResponse = ['success' => true, 'updated' => true];

        // Request options with JSON payload
        $options = [
            'json' => [
                'title' => 'Updated Notification',
                'body' => 'This notification has been updated',
                'priority' => 'high'
            ]
        ];

        // Mock client to return success response
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $options)
            ->willReturn(new WSResponse(Response::HTTP_OK, $successResponse));

        // Execute the method
        $response = $this->connector->call($method, $uri, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals($successResponse, $response->getData());
    }

    /**
     * Test logger setter from LoggerTrait
     */
    public function testLoggerSetter(): void
    {
        // Create a new instance
        $customClient = $this->createMock(CustomHttpClient::class);
        $customUrl = 'https://custom-example.com';
        $customConnector = new SystemFirebaseConnector($customClient, $customUrl);

        // Create a custom logger
        $customLogger = $this->createMock(LoggerInterface::class);

        // Set the logger
        $customConnector->setLogger($customLogger);

        // Use reflection to access private logger property
        $reflector = new \ReflectionClass($customConnector);
        $loggerProperty = $reflector->getProperty('logger');
        $loggerProperty->setAccessible(true);

        // Assert that logger was set correctly
        $this->assertSame($customLogger, $loggerProperty->getValue($customConnector));

        // Test that logging works with the custom logger
        $customLogger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('call'));

        // Make a call to trigger logging
        $customClient->method('request')
            ->willReturn(new WSResponse(Response::HTTP_OK, []));

        $customConnector->call('GET', '/test');
    }
}
