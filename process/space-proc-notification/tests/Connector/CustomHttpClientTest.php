<?php

namespace App\Tests\Connector;

use App\Helper\WSResponse;
use App\Connector\CustomHttpClient;
use App\Connector\InternalHttpClientService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\ResponseInterface;

class CustomHttpClientTest extends TestCase
{
    private $httpClient;
    private $logger;
    private $customHttpClient;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(InternalHttpClientService::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->customHttpClient = new CustomHttpClient($this->httpClient);
        $this->customHttpClient->setLogger($this->logger);
    }

    protected function tearDown(): void
    {
        $this->httpClient = null;
        $this->logger = null;
        $this->customHttpClient = null;

        parent::tearDown();
    }

    /**
     * Test successful HTTP request with a 200 OK response
     */
    public function testSuccessfulRequestWithOkResponse(): void
    {
        // Test data
        $method = 'GET';
        $url = 'http://example.com/api';
        $options = ['headers' => ['Content-Type' => 'application/json']];
        $responseData = ['foo' => 'bar', 'status' => 'success'];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('toArray')->willReturn($responseData);
        $response->method('getInfo')->with('debug')->willReturn('Debug info');

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Verify logging
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [
                    $this->equalTo("REQUEST: {$method} {$url}"),
                    $this->callback(function($context) use ($method, $url, $options) {
                        return $context['method'] === $method &&
                               $context['url'] === $url &&
                               $context['options'] === json_encode($options);
                    })
                ],
                [
                    $this->equalTo("RESPONSE: {$method} {$url}"),
                    $this->callback(function($context) use ($responseData) {
                        return $context['status_code'] === Response::HTTP_OK &&
                               $context['data'] === json_encode($responseData);
                    })
                ]
            );

        $this->logger->expects($this->once())
            ->method('debug')
            ->with('Full response logs: {logs}', ['logs' => 'Debug info']);

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test successful HTTP request with a 204 No Content response
     */
    public function testSuccessfulRequestWithNoContentResponse(): void
    {
        // Test data
        $method = 'DELETE';
        $url = 'http://example.com/api/resource/123';
        $options = [];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_NO_CONTENT);
        $response->method('getInfo')->with('debug')->willReturn('Debug info');

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Verify logging
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [
                    $this->equalTo("REQUEST: {$method} {$url}"),
                    $this->callback(function($context) use ($method, $url) {
                        return $context['method'] === $method &&
                               $context['url'] === $url;
                    })
                ],
                [
                    $this->equalTo("RESPONSE: {$method} {$url}"),
                    $this->callback(function($context) {
                        return $context['status_code'] === Response::HTTP_NO_CONTENT &&
                               $context['data'] === json_encode([]);
                    })
                ]
            );

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NO_CONTENT, $result->getCode());
        $this->assertEquals([], $result->getData());
    }

    /**
     * Test HTTP request with a client error response (4xx)
     */
    public function testRequestWithClientErrorResponse(): void
    {
        // Test data
        $method = 'POST';
        $url = 'http://example.com/api/users';
        $options = ['json' => ['name' => 'John Doe']];
        $responseData = ['error' => 'Invalid input', 'code' => 'VALIDATION_ERROR'];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_BAD_REQUEST);
        $response->method('toArray')->willReturn($responseData);
        $response->method('getInfo')->with('debug')->willReturn('Debug info');

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test HTTP request with a server error response (5xx)
     */
    public function testRequestWithServerErrorResponse(): void
    {
        // Test data
        $method = 'GET';
        $url = 'http://example.com/api/status';
        $options = [];
        $responseData = ['error' => 'Internal server error', 'details' => 'Database connection failed'];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->method('toArray')->willReturn($responseData);
        $response->method('getInfo')->with('debug')->willReturn('Debug info');

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test HTTP request that throws a generic exception
     */
    public function testRequestWithGenericException(): void
    {
        // Test data
        $method = 'GET';
        $url = 'http://example.com/api';
        $options = [];
        $errorMessage = 'Connection timed out';
        $errorCode = 408;

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willThrowException(new \Exception($errorMessage, $errorCode));

        // Verify error logging
        $this->logger->expects($this->once())
            ->method('error')
            ->with(
                $this->equalTo('Cached Exception : CustomHttpClient::request '.$errorMessage),
                $this->callback(function($context) use ($method, $url, $errorCode) {
                    return $context['method'] === $method &&
                           $context['url'] === $url &&
                           $context['error_code'] === $errorCode &&
                           isset($context['trace']);
                })
            );

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($errorCode, $result->getCode());
        $this->assertEquals($errorMessage, $result->getData());
    }

    /**
     * Test HTTP request that throws a specific HTTP exception
     */
    public function testRequestWithHttpException(): void
    {
        // Test data
        $method = 'GET';
        $url = 'http://example.com/api/restricted';
        $options = [];
        $errorMessage = 'Unauthorized access';
        $errorCode = Response::HTTP_UNAUTHORIZED;

        // Configure mocks
        $exception = new \Exception($errorMessage, $errorCode);
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willThrowException($exception);

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($errorCode, $result->getCode());
        $this->assertEquals($errorMessage, $result->getData());
    }

    /**
     * Test HTTP request with complex request options
     */
    public function testRequestWithComplexOptions(): void
    {
        // Test data
        $method = 'POST';
        $url = 'http://example.com/api/upload';
        $options = [
            'headers' => [
                'Authorization' => 'Bearer token123',
                'Content-Type' => 'multipart/form-data'
            ],
            'body' => 'file content',
            'timeout' => 30,
            'max_redirects' => 5
        ];
        $responseData = ['success' => true, 'fileId' => '12345'];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_CREATED);
        $response->method('toArray')->willReturn($responseData);
        $response->method('getInfo')->with('debug')->willReturn('Debug info');

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Verify logging
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [
                    $this->equalTo("REQUEST: {$method} {$url}"),
                    $this->callback(function($context) use ($method, $url, $options) {
                        return $context['method'] === $method &&
                               $context['url'] === $url &&
                               $context['options'] === json_encode($options);
                    })
                ],
                [
                    $this->equalTo("RESPONSE: {$method} {$url}"),
                    $this->callback(function($context) use ($responseData) {
                        return $context['status_code'] === Response::HTTP_CREATED &&
                               $context['data'] === json_encode($responseData);
                    })
                ]
            );

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test HTTP PUT request for updating a resource
     */
    public function testPutRequest(): void
    {
        // Test data
        $method = 'PUT';
        $url = 'http://example.com/api/users/123';
        $options = [
            'headers' => [
                'Content-Type' => 'application/json'
            ],
            'json' => [
                'name' => 'John Smith',
                'email' => '<EMAIL>'
            ]
        ];
        $responseData = [
            'id' => 123,
            'name' => 'John Smith',
            'email' => '<EMAIL>',
            'updated_at' => '2023-05-20T10:00:00Z'
        ];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('toArray')->willReturn($responseData);
        $response->method('getInfo')->with('debug')->willReturn('Debug info');

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Verify logging
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [
                    $this->equalTo("REQUEST: {$method} {$url}"),
                    $this->callback(function($context) use ($method, $url, $options) {
                        return $context['method'] === $method &&
                               $context['url'] === $url &&
                               $context['options'] === json_encode($options);
                    })
                ],
                [
                    $this->equalTo("RESPONSE: {$method} {$url}"),
                    $this->callback(function($context) use ($responseData) {
                        return $context['status_code'] === Response::HTTP_OK &&
                               $context['data'] === json_encode($responseData);
                    })
                ]
            );

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test HTTP PATCH request for partial update of a resource
     */
    public function testPatchRequest(): void
    {
        // Test data
        $method = 'PATCH';
        $url = 'http://example.com/api/users/123';
        $options = [
            'headers' => [
                'Content-Type' => 'application/json'
            ],
            'json' => [
                'email' => '<EMAIL>'
            ]
        ];
        $responseData = [
            'id' => 123,
            'name' => 'John Smith',
            'email' => '<EMAIL>',
            'updated_at' => '2023-05-20T11:30:00Z'
        ];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('toArray')->willReturn($responseData);
        $response->method('getInfo')->with('debug')->willReturn('Debug info');

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Verify logging
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [
                    $this->equalTo("REQUEST: {$method} {$url}"),
                    $this->callback(function($context) use ($method, $url, $options) {
                        return $context['method'] === $method &&
                               $context['url'] === $url &&
                               $context['options'] === json_encode($options);
                    })
                ],
                [
                    $this->equalTo("RESPONSE: {$method} {$url}"),
                    $this->callback(function($context) use ($responseData) {
                        return $context['status_code'] === Response::HTTP_OK &&
                               $context['data'] === json_encode($responseData);
                    })
                ]
            );

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test HTTP request with query parameters
     */
    public function testRequestWithQueryParameters(): void
    {
        // Test data
        $method = 'GET';
        $url = 'http://example.com/api/search';
        $options = [
            'query' => [
                'q' => 'test query',
                'page' => 1,
                'limit' => 20,
                'sort' => 'relevance'
            ]
        ];
        $responseData = [
            'results' => [
                ['id' => 1, 'title' => 'Test Result 1'],
                ['id' => 2, 'title' => 'Test Result 2']
            ],
            'total' => 2,
            'page' => 1,
            'limit' => 20
        ];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('toArray')->willReturn($responseData);
        $response->method('getInfo')->with('debug')->willReturn('Debug info');

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test HTTP request with a non-standard HTTP status code
     */
    public function testRequestWithNonStandardStatusCode(): void
    {
        // Test data
        $method = 'GET';
        $url = 'http://example.com/api/status';
        $options = [];
        $responseData = ['message' => 'Enhanced status information'];
        $nonStandardStatusCode = 418; // I'm a teapot

        // Mock response
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn($nonStandardStatusCode);
        $response->method('toArray')->willReturn($responseData);
        $response->method('getInfo')->with('debug')->willReturn('Debug info');

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Verify logging
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [
                    $this->equalTo("REQUEST: {$method} {$url}"),
                    $this->callback(function($context) use ($method, $url) {
                        return $context['method'] === $method &&
                               $context['url'] === $url;
                    })
                ],
                [
                    $this->equalTo("RESPONSE: {$method} {$url}"),
                    $this->callback(function($context) use ($responseData, $nonStandardStatusCode) {
                        return $context['status_code'] === $nonStandardStatusCode &&
                               $context['data'] === json_encode($responseData);
                    })
                ]
            );

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($nonStandardStatusCode, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test HTTP request with a zero exception code
     */
    public function testRequestWithZeroExceptionCode(): void
    {
        // Test data
        $method = 'GET';
        $url = 'http://example.com/api/error';
        $options = [];
        $errorMessage = 'Unknown error occurred';
        $errorCode = 0; // Zero error code

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willThrowException(new \Exception($errorMessage, $errorCode));

        // Verify error logging
        $this->logger->expects($this->once())
            ->method('error')
            ->with(
                $this->equalTo('Cached Exception : CustomHttpClient::request '.$errorMessage),
                $this->callback(function($context) use ($method, $url, $errorCode) {
                    return $context['method'] === $method &&
                           $context['url'] === $url &&
                           $context['error_code'] === $errorCode &&
                           isset($context['trace']);
                })
            );

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($errorCode, $result->getCode());
        $this->assertEquals($errorMessage, $result->getData());
    }

    /**
     * Test that tearDown properly cleans up resources
     */
    public function testTearDownCleansUpResources(): void
    {
        // Store references to the objects
        $httpClient = $this->httpClient;
        $logger = $this->logger;
        $customHttpClient = $this->customHttpClient;

        // Call tearDown manually
        $this->tearDown();

        // Verify that the objects have been nullified
        $this->assertNull($this->httpClient);
        $this->assertNull($this->logger);
        $this->assertNull($this->customHttpClient);

        // Restore the objects for proper test cleanup
        $this->httpClient = $httpClient;
        $this->logger = $logger;
        $this->customHttpClient = $customHttpClient;
    }
}
