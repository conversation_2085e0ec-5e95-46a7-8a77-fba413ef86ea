<?php

namespace App\Tests\Connector;

use App\Connector\MongoAtlasApiConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class MongoAtlasApiConnectorTest extends TestCase
{
    private $client;
    private $logger;
    private $mongoApp = 'test-app';
    private $connector;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->client = $this->createMock(HttpClientInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->connector = new MongoAtlasApiConnector($this->client, $this->mongoApp);
        $this->connector->setLogger($this->logger);
    }

    protected function tearDown(): void
    {
        unset($this->client);
        unset($this->logger);
        unset($this->connector);
    }

    /**
     * Test successful GET request
     */
    public function testSuccessfulGetRequest(): void
    {
        // Test data
        $method = 'GET';
        $endpoint = '/test/resource';
        $options = ['query' => ['filter' => 'value']];
        $responseContent = '{"status": "success", "data": {"id": 123, "name": "Test"}}';

        // Mock response
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(Response::HTTP_OK);
        $response->method('getContent')->willReturn($responseContent);

        // Configure client mock
        $this->client->method('request')->willReturn($response);

        // Execute the method
        $result = $this->connector->call($method, $endpoint, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseContent, $result->getData());
    }

    /**
     * Test API call that throws an exception
     */
    public function testApiCallWithException(): void
    {
        // Test data
        $method = 'GET';
        $endpoint = '/test/resource';
        $options = [];
        $errorMessage = 'Connection timed out';
        $errorCode = 408;

        // Configure client mock to throw exception
        $this->client->method('request')
            ->willThrowException(new \Exception($errorMessage, $errorCode));

        // Execute the method
        $result = $this->connector->call($method, $endpoint, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($errorCode, $result->getCode());
        $this->assertEquals($errorMessage, $result->getData());
    }

    /**
     * Test API call that throws an exception with zero code
     */
    public function testApiCallWithExceptionZeroCode(): void
    {
        // Test data
        $method = 'GET';
        $endpoint = '/test/resource';
        $options = [];
        $errorMessage = 'Unknown error';
        $errorCode = 0;

        // Configure client mock to throw exception
        $this->client->method('request')
            ->willThrowException(new \Exception($errorMessage, $errorCode));

        // Execute the method
        $result = $this->connector->call($method, $endpoint, $options);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($errorCode, $result->getCode());
        $this->assertEquals($errorMessage, $result->getData());
    }

    /**
     * Test getEndpoint method with different actions
     *
     * @dataProvider endpointActionProvider
     */
    public function testGetEndpointWithDifferentActions(string $action): void
    {
        $expectedEndpoint = sprintf('/app/%s/endpoint/data/v1/action/%s', $this->mongoApp, $action);

        $endpoint = $this->connector->getEndpoint($action);

        $this->assertEquals($expectedEndpoint, $endpoint);
    }

    /**
     * Data provider for endpoint actions
     */
    public function endpointActionProvider(): array
    {
        return [
            'find action' => ['find'],
            'findOne action' => ['findOne'],
            'insertOne action' => ['insertOne'],
            'insertMany action' => ['insertMany'],
            'updateOne action' => ['updateOne'],
            'updateMany action' => ['updateMany'],
            'deleteOne action' => ['deleteOne'],
            'deleteMany action' => ['deleteMany'],
            'aggregate action' => ['aggregate'],
            'count action' => ['count'],
            'custom action' => ['customAction'],
        ];
    }
}
