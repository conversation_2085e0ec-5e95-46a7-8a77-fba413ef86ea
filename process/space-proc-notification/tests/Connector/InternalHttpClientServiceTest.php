<?php

namespace App\Tests\Connector;

use App\Connector\InternalHttpClientService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Symfony\Contracts\HttpClient\ResponseStreamInterface;

/**
 * Tests for the InternalHttpClientService class
 *
 * This test suite covers all scenarios for the InternalHttpClientService:
 * - Request method delegation with various HTTP methods (GET, POST, PUT, DELETE)
 * - Request method with different options (headers, query parameters, body)
 * - Stream method delegation with single and multiple responses
 * - Stream method with different timeout values
 * - WithOptions method behavior and method chaining
 * - Constructor behavior with and without current request
 * - Error handling and edge cases
 */
class InternalHttpClientServiceTest extends TestCase
{
    private $httpClient;
    private $requestStack;
    private $request;
    private $service;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->requestStack = $this->createMock(RequestStack::class);
        $this->request = $this->createMock(Request::class);

        // Default setup: RequestStack returns a Request
        $this->requestStack->method('getCurrentRequest')->willReturn($this->request);

        $this->service = new InternalHttpClientService($this->httpClient, $this->requestStack);
    }

    protected function tearDown(): void
    {
        unset($this->httpClient);
        unset($this->requestStack);
        unset($this->request);
        unset($this->service);
    }

    /**
     * Test that the request method delegates to the underlying HTTP client
     */
    public function testRequestMethodDelegation(): void
    {
        // Test data
        $method = 'GET';
        $url = 'https://example.com/api';
        $options = ['headers' => ['X-API-Key' => 'test-key']];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Execute the method
        $result = $this->service->request($method, $url, $options);

        // Assertions
        $this->assertSame($response, $result);
    }

    /**
     * Test that the request method works with default options
     */
    public function testRequestMethodWithDefaultOptions(): void
    {
        // Test data
        $method = 'POST';
        $url = 'https://example.com/api/resource';

        // Mock response
        $response = $this->createMock(ResponseInterface::class);

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, [])
            ->willReturn($response);

        // Execute the method
        $result = $this->service->request($method, $url);

        // Assertions
        $this->assertSame($response, $result);
    }

    /**
     * Test that the stream method delegates to the underlying HTTP client
     */
    public function testStreamMethodDelegation(): void
    {
        // Test data
        $responses = [$this->createMock(ResponseInterface::class)];
        $timeout = 30.0;

        // Mock response stream
        $responseStream = $this->createMock(ResponseStreamInterface::class);

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('stream')
            ->with($responses, $timeout)
            ->willReturn($responseStream);

        // Execute the method
        $result = $this->service->stream($responses, $timeout);

        // Assertions
        $this->assertSame($responseStream, $result);
    }

    /**
     * Test that the stream method works with default timeout
     */
    public function testStreamMethodWithDefaultTimeout(): void
    {
        // Test data
        $responses = [$this->createMock(ResponseInterface::class)];

        // Mock response stream
        $responseStream = $this->createMock(ResponseStreamInterface::class);

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('stream')
            ->with($responses, null)
            ->willReturn($responseStream);

        // Execute the method
        $result = $this->service->stream($responses);

        // Assertions
        $this->assertSame($responseStream, $result);
    }

    /**
     * Test that the withOptions method returns the service itself
     */
    public function testWithOptionsMethodReturnsSelf(): void
    {
        // Test data
        $options = ['timeout' => 5, 'max_redirects' => 3];

        // Execute the method
        $result = $this->service->withOptions($options);

        // Assertions
        $this->assertSame($this->service, $result);
    }

    /**
     * Test constructor with null current request
     */
    public function testConstructorWithNullCurrentRequest(): void
    {
        // Configure requestStack to return null
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn(null);

        // Create service with null request
        $service = new InternalHttpClientService($this->httpClient, $requestStack);

        // Test that the service still works
        $method = 'GET';
        $url = 'https://example.com/api';
        $response = $this->createMock(ResponseInterface::class);

        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, [])
            ->willReturn($response);

        $result = $service->request($method, $url);

        $this->assertSame($response, $result);
    }

    /**
     * Test that the service implements HttpClientInterface
     */
    public function testServiceImplementsHttpClientInterface(): void
    {
        $this->assertInstanceOf(HttpClientInterface::class, $this->service);
    }

    /**
     * Test request method with PUT HTTP method
     */
    public function testRequestMethodWithPutMethod(): void
    {
        // Test data
        $method = 'PUT';
        $url = 'https://example.com/api/resource/123';
        $options = [
            'headers' => ['Content-Type' => 'application/json'],
            'json' => ['name' => 'Updated Resource', 'status' => 'active']
        ];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Execute the method
        $result = $this->service->request($method, $url, $options);

        // Assertions
        $this->assertSame($response, $result);
    }

    /**
     * Test request method with DELETE HTTP method
     */
    public function testRequestMethodWithDeleteMethod(): void
    {
        // Test data
        $method = 'DELETE';
        $url = 'https://example.com/api/resource/123';
        $options = [
            'headers' => ['Authorization' => 'Bearer token123']
        ];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Execute the method
        $result = $this->service->request($method, $url, $options);

        // Assertions
        $this->assertSame($response, $result);
    }

    /**
     * Test request method with query parameters
     */
    public function testRequestMethodWithQueryParameters(): void
    {
        // Test data
        $method = 'GET';
        $url = 'https://example.com/api/search';
        $options = [
            'query' => [
                'q' => 'test query',
                'page' => 1,
                'limit' => 20,
                'sort' => 'relevance'
            ]
        ];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Execute the method
        $result = $this->service->request($method, $url, $options);

        // Assertions
        $this->assertSame($response, $result);
    }

    /**
     * Test request method with form data
     */
    public function testRequestMethodWithFormData(): void
    {
        // Test data
        $method = 'POST';
        $url = 'https://example.com/api/form';
        $options = [
            'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
            'body' => http_build_query([
                'username' => 'testuser',
                'password' => 'password123',
                'remember_me' => true
            ])
        ];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Execute the method
        $result = $this->service->request($method, $url, $options);

        // Assertions
        $this->assertSame($response, $result);
    }

    /**
     * Test stream method with multiple responses
     */
    public function testStreamMethodWithMultipleResponses(): void
    {
        // Test data
        $response1 = $this->createMock(ResponseInterface::class);
        $response2 = $this->createMock(ResponseInterface::class);
        $response3 = $this->createMock(ResponseInterface::class);
        $responses = [$response1, $response2, $response3];
        $timeout = 60.0;

        // Mock response stream
        $responseStream = $this->createMock(ResponseStreamInterface::class);

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('stream')
            ->with($responses, $timeout)
            ->willReturn($responseStream);

        // Execute the method
        $result = $this->service->stream($responses, $timeout);

        // Assertions
        $this->assertSame($responseStream, $result);
    }

    /**
     * Test withOptions method chaining with request
     */
    public function testWithOptionsMethodChainingWithRequest(): void
    {
        // Test data
        $method = 'GET';
        $url = 'https://example.com/api/resource';
        $options = [];

        // Mock response
        $response = $this->createMock(ResponseInterface::class);

        // Configure mocks
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($response);

        // Execute the method with chaining
        $result = $this->service
            ->withOptions(['timeout' => 30])
            ->withOptions(['max_redirects' => 5])
            ->request($method, $url, $options);

        // Assertions
        $this->assertSame($response, $result);
    }
}
