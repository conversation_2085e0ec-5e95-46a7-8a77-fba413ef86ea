<?php

namespace App\Tests\Manager;

use App\Manager\NotificationManager;
use App\Service\NotificationService;
use App\Manager\UserManager;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Psr\Log\LoggerInterface;
use Exception;


class NotificationManagerTest extends TestCase
{
    private $notificationService;
    private $userManager;
    private $notificationManager;
    private $logger;
    private string $userId;
    private array $timeRangeData;
    private string $vin;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->notificationService = $this->createMock(NotificationService::class);
        $this->userManager = $this->createMock(UserManager::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->notificationManager = new NotificationManager(
            $this->notificationService,
            $this->userManager
        );
        $this->notificationManager->setLogger($this->logger);

        // Common test data
        $this->userId = '111ef5d49f0b4dce874859657ae98122';
        $this->timeRangeData = [
            'since' => 1609459200, // 2021-01-01
            'till' => 1640995200,  // 2022-01-01
        ];
        $this->vin = 'WBAJB1C50JB083604';
    }

    /**
     * Clean up the test environment after each test
     */
    protected function tearDown(): void
    {
        $this->notificationService = null;
        $this->userManager = null;
        $this->logger = null;
        $this->notificationManager = null;

        parent::tearDown();
    }

    /**
     * Test getVehicleNotifications with successful response and data
     */
    public function testGetVehicleNotificationsSuccess(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return notifications
        $notificationsData = [
            'documents' => [[
                'count' => 2,
                'items' => [
                    [
                        'title' => 'Vehicle location',
                        'body' => 'Vehicle is in India',
                        'criticality' => 'high',
                        'timestamp' => 1620000000,
                        'vin' => $this->vin
                    ],
                    [
                        'title' => 'Low fuel',
                        'body' => 'Your vehicle is low on fuel',
                        'criticality' => 'medium',
                        'timestamp' => 1625000000,
                        'vin' => $this->vin
                    ]
                ]
            ]]
        ];

        $this->notificationService
            ->expects($this->once())
            ->method('getVehicleNotifications')
            ->with($this->userId, $this->vin, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode($notificationsData)));

        // Configure logger expectations
        $this->logger
            ->expects($this->once())
            ->method('info')
            ->with($this->stringContains('for vin ' . $this->vin));

        // Call the method
        $result = $this->notificationManager->getVehicleNotifications(
            $this->userId,
            $this->timeRangeData,
            $this->vin
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('userId', $result['content']);
        $this->assertArrayHasKey('startTS', $result['content']);
        $this->assertArrayHasKey('endTS', $result['content']);
        $this->assertArrayHasKey('notifications', $result['content']);
        $this->assertEquals($this->userId, $result['content']['userId']);
        $this->assertEquals((int)$this->timeRangeData['since'], $result['content']['startTS']);
        $this->assertEquals((int)$this->timeRangeData['till'], $result['content']['endTS']);
    }

    /**
     * Test getVehicleNotifications with successful response but empty data
     */
    public function testGetVehicleNotificationsSuccessEmptyData(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return empty notifications
        // The documents array should have at least one element for reset() to work
        $notificationsData = [
            'documents' => [[]]
        ];

        $this->notificationService
            ->expects($this->once())
            ->method('getVehicleNotifications')
            ->with($this->userId, $this->vin, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode($notificationsData)));

        // Call the method
        $result = $this->notificationManager->getVehicleNotifications(
            $this->userId,
            $this->timeRangeData,
            $this->vin
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('userId', $result['content']);
        $this->assertArrayHasKey('startTS', $result['content']);
        $this->assertArrayHasKey('endTS', $result['content']);
        $this->assertArrayHasKey('notifications', $result['content']);
        $this->assertEquals($this->userId, $result['content']['userId']);
        $this->assertEquals((int)$this->timeRangeData['since'], $result['content']['startTS']);
        $this->assertEquals((int)$this->timeRangeData['till'], $result['content']['endTS']);
        // The notifications array should be an empty array in this case
        $this->assertIsArray($result['content']['notifications']);
        $this->assertEmpty($result['content']['notifications']);
    }

    /**
     * Test getVehicleNotifications with no users found
     */
    public function testGetVehicleNotificationsNoUsersFound(): void
    {
        // Mock user manager to return no users
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => []
            ])));

        // Configure logger expectations
        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with('No users found', ['userId' => $this->userId]);

        // Call the method
        $result = $this->notificationManager->getVehicleNotifications(
            $this->userId,
            $this->timeRangeData,
            $this->vin
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals('No users found', $result['content']['error']['message']);
    }

    /**
     * Test getVehicleNotifications with service error
     */
    public function testGetVehicleNotificationsServiceError(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return an error
        $errorMessage = 'Service unavailable';
        $errorResponse = [
            'error' => [
                'message' => $errorMessage
            ]
        ];

        $this->notificationService
            ->expects($this->once())
            ->method('getVehicleNotifications')
            ->with($this->userId, $this->vin, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode($errorResponse)));

        // Call the method
        $result = $this->notificationManager->getVehicleNotifications(
            $this->userId,
            $this->timeRangeData,
            $this->vin
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_SERVICE_UNAVAILABLE, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals($errorMessage, $result['content']['error']['message']);
    }

    /**
     * Test getVehicleNotifications with generic exception
     */
    public function testGetVehicleNotificationsGenericException(): void
    {
        // Mock user manager to throw an exception
        $exceptionMessage = 'Database connection error';
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willThrowException(new Exception($exceptionMessage));

        // Configure logger expectations
        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Caught Exception in NotificationManager::getNotifications'));

        // Call the method
        $result = $this->notificationManager->getVehicleNotifications(
            $this->userId,
            $this->timeRangeData,
            $this->vin
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals($exceptionMessage, $result['content']['error']['message']);
    }

    /**
     * Test getUserVehicleNotifications with successful response and data
     */
    public function testGetUserVehicleNotificationsSuccess(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return notifications
        $notificationsData = [
            'documents' => [
                [
                    'count' => 1,
                    'items' => [
                        [
                            'title' => 'Vehicle location',
                            'body' => 'Vehicle is in India',
                            'criticality' => 'high',
                            'timestamp' => 1620000000,
                            'vin' => 'VIN1'
                        ]
                    ]
                ],
                [
                    'count' => 1,
                    'items' => [
                        [
                            'title' => 'Low fuel',
                            'body' => 'Your vehicle is low on fuel',
                            'criticality' => 'medium',
                            'timestamp' => 1625000000,
                            'vin' => 'VIN2'
                        ]
                    ]
                ]
            ]
        ];

        $this->notificationService
            ->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->with($this->userId, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode($notificationsData)));

        // Configure logger expectations
        $this->logger
            ->expects($this->once())
            ->method('info')
            ->with($this->stringContains('for userId ' . $this->userId));

        // Call the method
        $result = $this->notificationManager->getUserVehicleNotifications(
            $this->userId,
            $this->timeRangeData
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('userId', $result['content']);
        $this->assertArrayHasKey('startTS', $result['content']);
        $this->assertArrayHasKey('endTS', $result['content']);
        $this->assertArrayHasKey('notifications', $result['content']);
        $this->assertEquals($this->userId, $result['content']['userId']);
        $this->assertEquals((int)$this->timeRangeData['since'], $result['content']['startTS']);
        $this->assertEquals((int)$this->timeRangeData['till'], $result['content']['endTS']);
        $this->assertCount(2, $result['content']['notifications']);
    }

    /**
     * Test getUserVehicleNotifications with no users found
     */
    public function testGetUserVehicleNotificationsNoUsersFound(): void
    {
        // Mock user manager to return no users
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => []
            ])));

        // Configure logger expectations
        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with('No users found', ['userId' => $this->userId]);

        // Call the method
        $result = $this->notificationManager->getUserVehicleNotifications(
            $this->userId,
            $this->timeRangeData
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals('No users found', $result['content']['error']['message']);
    }

    /**
     * Test getUserVehicleNotifications with service error
     */
    public function testGetUserVehicleNotificationsServiceError(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return an error
        $errorMessage = 'Service unavailable';
        $errorResponse = [
            'error' => [
                'message' => $errorMessage
            ]
        ];

        $this->notificationService
            ->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->with($this->userId, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode($errorResponse)));

        // Call the method
        $result = $this->notificationManager->getUserVehicleNotifications(
            $this->userId,
            $this->timeRangeData
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_SERVICE_UNAVAILABLE, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals($errorMessage, $result['content']['error']['message']);
    }

    /**
     * Test getUserVehicleNotifications with generic exception
     */
    public function testGetUserVehicleNotificationsGenericException(): void
    {
        // Mock user manager to throw an exception
        $exceptionMessage = 'Database connection error';
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willThrowException(new Exception($exceptionMessage));

        // Configure logger expectations
        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Caught Exception in NotificationManager::getNotifications'));

        // Call the method
        $result = $this->notificationManager->getUserVehicleNotifications(
            $this->userId,
            $this->timeRangeData
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals($exceptionMessage, $result['content']['error']['message']);
    }

    /**
     * Test getUserVehicleNotifications with empty data
     */
    public function testGetUserVehicleNotificationsEmptyData(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return empty notifications
        $notificationsData = [
            'documents' => []
        ];

        $this->notificationService
            ->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->with($this->userId, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode($notificationsData)));

        // Call the method
        $result = $this->notificationManager->getUserVehicleNotifications(
            $this->userId,
            $this->timeRangeData
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('userId', $result['content']);
        $this->assertArrayHasKey('startTS', $result['content']);
        $this->assertArrayHasKey('endTS', $result['content']);
        $this->assertArrayHasKey('notifications', $result['content']);
        $this->assertEquals($this->userId, $result['content']['userId']);
        $this->assertEquals((int)$this->timeRangeData['since'], $result['content']['startTS']);
        $this->assertEquals((int)$this->timeRangeData['till'], $result['content']['endTS']);
        $this->assertIsArray($result['content']['notifications']);
        $this->assertEmpty($result['content']['notifications']);
    }

    /**
     * Test getVehicleNotifications with malformed data
     */
    public function testGetVehicleNotificationsMalformedData(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return malformed data
        $malformedData = "This is not valid JSON";

        $this->notificationService
            ->expects($this->once())
            ->method('getVehicleNotifications')
            ->with($this->userId, $this->vin, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_OK, $malformedData));

        // Call the method
        $result = $this->notificationManager->getVehicleNotifications(
            $this->userId,
            $this->timeRangeData,
            $this->vin
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('userId', $result['content']);
        $this->assertArrayHasKey('startTS', $result['content']);
        $this->assertArrayHasKey('endTS', $result['content']);
        $this->assertArrayHasKey('notifications', $result['content']);
        $this->assertEquals($this->userId, $result['content']['userId']);
        $this->assertEquals((int)$this->timeRangeData['since'], $result['content']['startTS']);
        $this->assertEquals((int)$this->timeRangeData['till'], $result['content']['endTS']);
    }

    /**
     * Test getUserVehicleNotifications with malformed data
     */
    public function testGetUserVehicleNotificationsMalformedData(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return malformed data
        $malformedData = "This is not valid JSON";

        $this->notificationService
            ->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->with($this->userId, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_OK, $malformedData));

        // Call the method
        $result = $this->notificationManager->getUserVehicleNotifications(
            $this->userId,
            $this->timeRangeData
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('userId', $result['content']);
        $this->assertArrayHasKey('startTS', $result['content']);
        $this->assertArrayHasKey('endTS', $result['content']);
        $this->assertArrayHasKey('notifications', $result['content']);
        $this->assertEquals($this->userId, $result['content']['userId']);
        $this->assertEquals((int)$this->timeRangeData['since'], $result['content']['startTS']);
        $this->assertEquals((int)$this->timeRangeData['till'], $result['content']['endTS']);
        $this->assertIsArray($result['content']['notifications']);
        $this->assertEmpty($result['content']['notifications']);
    }

    /**
     * Test getVehicleNotifications with invalid error response format
     */
    public function testGetVehicleNotificationsInvalidErrorResponseFormat(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return an error with invalid format
        $invalidErrorResponse = "Service unavailable";

        $this->notificationService
            ->expects($this->once())
            ->method('getVehicleNotifications')
            ->with($this->userId, $this->vin, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, $invalidErrorResponse));

        // Call the method
        $result = $this->notificationManager->getVehicleNotifications(
            $this->userId,
            $this->timeRangeData,
            $this->vin
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_SERVICE_UNAVAILABLE, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals('Unknown error occurred', $result['content']['error']['message']);
    }

    /**
     * Test getUserVehicleNotifications with invalid error response format
     */
    public function testGetUserVehicleNotificationsInvalidErrorResponseFormat(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return an error with invalid format
        $invalidErrorResponse = "Service unavailable";

        $this->notificationService
            ->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->with($this->userId, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, $invalidErrorResponse));

        // Call the method
        $result = $this->notificationManager->getUserVehicleNotifications(
            $this->userId,
            $this->timeRangeData
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_SERVICE_UNAVAILABLE, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals('Unknown error occurred', $result['content']['error']['message']);
    }

    /**
     * Test getVehicleNotifications with special characters in data
     */
    public function testGetVehicleNotificationsWithSpecialCharacters(): void
    {
        // Mock user manager to return a user
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [['userId' => $this->userId]]
            ])));

        // Mock notification service to return notifications with special characters
        $notificationsData = [
            'documents' => [[
                'count' => 1,
                'items' => [
                    [
                        'title' => 'Special characters: !@#$%^&*()',
                        'body' => 'Text with unicode: 你好, こんにちは, Привет',
                        'criticality' => 'high',
                        'timestamp' => 1620000000,
                        'vin' => $this->vin
                    ]
                ]
            ]]
        ];

        $this->notificationService
            ->expects($this->once())
            ->method('getVehicleNotifications')
            ->with($this->userId, $this->vin, $this->timeRangeData)
            ->willReturn(new WSResponse(Response::HTTP_OK, json_encode($notificationsData)));

        // Call the method
        $result = $this->notificationManager->getVehicleNotifications(
            $this->userId,
            $this->timeRangeData,
            $this->vin
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('notifications', $result['content']);
        $this->assertIsArray($result['content']['notifications']);
        $this->assertArrayHasKey('items', $result['content']['notifications']);
        $this->assertCount(1, $result['content']['notifications']['items']);
        $this->assertEquals('Special characters: !@#$%^&*()', $result['content']['notifications']['items'][0]['title']);
        $this->assertEquals('Text with unicode: 你好, こんにちは, Привет', $result['content']['notifications']['items'][0]['body']);
    }

    /**
     * Test getVehicleNotifications with UnrecoverableMessageHandlingException
     */
    public function testGetVehicleNotificationsWithUnrecoverableException(): void
    {
        // Mock user manager to throw an UnrecoverableMessageHandlingException
        $exceptionMessage = 'Critical error occurred';
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willThrowException(new UnrecoverableMessageHandlingException($exceptionMessage));

        // Call the method
        $result = $this->notificationManager->getVehicleNotifications(
            $this->userId,
            $this->timeRangeData,
            $this->vin
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals($exceptionMessage, $result['content']['error']['message']);
    }

    /**
     * Test getUserVehicleNotifications with UnrecoverableMessageHandlingException
     */
    public function testGetUserVehicleNotificationsWithUnrecoverableException(): void
    {
        // Mock user manager to throw an UnrecoverableMessageHandlingException
        $exceptionMessage = 'Critical error occurred';
        $this->userManager
            ->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willThrowException(new UnrecoverableMessageHandlingException($exceptionMessage));

        // Call the method
        $result = $this->notificationManager->getUserVehicleNotifications(
            $this->userId,
            $this->timeRangeData
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertEquals($exceptionMessage, $result['content']['error']['message']);
    }
}
