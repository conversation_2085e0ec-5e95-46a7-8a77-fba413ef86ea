<?php

namespace App\Tests\Manager;

use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;

use Symfony\Component\HttpFoundation\Response;
use App\Service\RegisterService;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\RegisterManager;
use App\Model\FcmTokenRegisterModel;
use Exception;


class RegisterManagerTest extends TestCase
{
    private $registerService;
    private $validator;
    private $logger;
    private $registerManager;
    private $fcmTokenRegisterModel;
    private $userId;
    private $sessionId;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->registerService = $this->createMock(RegisterService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->fcmTokenRegisterModel = $this->createMock(FcmTokenRegisterModel::class);

        $this->registerManager = new RegisterManager(
            $this->validator,
            $this->registerService
        );
        $this->registerManager->setLogger($this->logger);

        // Common test data
        $this->userId = 'user123';
        $this->sessionId = 'session456';
    }

    protected function tearDown(): void
    {
        $this->registerService = null;
        $this->validator = null;
        $this->logger = null;
        $this->fcmTokenRegisterModel = null;
        $this->registerManager = null;

        parent::tearDown();
    }

    /**
     * Test registerFcmToken with successful response
     */
    public function testRegisterFcmTokenSuccess(): void
    {
        // Configure validator to return no violations
        $violations = $this->createMock(ConstraintViolationListInterface::class);
        $violations->method('count')->willReturn(0);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($this->fcmTokenRegisterModel, null, ['fcmTokenRegister'])
            ->willReturn($violations);

        // Configure register service to return success
        $this->registerService->expects($this->once())
            ->method('registerFcmToken')
            ->with($this->fcmTokenRegisterModel, $this->userId)
            ->willReturn(new WSResponse(Response::HTTP_OK, 'fcm token registered successfully'));

        // Call the method
        $response = $this->registerManager->registerFcmToken($this->fcmTokenRegisterModel, $this->userId);

        // Assertions
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals(['message' => 'fcm token registered successfully'], $response->getData());
    }

    /**
     * Test registerFcmToken with service error
     */
    public function testRegisterFcmTokenFailure(): void
    {
        // Configure validator to return no violations
        $violations = $this->createMock(ConstraintViolationListInterface::class);
        $violations->method('count')->willReturn(0);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($this->fcmTokenRegisterModel, null, ['fcmTokenRegister'])
            ->willReturn($violations);

        // Configure register service to return error
        $errorMessage = 'User not found';
        $this->registerService->expects($this->once())
            ->method('registerFcmToken')
            ->with($this->fcmTokenRegisterModel, $this->userId)
            ->willReturn(new WSResponse(Response::HTTP_NOT_FOUND, $errorMessage));

        // Call the method
        $response = $this->registerManager->registerFcmToken($this->fcmTokenRegisterModel, $this->userId);

        // Assertions
        $this->assertInstanceOf(ErrorResponse::class, $response);

        // Get the response data using toArray()
        $responseData = $response->toArray();
        $this->assertEquals(Response::HTTP_NOT_FOUND, $responseData['code']);
        $this->assertEquals($errorMessage, $responseData['content']['error']['errors']);
    }

    /**
     * Test registerFcmToken with validation error
     */
    public function testRegisterFcmTokenValidationError(): void
    {
        // Create a mock ConstraintViolationList that returns a count > 0
        $violations = $this->createMock(ConstraintViolationListInterface::class);
        $violations->method('count')->willReturn(2);

        // Configure validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($this->fcmTokenRegisterModel, null, ['fcmTokenRegister'])
            ->willReturn($violations);

        // Configure logger expectations
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Validation fails for incoming request'));

        // Call the method
        $response = $this->registerManager->registerFcmToken($this->fcmTokenRegisterModel, $this->userId);

        // Assertions
        $this->assertInstanceOf(ErrorResponse::class, $response);

        // Get the response data using toArray()
        $responseData = $response->toArray();
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $responseData['code']);

        // The errors should be directly in the content.error object
        $this->assertIsArray($responseData['content']['error']);
        $this->assertEquals('validation_failed', $responseData['content']['error']['message']);
    }

    /**
     * Test registerFcmToken with exception
     */
    public function testRegisterFcmTokenException(): void
    {
        // Configure validator to throw an exception
        $exceptionMessage = 'Database connection error';
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($this->fcmTokenRegisterModel, null, ['fcmTokenRegister'])
            ->willThrowException(new Exception($exceptionMessage, 500));

        // Configure logger expectations
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Catching Exception'));

        // Call the method
        $response = $this->registerManager->registerFcmToken($this->fcmTokenRegisterModel, $this->userId);

        // Assertions
        $this->assertInstanceOf(ErrorResponse::class, $response);

        // Get the response data using toArray()
        $responseData = $response->toArray();
        $this->assertEquals(500, $responseData['code']);
        $this->assertEquals($exceptionMessage, $responseData['content']['error']['errors']);
    }

    /**
     * Test registerSessionId with successful response
     */
    public function testRegisterSessionIdSuccess(): void
    {
        // Configure validator to return no violations
        $violations = $this->createMock(ConstraintViolationListInterface::class);
        $violations->method('count')->willReturn(0);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($this->fcmTokenRegisterModel, null, ['fcmTokenRegister'])
            ->willReturn($violations);

        // Configure register service to return success
        $this->registerService->expects($this->once())
            ->method('registerSessionId')
            ->with($this->fcmTokenRegisterModel, $this->userId, $this->sessionId)
            ->willReturn(new WSResponse(Response::HTTP_OK, 'sessionId registered successfully'));

        // Call the method
        $response = $this->registerManager->registerSessionId($this->fcmTokenRegisterModel, $this->userId, $this->sessionId);

        // Assertions
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals(['message' => 'sessionId registered successfully'], $response->getData());
    }

    /**
     * Test registerSessionId with service error
     */
    public function testRegisterSessionIdFailure(): void
    {
        // Configure validator to return no violations
        $violations = $this->createMock(ConstraintViolationListInterface::class);
        $violations->method('count')->willReturn(0);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($this->fcmTokenRegisterModel, null, ['fcmTokenRegister'])
            ->willReturn($violations);

        // Configure register service to return error
        $errorMessage = 'User not found';
        $this->registerService->expects($this->once())
            ->method('registerSessionId')
            ->with($this->fcmTokenRegisterModel, $this->userId, $this->sessionId)
            ->willReturn(new WSResponse(Response::HTTP_NOT_FOUND, $errorMessage));

        // Call the method
        $response = $this->registerManager->registerSessionId($this->fcmTokenRegisterModel, $this->userId, $this->sessionId);

        // Assertions
        $this->assertInstanceOf(ErrorResponse::class, $response);

        // Get the response data using toArray()
        $responseData = $response->toArray();
        $this->assertEquals(Response::HTTP_NOT_FOUND, $responseData['code']);
        $this->assertEquals($errorMessage, $responseData['content']['error']['errors']);
    }

    /**
     * Test registerSessionId with validation error
     */
    public function testRegisterSessionIdValidationError(): void
    {
        // Create a mock ConstraintViolationList that returns a count > 0
        $violations = $this->createMock(ConstraintViolationListInterface::class);
        $violations->method('count')->willReturn(1);

        // Configure validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($this->fcmTokenRegisterModel, null, ['fcmTokenRegister'])
            ->willReturn($violations);

        // Configure logger expectations
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Validation fails for incoming request'));

        // Call the method
        $response = $this->registerManager->registerSessionId($this->fcmTokenRegisterModel, $this->userId, $this->sessionId);

        // Assertions
        $this->assertInstanceOf(ErrorResponse::class, $response);

        // Get the response data using toArray()
        $responseData = $response->toArray();
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $responseData['code']);

        // The errors should be directly in the content.error object
        $this->assertIsArray($responseData['content']['error']);
        $this->assertEquals('validation_failed', $responseData['content']['error']['message']);
    }

    /**
     * Test registerSessionId with exception
     */
    public function testRegisterSessionIdException(): void
    {
        // Configure validator to throw an exception
        $exceptionMessage = 'Database connection error';
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($this->fcmTokenRegisterModel, null, ['fcmTokenRegister'])
            ->willThrowException(new Exception($exceptionMessage, 500));

        // Configure logger expectations
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Catching Exception'));

        // Call the method
        $response = $this->registerManager->registerSessionId($this->fcmTokenRegisterModel, $this->userId, $this->sessionId);

        // Assertions
        $this->assertInstanceOf(ErrorResponse::class, $response);

        // Get the response data using toArray()
        $responseData = $response->toArray();
        $this->assertEquals(500, $responseData['code']);
        $this->assertEquals($exceptionMessage, $responseData['content']['error']['errors']);
    }
}