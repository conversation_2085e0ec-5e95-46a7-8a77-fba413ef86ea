<?php

namespace App\Tests\Manager;

use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use Psr\Log\LoggerInterface;

use App\Manager\UserManager;
use App\Service\UserService;
use App\Helper\WSResponse;


class UserManagerTest extends TestCase
{
    private $logger;
    private $userService;
    private $userManager;
    private $userId;
    private $vin;
    private $sessionId;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->userService = $this->createMock(UserService::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->userManager = new UserManager($this->userService);
        $this->userManager->setLogger($this->logger);

        // Common test data
        $this->userId = '100000000f0b4dce874859657ae00100';
        $this->vin = 'VR3UPHNKSKT101603';
        $this->sessionId = '44dscsbf5d4dsdsdfsasa9657aefgdxc2';
    }

    protected function tearDown(): void
    {
        $this->userService = null;
        $this->logger = null;
        $this->userManager = null;

        parent::tearDown();
    }

    /**
     * Test getUsersBySessionId with all parameters
     */
    public function testGetUsersBySessionIdWithAllParameters(): void
    {
        // Expected result from the service
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"' . $this->userId . '"}]}'
        );

        // Configure service mock
        $this->userService->expects($this->once())
            ->method('getUsersBySessionId')
            ->with($this->userId, $this->vin, $this->sessionId)
            ->willReturn($serviceResult);

        // Configure logger expectations
        $this->logger->expects($this->once())
            ->method('info')
            ->with('Getting users', ['userId' => $this->userId, 'vin' => $this->vin, 'sessionId' => $this->sessionId]);

        // Call the method
        $result = $this->userManager->getUsersBySessionId($this->userId, $this->vin, $this->sessionId);

        // Assertions
        $this->assertSame($serviceResult, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());

        // Verify the response data
        $responseData = json_decode($result->getData(), true);
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('documents', $responseData);
        $this->assertCount(1, $responseData['documents']);
        $this->assertEquals($this->userId, $responseData['documents'][0]['userId']);
    }

    /**
     * Test getUsersBySessionId with null parameters
     */
    public function testGetUsersBySessionIdWithNullParameters(): void
    {
        // Expected result from the service
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"' . $this->userId . '"}]}'
        );

        // Configure service mock
        $this->userService->expects($this->once())
            ->method('getUsersBySessionId')
            ->with(null, null, null)
            ->willReturn($serviceResult);

        // Configure logger expectations
        $this->logger->expects($this->once())
            ->method('info')
            ->with('Getting users', ['userId' => null, 'vin' => null, 'sessionId' => null]);

        // Call the method with null parameters
        $result = $this->userManager->getUsersBySessionId(null, null, null);

        // Assertions
        $this->assertSame($serviceResult, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    /**
     * Test getUsersBySessionId with error response
     */
    public function testGetUsersBySessionIdWithError(): void
    {
        // Error response from the service
        $errorMessage = 'User not found';
        $serviceResult = new WSResponse(
            Response::HTTP_NOT_FOUND,
            '{"error":{"message":"' . $errorMessage . '"}}'
        );

        // Configure service mock
        $this->userService->expects($this->once())
            ->method('getUsersBySessionId')
            ->with($this->userId, $this->vin, $this->sessionId)
            ->willReturn($serviceResult);

        // Call the method
        $result = $this->userManager->getUsersBySessionId($this->userId, $this->vin, $this->sessionId);

        // Assertions
        $this->assertSame($serviceResult, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());

        // Verify the error message
        $responseData = json_decode($result->getData(), true);
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals($errorMessage, $responseData['error']['message']);
    }

    /**
     * Test getUserByUserId with success response
     */
    public function testGetUserByUserIdSuccess(): void
    {
        // Expected result from the service
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"' . $this->userId . '"}]}'
        );

        // Configure service mock
        $this->userService->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn($serviceResult);

        // Configure logger expectations
        $this->logger->expects($this->once())
            ->method('info')
            ->with('Getting user', ['userId' => $this->userId]);

        // Call the method
        $result = $this->userManager->getUserByUserId($this->userId);

        // Assertions
        $this->assertSame($serviceResult, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());

        // Verify the response data
        $responseData = json_decode($result->getData(), true);
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('documents', $responseData);
        $this->assertCount(1, $responseData['documents']);
        $this->assertEquals($this->userId, $responseData['documents'][0]['userId']);
    }

    /**
     * Test getUserByUserId with error response
     */
    public function testGetUserByUserIdWithError(): void
    {
        // Error response from the service
        $errorMessage = 'User not found';
        $serviceResult = new WSResponse(
            Response::HTTP_NOT_FOUND,
            '{"error":{"message":"' . $errorMessage . '"}}'
        );

        // Configure service mock
        $this->userService->expects($this->once())
            ->method('getUserByUserId')
            ->with($this->userId)
            ->willReturn($serviceResult);

        // Call the method
        $result = $this->userManager->getUserByUserId($this->userId);

        // Assertions
        $this->assertSame($serviceResult, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());

        // Verify the error message
        $responseData = json_decode($result->getData(), true);
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals($errorMessage, $responseData['error']['message']);
    }
}
