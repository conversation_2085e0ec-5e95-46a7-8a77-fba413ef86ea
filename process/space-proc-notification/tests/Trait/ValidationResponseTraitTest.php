<?php

namespace App\Tests\Trait;

use App\Helper\ErrorResponse;
use App\Trait\ValidationResponseTrait;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validation;

/**
 * Tests for the ValidationResponseTrait
 *
 * This test suite covers all scenarios for the ValidationResponseTrait:
 * - Getting validation messages from constraint violations
 * - Getting validation error response
 * - Handling empty validation errors
 * - Handling nested property paths
 * - Handling different types of validation errors
 * - Handling custom error messages
 */
class ValidationResponseTraitTest extends TestCase
{
    use ValidationResponseTrait;

    /**
     * Test getting validation messages with basic validation errors
     */
    public function testGetValidationMessages(): void
    {
        $userCvsToken = '';
        $brand = '';
        $validator = Validation::createValidator();
        $errors = $validator->validate(
            compact('userCvsToken', 'brand'),
            new Assert\Collection([
                'userCvsToken' => new Assert\NotBlank(),
                'brand' => new Assert\NotBlank(),
            ])
        );

        $messages = $this->getValidationMessages($errors);
        $expected = [
            'userCvsToken' => 'This value should not be blank.',
            'brand' => 'This value should not be blank.',
        ];

        static::assertEquals($expected, $messages);
        static::assertIsArray($messages);
        static::assertCount(2, $messages);
        static::assertArrayHasKey('userCvsToken', $messages);
        static::assertArrayHasKey('brand', $messages);
    }

    /**
     * Test getting validation error response
     */
    public function testGetValidationErrorResponse(): void
    {
        $userCvsToken = '';
        $brand = '';
        $expected['error'] = [
            'message' => 'validation_failed',
            'errors' => [
                'userCvsToken' => 'This value should not be blank.',
                'brand' => 'This value should not be blank.',
            ],
        ];
        $validator = Validation::createValidator();
        $errors = $validator->validate(
            compact('userCvsToken', 'brand'),
            new Assert\Collection([
                'userCvsToken' => new Assert\NotBlank(),
                'brand' => new Assert\NotBlank(),
            ])
        );

        $messages = $this->getValidationMessages($errors);
        $response = $this->getValidationErrorResponse($messages)->toArray();

        static::assertIsArray($response);
        static::assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response['code']);
        static::assertEquals($expected, $response['content']);

        // Verify the response structure in more detail
        static::assertArrayHasKey('code', $response);
        static::assertArrayHasKey('content', $response);
        static::assertArrayHasKey('error', $response['content']);
        static::assertArrayHasKey('message', $response['content']['error']);
        static::assertArrayHasKey('errors', $response['content']['error']);
        static::assertEquals('validation_failed', $response['content']['error']['message']);
    }

    /**
     * Test getting validation messages with no errors
     */
    public function testGetValidationNoErrors(): void
    {
        $userCvsToken = 'test-token';
        $brand = 'AP';
        $validator = Validation::createValidator();
        $errors = $validator->validate(
            compact('userCvsToken', 'brand'),
            new Assert\Collection([
                'userCvsToken' => new Assert\NotBlank(),
                'brand' => new Assert\NotBlank(),
            ])
        );

        $messages = $this->getValidationMessages($errors);

        static::assertEquals([], $messages);
        static::assertIsArray($messages);
        static::assertCount(0, $messages);
    }

    /**
     * Test getting validation messages with nested property paths
     *
     * Note: This test verifies the behavior of the getValidationMessages method
     * with property paths that contain brackets, simulating nested properties.
     * The method should replace brackets with dots in the property paths.
     */
    public function testGetValidationMessagesWithNestedProperties(): void
    {
        // Create a test class with a method that returns a mock ConstraintViolationListInterface
        $testClass = new class {
            public function createViolationList(): \Symfony\Component\Validator\ConstraintViolationListInterface {
                // Create mock violations
                $violation1 = $this->createViolation('[user][name]', 'This value should not be blank.');
                $violation2 = $this->createViolation('[user][email]', 'This value is not a valid email address.');

                // Create a mock violation list
                return new \Symfony\Component\Validator\ConstraintViolationList([$violation1, $violation2]);
            }

            private function createViolation(string $path, string $message): \Symfony\Component\Validator\ConstraintViolationInterface {
                return new \Symfony\Component\Validator\ConstraintViolation(
                    $message,
                    '',
                    [],
                    null,
                    $path,
                    null
                );
            }
        };

        // Get the violation list
        $violationList = $testClass->createViolationList();

        // Call the method under test
        $messages = $this->getValidationMessages($violationList);

        // Verify the results
        static::assertIsArray($messages);
        static::assertCount(2, $messages);
        static::assertArrayHasKey('username', $messages);
        static::assertArrayHasKey('useremail', $messages);
        static::assertEquals('This value should not be blank.', $messages['username']);
        static::assertEquals('This value is not a valid email address.', $messages['useremail']);
    }

    /**
     * Test getting validation messages with different types of validation errors
     */
    public function testGetValidationMessagesWithDifferentErrorTypes(): void
    {
        // Use a real validator with different types of validation errors
        $data = [
            'age' => 10,
            'email' => 'invalid-email',
            'url' => 'invalid-url',
            'choice' => 'invalid-choice'
        ];

        $validator = Validation::createValidator();
        $constraint = new Assert\Collection([
            'age' => new Assert\Range(['min' => 18, 'max' => 100]),
            'email' => new Assert\Email(),
            'url' => new Assert\Url(),
            'choice' => new Assert\Choice(['choices' => ['option1', 'option2']])
        ]);

        $errors = $validator->validate($data, $constraint);

        // Extract the property paths and messages manually for verification
        $expectedMessages = [];
        foreach ($errors as $error) {
            $path = str_replace(['[', ']'], '', $error->getPropertyPath());
            $expectedMessages[$path] = $error->getMessage();
        }

        $messages = $this->getValidationMessages($errors);

        static::assertIsArray($messages);
        static::assertCount(count($expectedMessages), $messages);

        // Verify that each expected message is in the result
        foreach ($expectedMessages as $path => $message) {
            static::assertArrayHasKey($path, $messages);
            static::assertEquals($message, $messages[$path]);
        }

        // Verify specific paths are present
        static::assertArrayHasKey('age', $messages);
        static::assertArrayHasKey('email', $messages);
        static::assertArrayHasKey('url', $messages);
        static::assertArrayHasKey('choice', $messages);
    }

    /**
     * Test getting validation messages with a mix of valid and invalid properties
     */
    public function testGetValidationMessagesWithMixedValidAndInvalidProperties(): void
    {
        // Use a real validator with a mix of valid and invalid properties
        $data = [
            'validField' => 'valid-value',
            'invalidField' => ''
        ];

        $validator = Validation::createValidator();
        $constraint = new Assert\Collection([
            'validField' => new Assert\NotBlank(),
            'invalidField' => new Assert\NotBlank()
        ]);

        $errors = $validator->validate($data, $constraint);

        $messages = $this->getValidationMessages($errors);

        static::assertIsArray($messages);
        static::assertCount(1, $messages);
        static::assertArrayHasKey('invalidField', $messages);
        static::assertArrayNotHasKey('validField', $messages);
        static::assertEquals('This value should not be blank.', $messages['invalidField']);
    }

    /**
     * Test getting validation messages with custom error messages
     */
    public function testGetValidationMessagesWithCustomErrorMessages(): void
    {
        // Use a real validator with custom error messages
        $data = [
            'username' => '',
            'password' => 'short'
        ];

        $validator = Validation::createValidator();
        $constraint = new Assert\Collection([
            'username' => new Assert\NotBlank([
                'message' => 'Username cannot be empty'
            ]),
            'password' => new Assert\Length([
                'min' => 8,
                'minMessage' => 'Password must be at least {{ limit }} characters long'
            ])
        ]);

        $errors = $validator->validate($data, $constraint);

        $messages = $this->getValidationMessages($errors);

        static::assertIsArray($messages);
        static::assertCount(2, $messages);
        static::assertArrayHasKey('username', $messages);
        static::assertArrayHasKey('password', $messages);
        static::assertEquals('Username cannot be empty', $messages['username']);
        static::assertEquals('Password must be at least 8 characters long', $messages['password']);
    }

    /**
     * Test the error response structure in more detail
     */
    public function testErrorResponseStructure(): void
    {
        $messages = [
            'field1' => 'Error 1',
            'field2' => 'Error 2'
        ];

        $response = $this->getValidationErrorResponse($messages);

        // Verify the response is an ErrorResponse object
        static::assertInstanceOf(ErrorResponse::class, $response);

        // Verify the response array structure
        $responseArray = $response->toArray();
        static::assertIsArray($responseArray);
        static::assertArrayHasKey('code', $responseArray);
        static::assertArrayHasKey('content', $responseArray);

        // Verify the response code
        static::assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $responseArray['code']);

        // Verify the content structure
        static::assertArrayHasKey('error', $responseArray['content']);
        static::assertArrayHasKey('message', $responseArray['content']['error']);
        static::assertArrayHasKey('errors', $responseArray['content']['error']);

        // Verify the error message
        static::assertEquals('validation_failed', $responseArray['content']['error']['message']);

        // Verify the errors array
        static::assertIsArray($responseArray['content']['error']['errors']);
        static::assertCount(2, $responseArray['content']['error']['errors']);
        static::assertArrayHasKey('field1', $responseArray['content']['error']['errors']);
        static::assertArrayHasKey('field2', $responseArray['content']['error']['errors']);
        static::assertEquals('Error 1', $responseArray['content']['error']['errors']['field1']);
        static::assertEquals('Error 2', $responseArray['content']['error']['errors']['field2']);
    }
}
