<?php

namespace App\Tests\Service;

use App\Service\NotificationService;
use App\Helper\WSResponse;
use App\Service\MongoAtlasQueryService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Uid\Uuid;

/**
 * Tests for the NotificationService class
 *
 * This test suite covers all scenarios for the NotificationService:
 * - Getting vehicle notifications
 * - Getting user vehicle notifications
 * - Building notification filters
 * - Saving notifications (both user and vehicle types)
 * - Formatting notifications for XF format
 * - Error handling
 * - Edge cases (invalid types, empty messages, error responses)
 */
class NotificationServiceTest extends TestCase
{
    private $mongoServiceMock;
    private $serializerMock;
    private $notificationService;
    private $loggerMock;
    private string $userId;
    private string $vin;
    private array $vehicleMessage;
    private array $userMessage;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->mongoServiceMock = $this->createMock(MongoAtlasQueryService::class);
        $this->serializerMock = $this->createMock(SerializerInterface::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        $this->notificationService = new NotificationService(
            $this->serializerMock,
            $this->mongoServiceMock
        );

        $this->notificationService->setLogger($this->loggerMock);

        // Common test data
        $this->userId = '111ef5d49f0b4dce874859657ae98122';
        $this->vin = '1HGBH41JXMN109186';

        // Vehicle notification message
        $this->vehicleMessage = [
            'notification' => [
                'title' => 'Vehicle location',
                'body' => 'Vehicle is in India',
                'subtitle' => 'Vehicle Status',
                'bannerTitle' => 'Location Update',
                'bannerDesc' => 'Your vehicle location has been updated'
            ],
            'criticality' => 'high',
            'data' => [
                'serviceData' => [
                    'EventID' => 'GenericNotificationEvent',
                    'Version' => '1.0',
                    'Timestamp' => *************,
                    'Data' => [
                        'notificationId' => 'S12_RO_START_FAILED_TIMEOUT',
                        'eventName' => 'HORN_AND_LIGHTS'
                    ]
                ],
                'context' => [
                    'nickname' => 'MY CAR',
                    'model' => 'AR',
                    'brandMarketingName' => 'ALFA ROMEO',
                    'firstName' => 'John'
                ]
            ],
            'apiPushConfig' => [
                'staticConfig' => [
                    'android' => [
                        'notification' => [
                            'click_action' => 'OPEN_ACTIVITY_1'
                        ]
                    ]
                ]
            ]
        ];

        // User notification message
        $this->userMessage = [
            'notification' => [
                'title' => 'Account Update',
                'body' => 'Your account has been updated',
                'subtitle' => 'Account Status',
                'bannerTitle' => 'Account Update',
                'bannerDesc' => 'Your account information has been updated'
            ],
            'criticality' => 'medium',
            'data' => [
                'serviceData' => [
                    'EventID' => 'AccountUpdateEvent',
                    'Version' => '1.0',
                    'Timestamp' => *************,
                    'Data' => [
                        'notificationId' => 'ACCOUNT_UPDATE',
                        'eventName' => 'ACCOUNT_UPDATE'
                    ]
                ],
                'context' => [
                    'nickname' => 'MY CAR',
                    'model' => 'AR',
                    'brandMarketingName' => 'ALFA ROMEO',
                    'firstName' => 'John'
                ]
            ],
            'apiPushConfig' => [
                'staticConfig' => [
                    'android' => [
                        'notification' => [
                            'click_action' => 'OPEN_ACCOUNT'
                        ]
                    ]
                ]
            ],
            'serviceData' => [
                'Data' => [
                    'eventName' => 'ACCOUNT_UPDATE'
                ]
            ]
        ];
    }

    /**
     * Test getting vehicle notifications
     */
    public function testGetVehicleNotifications(): void
    {
        $data = [
            'since' => *************,
            'till' => *************,
            'offset' => 0,
            'limit' => 10
        ];

        // Mock the pipeline that should be generated
        $expectedPipeline = $this->notificationService->getVehicleNotificationsFilter($this->userId, $this->vin, $data);

        // Mock the response from the MongoDB service
        $mockResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [
                [
                    'totalResults' => 5,
                    'offset' => 0,
                    'count' => 5,
                    'vin' => $this->vin,
                    'items' => [
                        // Notification items would be here
                    ]
                ]
            ]
        ]));

        // Set up expectations for the mongoService aggregate method
        $this->mongoServiceMock
            ->expects($this->once())
            ->method('aggregate')
            ->with(NotificationService::COLLECTION, $expectedPipeline)
            ->willReturn($mockResponse);

        // Call the method under test
        $result = $this->notificationService->getVehicleNotifications($this->userId, $this->vin, $data);

        // Verify the result
        $this->assertSame($mockResponse, $result);
    }

    /**
     * Test building vehicle notifications filter with all parameters
     */
    public function testGetVehicleNotificationsFilterWithAllParameters(): void
    {
        $data = [
            'since' => *************,
            'till' => *************,
            'offset' => 10,
            'limit' => 20
        ];

        $pipeline = $this->notificationService->getVehicleNotificationsFilter($this->userId, $this->vin, $data);

        // Verify the pipeline structure
        $this->assertIsArray($pipeline);
        $this->assertCount(8, $pipeline); // Actual implementation has 8 pipeline stages

        // Check the match condition for userId
        $this->assertEquals(['$match' => ['userId' => $this->userId]], $pipeline[0]);

        // Check the unwind operation
        $this->assertEquals(['$unwind' => '$messages'], $pipeline[1]);

        // Check the match condition for vin
        $this->assertEquals(['$match' => ['messages.vin' => $this->vin]], $pipeline[2]);

        // Check the since timestamp match
        $this->assertEquals(
            ['$match' => ['messages.notificationData.timestamp' => ['$gte' => (int)$data['since']]]],
            $pipeline[3]
        );

        // Check the till timestamp match
        $this->assertEquals(
            ['$match' => ['messages.notificationData.timestamp' => ['$lte' => (int)$data['till']]]],
            $pipeline[4]
        );

        // Check the sort operation
        $this->assertEquals(
            ['$sort' => ['messages.notificationData.timestamp' => -1]],
            $pipeline[5]
        );

        // Check the facet operation (pagination)
        $this->assertArrayHasKey('$facet', $pipeline[6]);
        $this->assertArrayHasKey('totalCount', $pipeline[6]['$facet']);
        $this->assertArrayHasKey('paginatedResults', $pipeline[6]['$facet']);

        // Check the pagination parameters
        $this->assertEquals(
            ['$skip' => 10],
            $pipeline[6]['$facet']['paginatedResults'][0]
        );
        $this->assertEquals(
            ['$limit' => 20],
            $pipeline[6]['$facet']['paginatedResults'][1]
        );
    }

    /**
     * Test building vehicle notifications filter with minimal parameters
     */
    public function testGetVehicleNotificationsFilterWithMinimalParameters(): void
    {
        $data = [];

        $pipeline = $this->notificationService->getVehicleNotificationsFilter($this->userId, $this->vin, $data);

        // Verify the pipeline structure
        $this->assertIsArray($pipeline);
        $this->assertCount(6, $pipeline); // No since/till filters, but actual implementation has 6 pipeline stages

        // Check the match condition for userId
        $this->assertEquals(['$match' => ['userId' => $this->userId]], $pipeline[0]);

        // Check the unwind operation
        $this->assertEquals(['$unwind' => '$messages'], $pipeline[1]);

        // Check the match condition for vin
        $this->assertEquals(['$match' => ['messages.vin' => $this->vin]], $pipeline[2]);

        // Check the sort operation
        $this->assertEquals(
            ['$sort' => ['messages.notificationData.timestamp' => -1]],
            $pipeline[3]
        );

        // Check the default pagination parameters
        $this->assertEquals(
            ['$skip' => 0],
            $pipeline[4]['$facet']['paginatedResults'][0]
        );
        $this->assertEquals(
            ['$limit' => 10],
            $pipeline[4]['$facet']['paginatedResults'][1]
        );
    }

    /**
     * Test building vehicle notifications filter with empty vin
     */
    public function testGetVehicleNotificationsFilterWithEmptyVin(): void
    {
        $data = [];
        $emptyVin = '';

        $pipeline = $this->notificationService->getVehicleNotificationsFilter($this->userId, $emptyVin, $data);

        // Verify the pipeline structure
        $this->assertIsArray($pipeline);
        $this->assertCount(5, $pipeline); // No vin filter, but actual implementation has 5 pipeline stages

        // Check the match condition for userId
        $this->assertEquals(['$match' => ['userId' => $this->userId]], $pipeline[0]);

        // Check the unwind operation
        $this->assertEquals(['$unwind' => '$messages'], $pipeline[1]);

        // Check that there's no match condition for vin
        $this->assertEquals(
            ['$sort' => ['messages.notificationData.timestamp' => -1]],
            $pipeline[2]
        );
    }

    /**
     * Test getting user vehicle notifications
     */
    public function testGetUserVehicleNotifications(): void
    {
        $data = [
            'since' => *************,
            'till' => *************
        ];

        // Mock the pipeline that should be generated
        $expectedPipeline = $this->notificationService->getUserNotificationFilter($this->userId, $data);

        // Mock the response from the MongoDB service
        $mockResponse = new WSResponse(Response::HTTP_OK, json_encode([
            'documents' => [
                [
                    'vin' => $this->vin,
                    'count' => 5,
                    'items' => [
                        // Notification items would be here
                    ]
                ]
            ]
        ]));

        // Set up expectations for the mongoService aggregate method
        $this->mongoServiceMock
            ->expects($this->once())
            ->method('aggregate')
            ->with(NotificationService::COLLECTION, $expectedPipeline)
            ->willReturn($mockResponse);

        // Call the method under test
        $result = $this->notificationService->getUserVehicleNotifications($this->userId, $data);

        // Verify the result
        $this->assertSame($mockResponse, $result);
    }

    /**
     * Test building user notification filter with all parameters
     */
    public function testGetUserNotificationFilterWithAllParameters(): void
    {
        $data = [
            'since' => *************,
            'till' => *************
        ];

        $pipeline = $this->notificationService->getUserNotificationFilter($this->userId, $data);

        // Verify the pipeline structure
        $this->assertIsArray($pipeline);
        $this->assertCount(7, $pipeline); // Actual implementation has 7 pipeline stages

        // Check the match condition for userId
        $this->assertEquals(['$match' => ['userId' => $this->userId]], $pipeline[0]);

        // Check the unwind operation
        $this->assertEquals(['$unwind' => '$messages'], $pipeline[1]);

        // Check the since timestamp match
        $this->assertEquals(
            ['$match' => ['messages.notificationData.timestamp' => ['$gte' => (int)$data['since']]]],
            $pipeline[2]
        );

        // Check the till timestamp match
        $this->assertEquals(
            ['$match' => ['messages.notificationData.timestamp' => ['$lte' => (int)$data['till']]]],
            $pipeline[3]
        );

        // Check the sort operation
        $this->assertEquals(
            ['$sort' => ['messages.notificationData.timestamp' => -1]],
            $pipeline[4]
        );

        // Check the group operation
        $this->assertArrayHasKey('$group', $pipeline[5]);
        $this->assertEquals('$messages.notificationData.vin', $pipeline[5]['$group']['_id']);
    }

    /**
     * Test building user notification filter with minimal parameters
     */
    public function testGetUserNotificationFilterWithMinimalParameters(): void
    {
        $data = [];

        $pipeline = $this->notificationService->getUserNotificationFilter($this->userId, $data);

        // Verify the pipeline structure
        $this->assertIsArray($pipeline);
        $this->assertCount(5, $pipeline); // No since/till filters, but actual implementation has 5 pipeline stages

        // Check the match condition for userId
        $this->assertEquals(['$match' => ['userId' => $this->userId]], $pipeline[0]);

        // Check the unwind operation
        $this->assertEquals(['$unwind' => '$messages'], $pipeline[1]);

        // Check the sort operation
        $this->assertEquals(
            ['$sort' => ['messages.notificationData.timestamp' => -1]],
            $pipeline[2]
        );

        // Check the group operation
        $this->assertArrayHasKey('$group', $pipeline[3]);
    }

    /**
     * Test saving vehicle notification successfully
     */
    public function testSaveVehicleNotificationSuccess(): void
    {
        // We'll use the real vehicleXfFormattedNotification method
        // and just mock the mongoService to return our expected response

        // Create a mock WSResponse
        $wsResponseMock = $this->createMock(WSResponse::class);
        $wsResponseMock->method('getCode')->willReturn(Response::HTTP_OK);
        $wsResponseMock->method('getData')->willReturn(json_encode(['success' => true]));

        // Set up expectations for the mongoService updatePush method
        $this->mongoServiceMock
            ->expects($this->once())
            ->method('updatePush')
            ->with(
                NotificationService::COLLECTION,
                ['userId' => $this->userId],
                $this->callback(function ($messages) {
                    // Just check that the messages array exists
                    return isset($messages['messages']);
                }),
                true
            )
            ->willReturn($wsResponseMock);

        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Notifications successfully saved', $this->anything());

        // Call the method under test
        $result = $this->notificationService->saveNotification(
            NotificationService::NOTIFICATION_TYPE_VEHICLE,
            $this->userId,
            $this->vehicleMessage,
            $this->vin
        );

        // Verify the result matches the mock response
        $this->assertSame($wsResponseMock, $result);
    }

    /**
     * Test saving user notification successfully
     */
    public function testSaveUserNotificationSuccess(): void
    {
        // We'll use the real userXfFormattedNotification method
        // and just mock the mongoService to return our expected response

        // Create a mock WSResponse
        $wsResponseMock = $this->createMock(WSResponse::class);
        $wsResponseMock->method('getCode')->willReturn(Response::HTTP_OK);
        $wsResponseMock->method('getData')->willReturn(json_encode(['success' => true]));

        // Set up expectations for the mongoService updatePush method
        $this->mongoServiceMock
            ->expects($this->once())
            ->method('updatePush')
            ->with(
                NotificationService::COLLECTION,
                ['userId' => $this->userId],
                $this->callback(function ($messages) {
                    // Just check that the messages array exists
                    return isset($messages['messages']);
                }),
                true
            )
            ->willReturn($wsResponseMock);

        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Notifications successfully saved', $this->anything());

        // Call the method under test
        $result = $this->notificationService->saveNotification(
            NotificationService::NOTIFICATION_TYPE_USER,
            $this->userId,
            $this->userMessage
        );

        // Verify the result matches the mock response
        $this->assertSame($wsResponseMock, $result);
    }

    /**
     * Test saving notification with exception
     */
    public function testSaveNotificationWithException(): void
    {
        // Simulate an exception in the mongoService updatePush method
        $this->mongoServiceMock
            ->expects($this->once())
            ->method('updatePush')
            ->willThrowException(new \Exception('Database error'));

        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('error')
            ->with('Failed to save notifications due to an exception: Database error', [
                'userId' => $this->userId,
                'messages' => $this->vehicleMessage,
            ]);

        // Call the method under test
        $result = $this->notificationService->saveNotification(
            NotificationService::NOTIFICATION_TYPE_VEHICLE,
            $this->userId,
            $this->vehicleMessage,
            $this->vin
        );

        // Verify the result is null
        $this->assertNull($result);
    }

    /**
     * Test saving notification with invalid notification type
     */
    public function testSaveNotificationWithInvalidType(): void
    {
        // Create a mock WSResponse
        $wsResponseMock = $this->createMock(WSResponse::class);
        $wsResponseMock->method('getCode')->willReturn(Response::HTTP_OK);
        $wsResponseMock->method('getData')->willReturn(json_encode(['success' => true]));

        // Set up expectations for the mongoService updatePush method
        $this->mongoServiceMock
            ->expects($this->once())
            ->method('updatePush')
            ->willReturn($wsResponseMock);

        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Notifications successfully saved', $this->anything());

        // Call the method under test with an invalid type
        $result = $this->notificationService->saveNotification(
            'invalid_type',
            $this->userId,
            $this->vehicleMessage,
            $this->vin
        );

        // Verify the result matches the mock response
        $this->assertSame($wsResponseMock, $result);

        // The notification should still be saved, but the formattedMessage will be empty
    }

    /**
     * Test saving notification with empty message
     */
    public function testSaveNotificationWithEmptyMessage(): void
    {
        // When an empty message is passed, the method will throw an exception
        // because it tries to access array keys that don't exist

        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Failed to save notifications due to an exception:'), $this->anything());

        // Call the method under test with an empty message
        $result = $this->notificationService->saveNotification(
            NotificationService::NOTIFICATION_TYPE_VEHICLE,
            $this->userId,
            [],
            $this->vin
        );

        // Verify the result is null due to the exception
        $this->assertNull($result);
    }

    /**
     * Test saving notification with error response from MongoDB
     */
    public function testSaveNotificationWithErrorResponse(): void
    {
        // Create a mock WSResponse with an error
        $wsResponseMock = $this->createMock(WSResponse::class);
        $wsResponseMock->method('getCode')->willReturn(Response::HTTP_INTERNAL_SERVER_ERROR);
        $wsResponseMock->method('getData')->willReturn('Database error');

        // Set up expectations for the mongoService updatePush method
        $this->mongoServiceMock
            ->expects($this->once())
            ->method('updatePush')
            ->willReturn($wsResponseMock);

        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Notifications successfully saved', $this->anything());

        // Call the method under test
        $result = $this->notificationService->saveNotification(
            NotificationService::NOTIFICATION_TYPE_VEHICLE,
            $this->userId,
            $this->vehicleMessage,
            $this->vin
        );

        // Verify the result matches the mock response
        $this->assertSame($wsResponseMock, $result);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $result->getCode());
        $this->assertEquals('Database error', $result->getData());
    }

    /**
     * Test formatting user notification for XF format
     */
    public function testUserXfFormattedNotification(): void
    {
        // Call the method under test
        $result = $this->notificationService->userXfFormattedNotification(
            $this->userId,
            $this->userMessage
        );

        // Verify the result structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('notification', $result);
        $this->assertArrayHasKey('timestamp', $result);
        $this->assertArrayHasKey('vin', $result);

        // Verify the notification structure
        $this->assertIsArray($result['notification']);
        $this->assertArrayHasKey('context', $result['notification']);
        $this->assertArrayHasKey('in-app', $result['notification']);
        $this->assertArrayHasKey('data', $result['notification']);
        $this->assertArrayHasKey('click_action', $result['notification']);

        // Verify specific values
        $this->assertInstanceOf(Uuid::class, Uuid::fromString($result['id']));
        $this->assertEquals('ACCOUNT_UPDATE', $result['vin']);
        $this->assertEquals($this->userId, $result['notification']['data']['userId']);
        $this->assertEquals('ACCOUNT_UPDATE', $result['notification']['data']['notificationId']);
        $this->assertEquals('medium', $result['notification']['in-app']['criticality']);
        $this->assertEquals('Account Update', $result['notification']['in-app']['title']);
        $this->assertEquals('Your account has been updated', $result['notification']['in-app']['body']);
        $this->assertEquals('Account Status', $result['notification']['in-app']['subtitle']);
        $this->assertEquals('OPEN_ACCOUNT', $result['notification']['click_action']);
        $this->assertEquals(*************, $result['timestamp']);
    }

    /**
     * Test formatting user notification with minimal data
     */
    public function testUserXfFormattedNotificationWithMinimalData(): void
    {
        // Create a minimal message with only required fields
        $minimalMessage = [
            'criticality' => 'low',
            'serviceData' => [
                'Data' => [
                    'eventName' => 'MINIMAL_EVENT'
                ]
            ]
        ];

        // Call the method under test
        $result = $this->notificationService->userXfFormattedNotification(
            $this->userId,
            $minimalMessage
        );

        // Verify the result structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('notification', $result);
        $this->assertArrayHasKey('vin', $result);

        // Verify the notification structure
        $this->assertIsArray($result['notification']);
        $this->assertArrayHasKey('context', $result['notification']);
        $this->assertArrayHasKey('in-app', $result['notification']);
        $this->assertArrayHasKey('data', $result['notification']);

        // Verify specific values
        $this->assertInstanceOf(Uuid::class, Uuid::fromString($result['id']));
        $this->assertEquals('MINIMAL_EVENT', $result['vin']);
        $this->assertEquals($this->userId, $result['notification']['data']['userId']);
        $this->assertEquals('', $result['notification']['data']['notificationId']);
        $this->assertEquals('low', $result['notification']['in-app']['criticality']);
        $this->assertEquals('', $result['notification']['in-app']['title']);
        $this->assertEquals('', $result['notification']['in-app']['body']);
        $this->assertEquals('', $result['notification']['in-app']['subtitle']);
        $this->assertEquals('', $result['notification']['click_action']);
        $this->assertNull($result['timestamp']);
    }

    /**
     * Test formatting vehicle notification for XF format
     */
    public function testVehicleXfFormattedNotification(): void
    {
        // Call the method under test
        $result = $this->notificationService->vehicleXfFormattedNotification(
            $this->userId,
            $this->vehicleMessage,
            $this->vin
        );

        // Verify the result structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('notification', $result);
        $this->assertArrayHasKey('timestamp', $result);
        $this->assertArrayHasKey('vin', $result);
        $this->assertArrayHasKey('eventName', $result);

        // Verify the notification structure
        $this->assertIsArray($result['notification']);
        $this->assertArrayHasKey('context', $result['notification']);
        $this->assertArrayHasKey('in-app', $result['notification']);
        $this->assertArrayHasKey('data', $result['notification']);
        $this->assertArrayHasKey('click_action', $result['notification']);

        // Verify specific values
        $this->assertInstanceOf(Uuid::class, Uuid::fromString($result['id']));
        $this->assertEquals($this->vin, $result['vin']);
        $this->assertEquals('HORN_AND_LIGHTS', $result['eventName']);
        $this->assertEquals($this->userId, $result['notification']['data']['userId']);
        $this->assertEquals('S12_RO_START_FAILED_TIMEOUT', $result['notification']['data']['notificationId']);
        $this->assertEquals('high', $result['notification']['in-app']['criticality']);
        $this->assertEquals('Vehicle location', $result['notification']['in-app']['title']);
        $this->assertEquals('Vehicle is in India', $result['notification']['in-app']['body']);
        $this->assertEquals('Vehicle Status', $result['notification']['in-app']['subtitle']);
        $this->assertEquals('OPEN_ACTIVITY_1', $result['notification']['click_action']);
        $this->assertEquals(*************, $result['timestamp']);
    }

    /**
     * Test formatting vehicle notification with minimal data
     */
    public function testVehicleXfFormattedNotificationWithMinimalData(): void
    {
        // Create a minimal message with only required fields
        $minimalMessage = [
            'criticality' => 'low',
            'data' => [
                'serviceData' => [
                    'Data' => []
                ]
            ]
        ];

        // Call the method under test
        $result = $this->notificationService->vehicleXfFormattedNotification(
            $this->userId,
            $minimalMessage,
            $this->vin
        );

        // Verify the result structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('notification', $result);
        $this->assertArrayHasKey('vin', $result);

        // Verify the notification structure
        $this->assertIsArray($result['notification']);
        $this->assertArrayHasKey('context', $result['notification']);
        $this->assertArrayHasKey('in-app', $result['notification']);
        $this->assertArrayHasKey('data', $result['notification']);

        // Verify specific values
        $this->assertInstanceOf(Uuid::class, Uuid::fromString($result['id']));
        $this->assertEquals($this->vin, $result['vin']);
        $this->assertEquals('', $result['eventName']);
        $this->assertEquals($this->userId, $result['notification']['data']['userId']);
        $this->assertEquals('', $result['notification']['data']['notificationId']);
        $this->assertEquals('low', $result['notification']['in-app']['criticality']);
        $this->assertEquals('', $result['notification']['in-app']['title']);
        $this->assertEquals('', $result['notification']['in-app']['body']);
        $this->assertEquals('', $result['notification']['in-app']['subtitle']);
        $this->assertEquals('', $result['notification']['click_action']);
        $this->assertNull($result['timestamp']);
    }
}
