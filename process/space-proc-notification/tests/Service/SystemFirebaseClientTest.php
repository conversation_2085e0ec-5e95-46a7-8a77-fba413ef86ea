<?php

namespace App\Tests\Service;

use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Psr\Log\LoggerInterface;

use App\Connector\SystemFirebaseConnector;
use App\Service\SystemFirebaseClient;
use App\Helper\WSResponse;

/**
 * Tests for the SystemFirebaseClient class
 *
 * This test suite covers all scenarios for the SystemFirebaseClient:
 * - Sending notifications with vehicle notification fields
 * - Sending notifications with user notification fields
 * - Handling empty users array
 * - Handling users without push details
 * - Handling users with empty push details
 * - Handling users with push details but missing pushToken
 * - Handling multiple users with multiple push details
 * - Handling error responses from the connector
 */
class SystemFirebaseClientTest extends TestCase
{
    private LoggerInterface $logger;
    private SystemFirebaseConnector $connector;
    private SystemFirebaseClient $systemFirebaseClient;
    private array $standardMessage;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->connector = $this->createMock(SystemFirebaseConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        // Allow any number of info and error log calls
        $this->logger->expects($this->any())
            ->method('info');
        $this->logger->expects($this->any())
            ->method('error');

        $this->systemFirebaseClient = new SystemFirebaseClient($this->connector);
        $this->systemFirebaseClient->setLogger($this->logger);

        // Standard notification message used in most tests
        $this->standardMessage = [
            'notification' => [
                'title' => 'Vehicle location',
                'body' => 'Vehicle is in India'
            ],
            'data' => [
                'serviceData' => [
                    'EventID' => 'GenericNotificationEvent',
                    'Version' => '1.0',
                    'VehicleId' => 'v38uihwnjsfns',
                    'Timestamp' => 1539179391201,
                    'Data' => [
                        'notificationId' => 'S12_RO_START_FAILED_TIMEOUT',
                        'eventName' => 'HORN_AND_LIGHTS',
                        'flatAttrA' => '18.58483868',
                        'flatAttrB' => 'Boundary 9 Oct',
                        'flatAttrC' => '3bca9be2-fb5d-430a-9bb7-4ce6c7f9a474',
                        'flatAttrD' => '73.73466107'
                    ]
                ],
                'context' => [
                    'nickname' => 'MY CAR',
                    'model' => 'AR',
                    'brandMarketingName' => 'ALFA ROMEO',
                    'firstName' => 'John'
                ]
            ],
            'criticality' => 'high',
            'apiPushConfig' => [
                'staticConfig' => [
                    'silent' => true,
                    'inApp' => true,
                    'push' => true,
                    'android' => [
                        'ttl' => 'string',
                        'notification' => [
                            'click_action' => 'string'
                        ],
                        'priority' => 'string'
                    ],
                    'apns' => [
                        'headers' => [
                            'apns-priority' => 'string'
                        ],
                        'payload' => [
                            'aps' => [
                                'category' => 'string'
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Test sending notification with vehicle notification field
     */
    public function testSendNotificationWithVehicleNotificationField(): void
    {
        $users = [
            [
                '_id' => 'pushDetails',
                'push' => [
                    [
                        "deviceId" => "AZERR",
                        "pushToken" => "1234566",
                        "appId" => "appspace",
                        "timestamp" => "123456677",
                        "commandSession" => [
                            [
                                "id" => "12345",
                                "timestamp" => "2344"
                            ],
                            [
                                "id" => "12345",
                                "timestamp" => "123333"
                            ]
                        ]
                    ]
                ],
            ],
        ];

        // Verify that the connector's call method is called exactly once
        $this->connector->expects($this->exactly(1))
            ->method('call')
            ->with(
                Request::METHOD_POST,
                '/v1/push/notification',
                $this->callback(function($options) {
                    // Verify the options structure
                    return isset($options['headers']['pushToken']) &&
                           $options['headers']['pushToken'] === '1234566' &&
                           isset($options['json']['message']) &&
                           $options['json']['message'] === $this->standardMessage;
                })
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, ''));

        $response = $this->systemFirebaseClient->sendNotification($users, $this->standardMessage);

        $this->assertTrue($response);
    }

    /**
     * Test sending notification with user notification field
     */
    public function testSendNotificationWithUserNotificationField(): void
    {
        $users = [
            [
                '_id' => 'pushDetails',
                'push' => [
                    [
                        "deviceId" => "AZERR",
                        "pushToken" => "1234566",
                        "appId" => "appspace",
                        "timestamp" => "123456677",
                        "commandSession" => [
                            [
                                "id" => "12345",
                                "timestamp" => "2344"
                            ],
                            [
                                "id" => "12345",
                                "timestamp" => "123333"
                            ]
                        ]
                    ]
                ],
            ],
        ];

        // Create a user notification message
        $userMessage = $this->standardMessage;
        $userMessage['data']['serviceData']['Data']['eventName'] = 'USER_NOTIFICATION';

        // Verify that the connector's call method is called exactly once
        $this->connector->expects($this->exactly(1))
            ->method('call')
            ->with(
                Request::METHOD_POST,
                '/v1/push/notification',
                $this->callback(function($options) use ($userMessage) {
                    // Verify the options structure
                    return isset($options['headers']['pushToken']) &&
                           $options['headers']['pushToken'] === '1234566' &&
                           isset($options['json']['message']) &&
                           $options['json']['message'] === $userMessage;
                })
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, ''));

        $response = $this->systemFirebaseClient->sendNotification($users, $userMessage);

        $this->assertTrue($response);
    }

    /**
     * Test sending notification with empty users array
     */
    public function testSendNotificationWithEmptyUsers(): void
    {
        $users = [];

        // Verify that the connector's call method is never called
        $this->connector->expects($this->never())
            ->method('call');

        $response = $this->systemFirebaseClient->sendNotification($users, $this->standardMessage);

        $this->assertTrue($response);
    }

    /**
     * Test sending notification with users without push details
     */
    public function testSendNotificationWithUsersWithoutPushDetails(): void
    {
        $users = [
            [
                '_id' => 'pushDetails',
                // No push field
            ],
        ];

        // Verify that the connector's call method is never called
        $this->connector->expects($this->never())
            ->method('call');

        $response = $this->systemFirebaseClient->sendNotification($users, $this->standardMessage);

        $this->assertTrue($response);
    }

    /**
     * Test sending notification with users with empty push details
     */
    public function testSendNotificationWithUsersWithEmptyPushDetails(): void
    {
        $users = [
            [
                '_id' => 'pushDetails',
                'push' => [], // Empty push array
            ],
        ];

        // Verify that the connector's call method is never called
        $this->connector->expects($this->never())
            ->method('call');

        $response = $this->systemFirebaseClient->sendNotification($users, $this->standardMessage);

        $this->assertTrue($response);
    }

    /**
     * Test sending notification with users with push details but missing pushToken
     */
    public function testSendNotificationWithMissingPushToken(): void
    {
        $users = [
            [
                '_id' => 'pushDetails',
                'push' => [
                    [
                        "deviceId" => "AZERR",
                        // No pushToken field
                        "appId" => "appspace",
                        "timestamp" => "123456677"
                    ]
                ],
            ],
        ];

        // Verify that the connector's call method is called exactly once
        $this->connector->expects($this->exactly(1))
            ->method('call')
            ->with(
                Request::METHOD_POST,
                '/v1/push/notification',
                $this->callback(function($options) {
                    // Verify the options structure has an empty pushToken
                    return isset($options['headers']['pushToken']) &&
                           $options['headers']['pushToken'] === '' &&
                           isset($options['json']['message']);
                })
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, ''));

        $response = $this->systemFirebaseClient->sendNotification($users, $this->standardMessage);

        $this->assertTrue($response);
    }

    /**
     * Test sending notification with multiple users with multiple push details
     */
    public function testSendNotificationWithMultipleUsersAndPushDetails(): void
    {
        $users = [
            [
                '_id' => 'user1',
                'push' => [
                    [
                        "deviceId" => "device1",
                        "pushToken" => "token1",
                        "appId" => "appspace"
                    ],
                    [
                        "deviceId" => "device2",
                        "pushToken" => "token2",
                        "appId" => "appspace"
                    ]
                ],
            ],
            [
                '_id' => 'user2',
                'push' => [
                    [
                        "deviceId" => "device3",
                        "pushToken" => "token3",
                        "appId" => "appspace"
                    ]
                ],
            ],
        ];

        // Verify that the connector's call method is called exactly 3 times (once for each push detail)
        $this->connector->expects($this->exactly(3))
            ->method('call')
            ->withConsecutive(
                [
                    Request::METHOD_POST,
                    '/v1/push/notification',
                    $this->callback(function($options) {
                        return isset($options['headers']['pushToken']) &&
                               $options['headers']['pushToken'] === 'token1';
                    })
                ],
                [
                    Request::METHOD_POST,
                    '/v1/push/notification',
                    $this->callback(function($options) {
                        return isset($options['headers']['pushToken']) &&
                               $options['headers']['pushToken'] === 'token2';
                    })
                ],
                [
                    Request::METHOD_POST,
                    '/v1/push/notification',
                    $this->callback(function($options) {
                        return isset($options['headers']['pushToken']) &&
                               $options['headers']['pushToken'] === 'token3';
                    })
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, ''));

        $response = $this->systemFirebaseClient->sendNotification($users, $this->standardMessage);

        $this->assertTrue($response);
    }

    /**
     * Test sending notification with error response from connector
     */
    public function testSendNotificationWithErrorResponse(): void
    {
        $users = [
            [
                '_id' => 'pushDetails',
                'push' => [
                    [
                        "deviceId" => "AZERR",
                        "pushToken" => "1234566",
                        "appId" => "appspace"
                    ]
                ],
            ],
        ];

        // Verify that the connector's call method is called exactly once and returns an error
        $this->connector->expects($this->exactly(1))
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, 'Error sending notification'));

        // Verify that the response status is logged (even if it's an error)
        $this->logger->expects($this->atLeastOnce())
            ->method('info')
            ->withConsecutive(
                [$this->anything(), $this->anything()],
                [$this->anything(), $this->anything()],
                ['Notification sent status', $this->callback(function($context) {
                    return isset($context['response']) && $context['response'] === 'Error sending notification';
                })]
            );

        $response = $this->systemFirebaseClient->sendNotification($users, $this->standardMessage);

        // The method should still return true even if there's an error
        $this->assertTrue($response);
    }
}
