<?php

namespace App\Tests\Service;

use App\Helper\WSResponse;
use App\Service\MongoAtlasQueryService;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

/**
 * Tests for the UserService class
 *
 * This test suite covers all scenarios for the UserService:
 * - Getting users by session ID with valid parameters
 * - Getting users by session ID with invalid session ID
 * - Getting users by session ID with null parameters
 * - Getting users by session ID with empty string parameters
 * - Getting users by user ID with valid parameters
 * - Getting users by user ID with invalid user ID
 * - Getting users by user ID with null parameters
 * - Getting users by user ID with empty string parameters
 * - Error responses from the MongoDB service
 * - Verifying the filter methods return the correct filter structure
 */
class UserServiceTest extends TestCase
{
    /** @var MongoAtlasQueryService|\PHPUnit\Framework\MockObject\MockObject */
    private $mongoService;

    /** @var LoggerInterface|\PHPUnit\Framework\MockObject\MockObject */
    private $logger;

    /** @var UserService */
    private $userService;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->userService = new UserService($this->mongoService);
        $this->userService->setLogger($this->logger);
    }

    protected function tearDown(): void
    {
        $this->mongoService = null;
        $this->logger = null;
        $this->userService = null;

        parent::tearDown();
    }

    /**
     * Test getting users by session ID with valid parameters
     */
    public function testGetUsersBySessionId(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'VR3UPHNKSKT101603';
        $sessionId = '44dscsbf5d4dsdsdfsasa9657aefgdxc2';
        $expectedResponse = new WSResponse(Response::HTTP_OK, '{"documents":[{"sessionId":"44dscsbf5d4dsdsdfsasa9657aefgdxc2","name":"John Doe"}]}');

        // Configure the mock to return the expected response
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($expectedResponse);

        // Call the method under test
        $response = $this->userService->getUsersBySessionId($userId, $vin, $sessionId);

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('{"documents":[{"sessionId":"44dscsbf5d4dsdsdfsasa9657aefgdxc2","name":"John Doe"}]}', $response->getData());
    }

    /**
     * Test getting users by session ID with invalid session ID
     */
    public function testGetUsersBySessionIdWithInvalidSessionId(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'VR3UPHNKSKT101603';
        $sessionId = 'invalid-session-id';
        $expectedResponse = new WSResponse(Response::HTTP_OK, '{"documents":[]}');

        // Configure the mock to return the expected response
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($expectedResponse);

        // Call the method under test
        $response = $this->userService->getUsersBySessionId($userId, $vin, $sessionId);

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('{"documents":[]}', $response->getData());
    }

    /**
     * Test getting users by session ID with null parameters
     */
    public function testGetUsersBySessionIdWithNullParameters(): void
    {
        $userId = null;
        $vin = null;
        $sessionId = null;
        $expectedResponse = new WSResponse(Response::HTTP_OK, '{"documents":[]}');

        // Configure the mock to return the expected response
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($expectedResponse);

        // Call the method under test
        $response = $this->userService->getUsersBySessionId($userId, $vin, $sessionId);

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('{"documents":[]}', $response->getData());
    }

    /**
     * Test getting users by session ID with empty string parameters
     */
    public function testGetUsersBySessionIdWithEmptyStringParameters(): void
    {
        $userId = '';
        $vin = '';
        $sessionId = '';
        $expectedResponse = new WSResponse(Response::HTTP_OK, '{"documents":[]}');

        // Configure the mock to return the expected response
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($expectedResponse);

        // Call the method under test
        $response = $this->userService->getUsersBySessionId($userId, $vin, $sessionId);

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('{"documents":[]}', $response->getData());
    }

    /**
     * Test getting users by session ID with error response from MongoDB
     */
    public function testGetUsersBySessionIdWithErrorResponse(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'VR3UPHNKSKT101603';
        $sessionId = '44dscsbf5d4dsdsdfsasa9657aefgdxc2';
        $expectedResponse = new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, 'Database error');

        // Configure the mock to return the expected response
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($expectedResponse);

        // Call the method under test
        $response = $this->userService->getUsersBySessionId($userId, $vin, $sessionId);

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getCode());
        $this->assertEquals('Database error', $response->getData());
    }

    /**
     * Test getting users by user ID with valid parameters
     */
    public function testGetUsersByUserId(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $expectedResponse = new WSResponse(Response::HTTP_OK, '{"documents":[{"userId":"100000000f0b4dce874859657ae00100","name":"John Doe"}]}');

        // Configure the mock to return the expected response
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($expectedResponse);

        // Call the method under test
        $response = $this->userService->getUserByUserId($userId);

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('{"documents":[{"userId":"100000000f0b4dce874859657ae00100","name":"John Doe"}]}', $response->getData());
    }

    /**
     * Test getting users by user ID with invalid user ID
     */
    public function testGetUsersByUserIdWithInvalidUserId(): void
    {
        $userId = 'invalid-user-id';
        $expectedResponse = new WSResponse(Response::HTTP_OK, '{"documents":[]}');

        // Configure the mock to return the expected response
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($expectedResponse);

        // Call the method under test
        $response = $this->userService->getUserByUserId($userId);

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('{"documents":[]}', $response->getData());
    }

    /**
     * Test getting users by user ID with null parameters
     */
    public function testGetUsersByUserIdWithNullParameters(): void
    {
        $userId = null;
        $expectedResponse = new WSResponse(Response::HTTP_OK, '{"documents":[]}');

        // Configure the mock to return the expected response
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($expectedResponse);

        // Call the method under test
        $response = $this->userService->getUserByUserId($userId);

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('{"documents":[]}', $response->getData());
    }

    /**
     * Test getting users by user ID with empty string parameters
     */
    public function testGetUsersByUserIdWithEmptyStringParameters(): void
    {
        $userId = '';
        $expectedResponse = new WSResponse(Response::HTTP_OK, '{"documents":[]}');

        // Configure the mock to return the expected response
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($expectedResponse);

        // Call the method under test
        $response = $this->userService->getUserByUserId($userId);

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('{"documents":[]}', $response->getData());
    }

    /**
     * Test getting users by user ID with error response from MongoDB
     */
    public function testGetUsersByUserIdWithErrorResponse(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $expectedResponse = new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, 'Database error');

        // Configure the mock to return the expected response
        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->willReturn($expectedResponse);

        // Call the method under test
        $response = $this->userService->getUserByUserId($userId);

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getCode());
        $this->assertEquals('Database error', $response->getData());
    }

    /**
     * Test the getUsersBySessionIdFilter method returns the correct filter structure
     */
    public function testGetUsersBySessionIdFilter(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';
        $vin = 'VR3UPHNKSKT101603';
        $sessionId = '44dscsbf5d4dsdsdfsasa9657aefgdxc2';

        $filter = $this->userService->getUsersBySessionIdFilter($userId, $vin, $sessionId);

        $this->assertIsArray($filter);
        $this->assertCount(4, $filter);

        // Check the match stage
        $this->assertArrayHasKey('$match', $filter[0]);
        $this->assertEquals(['userId' => $userId, 'vehicle.vin' => $vin], $filter[0]['$match']);

        // Check the unwind stage
        $this->assertArrayHasKey('$unwind', $filter[1]);
        $this->assertEquals('$push', $filter[1]['$unwind']);

        // Check the second match stage
        $this->assertArrayHasKey('$match', $filter[2]);
        $this->assertEquals(['push.commandSession.remoteSessionId' => $sessionId], $filter[2]['$match']);

        // Check the group stage
        $this->assertArrayHasKey('$group', $filter[3]);
        $this->assertEquals('pushDetails', $filter[3]['$group']['_id']);
        $this->assertArrayHasKey('$push', $filter[3]['$group']['push']);
        $this->assertEquals('$push', $filter[3]['$group']['push']['$push']);
    }

    /**
     * Test the getUserByUserIdFilter method returns the correct filter structure
     */
    public function testGetUserByUserIdFilter(): void
    {
        $userId = '100000000f0b4dce874859657ae00100';

        $filter = $this->userService->getUserByUserIdFilter($userId);

        $this->assertIsArray($filter);
        $this->assertCount(1, $filter);

        // Check the match stage
        $this->assertArrayHasKey('$match', $filter[0]);
        $this->assertEquals(['userId' => $userId], $filter[0]['$match']);
    }
}