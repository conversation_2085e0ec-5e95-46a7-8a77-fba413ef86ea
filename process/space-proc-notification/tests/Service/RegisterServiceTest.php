<?php

namespace App\Tests\Service;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use App\Service\RegisterService;
use App\Helper\WSResponse;
use App\Helper\SuccessResponse;
use App\Model\FcmTokenRegisterModel;
use App\Service\MongoAtlasQueryService;
use Symfony\Component\Serializer\SerializerInterface;

class RegisterServiceTest extends TestCase
{
    private RegisterService $registerService;
    private MongoAtlasQueryService $mongoService;
    private SerializerInterface $serializer;
    private LoggerInterface $loggerMock;
    private FcmTokenRegisterModel $fcmTokenRegisterModel;

    public function setUp(): void
    {
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);
        $this->fcmTokenRegisterModel = $this->createMock(FcmTokenRegisterModel::class);

        // Configure the mock to return a device ID when getDeviceId is called
        $this->fcmTokenRegisterModel
            ->method('getDeviceId')
            ->willReturn('device123');

        $this->registerService = new RegisterService($this->mongoService, $this->serializer);
        $this->registerService->setLogger($this->loggerMock);
    }

    public function testRegisterFcmTokenSuccess(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                ['Registering FCM token', ['userId' => 'userId']],
                ['User found', ['userId' => 'userId']]
            );

        // Mock the find method to return a user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));

        // Mock the aggregate method to return no existing token
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[]}'));

        // Mock the updatePush method for inserting a new token
        $this->mongoService
            ->expects($this->once())
            ->method('updatePush')
            ->with(
                RegisterService::COLLECTION,
                ['_id' => ['$oid' => 'objectId']],
                ['push' => ['deviceId' => 'device123', 'brand' => 'testBrand', 'country' => 'testCountry', 'pushToken' => 'testToken', 'appId' => 'testApp', 'timestamp' => 123456]]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, 'fcm token registered successfully'));

        // Mock the serializer to return a token object
        $this->serializer
            ->expects($this->once())
            ->method('serialize')
            ->with($this->fcmTokenRegisterModel, 'json')
            ->willReturn('{"deviceId":"device123","brand":"testBrand","country":"testCountry","pushToken":"testToken","appId":"testApp","timestamp":123456}');

        // Call the method under test
        $response = $this->registerService->registerFcmToken($this->fcmTokenRegisterModel, 'userId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('fcm token registered successfully', $response->getData());
    }

    public function testUpdateFcmTokenSuccess(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->exactly(3))
            ->method('info')
            ->withConsecutive(
                ['Registering FCM token', ['userId' => 'userId']],
                ['User found', ['userId' => 'userId']],
                ['FCM token already registered', ['userId' => 'userId']]
            );

        // Mock the find method to return a user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));

        // Mock the aggregate method to return an existing token
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"push":[{"deviceId":"device123","brand":"testBrand","country":"testCountry","pushToken":"testToken","appId":"testApp","timestamp":123456}]}]}'));

        // Mock the updateOne method for updating an existing token
        $this->mongoService
            ->expects($this->once())
            ->method('updateOne')
            ->with(
                RegisterService::COLLECTION,
                ['_id' => ['$oid' => 'objectId'], 'push.deviceId' => 'device123'],
                $this->anything()
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, 'fcm token updated successfully'));

        // Mock the serializer to return a token object
        $this->serializer
            ->expects($this->once())
            ->method('serialize')
            ->with($this->fcmTokenRegisterModel, 'json')
            ->willReturn('{"deviceId":"device123","brand":"testBrand","country":"testCountry","pushToken":"testToken","appId":"testApp","timestamp":123456}');

        // Call the method under test
        $response = $this->registerService->registerFcmToken($this->fcmTokenRegisterModel, 'userId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('fcm token updated successfully', $response->getData());
    }

    public function testRegisterFcmTokenFailure(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Registering FCM token', ['userId' => 'userId']);

        $this->loggerMock
            ->expects($this->once())
            ->method('error')
            ->with('User not found', ['userId' => 'userId']);

        // Mock the find method to return no user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(404, 'User not found'));

        // Call the method under test
        $response = $this->registerService->registerFcmToken($this->fcmTokenRegisterModel, 'userId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(404, $response->getCode());
        $this->assertEquals('User not found', $response->getData());
    }

    /**
     * Test registering FCM token with empty user ID
     */
    public function testRegisterFcmTokenWithEmptyUserId(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Registering FCM token', ['userId' => '']);

        $this->loggerMock
            ->expects($this->once())
            ->method('error')
            ->with('User not found', ['userId' => '']);

        // Mock the find method to return no user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => ''])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[]}'));

        // Call the method under test
        $response = $this->registerService->registerFcmToken($this->fcmTokenRegisterModel, '');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(404, $response->getCode());
        $this->assertEquals('User not found', $response->getData());
    }

    /**
     * Test registering FCM token with malformed JSON response
     */
    public function testRegisterFcmTokenWithMalformedJsonResponse(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Registering FCM token', ['userId' => 'userId']);

        // Mock the find method to return malformed JSON
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{malformed json}'));

        // Call the method under test
        $response = $this->registerService->registerFcmToken($this->fcmTokenRegisterModel, 'userId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(404, $response->getCode());
        $this->assertEquals('User not found', $response->getData());
    }

    public function testRegisterSessionIdSuccess(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->exactly(3))
            ->method('info')
            ->withConsecutive(
                ['Registering session ID', ['userId' => 'userId', 'sessionId' => 'sessionId']],
                ['User found', ['userId' => 'userId']],
                ['Session ID already registered', ['userId' => 'userId']]
            );

        // Mock the find method to return a user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));

        // Mock the aggregate method to return an existing token
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"push":[{"deviceId":"device123","brand":"testBrand","country":"testCountry","pushToken":"testToken","appId":"testApp","timestamp":123456}]}]}'));

        // Mock the updateOne method for updating the session ID
        $this->mongoService
            ->expects($this->once())
            ->method('updateOne')
            ->with(
                RegisterService::COLLECTION,
                ['_id' => ['$oid' => 'objectId'], 'push.deviceId' => 'device123'],
                $this->callback(function($fields) {
                    // Check that the commandSession array contains the session ID
                    return isset($fields['push.$.commandSession']) &&
                           count($fields['push.$.commandSession']) == 1 &&
                           $fields['push.$.commandSession'][0]['remoteSessionId'] == 'sessionId' &&
                           isset($fields['push.$.commandSession'][0]['timestamp']);
                })
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, 'sessionId registered successfully'));

        // Call the method under test
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', 'sessionId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('sessionId registered successfully', $response->getData());
    }

    public function testUpdateSessionIdWithExistingCommandSession(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->exactly(3))
            ->method('info')
            ->withConsecutive(
                ['Registering session ID', ['userId' => 'userId', 'sessionId' => 'sessionId']],
                ['User found', ['userId' => 'userId']],
                ['Session ID already registered', ['userId' => 'userId']]
            );

        // Mock the find method to return a user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));

        // Mock the aggregate method to return an existing token with command session
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"push":[{"deviceId":"device123","brand":"testBrand","country":"testCountry","pushToken":"testToken","appId":"testApp","timestamp":123456,"commandSession":[{"remoteSessionId":"oldSessionId","timestamp":123456}]}]}]}'));

        // Mock the updateOne method for updating the session ID
        $this->mongoService
            ->expects($this->once())
            ->method('updateOne')
            ->with(
                RegisterService::COLLECTION,
                ['_id' => ['$oid' => 'objectId'], 'push.deviceId' => 'device123'],
                $this->callback(function($fields) {
                    // Check that the commandSession array contains both the old and new session
                    return isset($fields['push.$.commandSession']) &&
                           count($fields['push.$.commandSession']) == 2 &&
                           $fields['push.$.commandSession'][0]['remoteSessionId'] == 'oldSessionId';
                })
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, 'sessionId updated successfully'));

        // Call the method under test
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', 'sessionId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('sessionId updated successfully', $response->getData());
    }

    public function testInsertFcmTokenAndSessionIdSuccess(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                ['Registering session ID', ['userId' => 'userId', 'sessionId' => 'sessionId']],
                ['User found', ['userId' => 'userId']]
            );

        // Mock the find method to return a user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));

        // Mock the aggregate method to return no existing token
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[]}'));

        // Mock the updatePush method for inserting a new token with session ID
        $this->mongoService
            ->expects($this->once())
            ->method('updatePush')
            ->with(
                RegisterService::COLLECTION,
                ['_id' => ['$oid' => 'objectId']],
                $this->callback(function($fields) {
                    // Check that the push field contains the token and commandSession
                    return isset($fields['push']) &&
                           isset($fields['push']['commandSession']) &&
                           count($fields['push']['commandSession']) == 1 &&
                           $fields['push']['commandSession'][0]['remoteSessionId'] == 'sessionId';
                })
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, 'sessionId registered successfully'));

        // Mock the serializer to return a token object
        $this->serializer
            ->expects($this->once())
            ->method('serialize')
            ->with($this->fcmTokenRegisterModel, 'json')
            ->willReturn('{"deviceId":"device123","brand":"testBrand","country":"testCountry","pushToken":"testToken","appId":"testApp","timestamp":123456}');

        // Call the method under test
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', 'sessionId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('sessionId registered successfully', $response->getData());
    }

    public function testRegisterSessionIdFailure(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Registering session ID', ['userId' => 'userId', 'sessionId' => 'sessionId']);

        $this->loggerMock
            ->expects($this->once())
            ->method('error')
            ->with('User not found', ['userId' => 'userId']);

        // Mock the find method to return no user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(404, 'User not found'));

        // Call the method under test
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', 'sessionId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(404, $response->getCode());
        $this->assertEquals('User not found', $response->getData());
    }

    /**
     * Test registering session ID with empty user ID
     */
    public function testRegisterSessionIdWithEmptyUserId(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Registering session ID', ['userId' => '', 'sessionId' => 'sessionId']);

        $this->loggerMock
            ->expects($this->once())
            ->method('error')
            ->with('User not found', ['userId' => '']);

        // Mock the find method to return no user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => ''])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[]}'));

        // Call the method under test
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, '', 'sessionId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(404, $response->getCode());
        $this->assertEquals('User not found', $response->getData());
    }

    /**
     * Test registering session ID with empty session ID
     */
    public function testRegisterSessionIdWithEmptySessionId(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->exactly(3))
            ->method('info')
            ->withConsecutive(
                ['Registering session ID', ['userId' => 'userId', 'sessionId' => '']],
                ['User found', ['userId' => 'userId']],
                ['Session ID already registered', ['userId' => 'userId']]
            );

        // Mock the find method to return a user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));

        // Mock the aggregate method to return an existing token
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"push":[{"deviceId":"device123","brand":"testBrand","country":"testCountry","pushToken":"testToken","appId":"testApp","timestamp":123456}]}]}'));

        // Mock the updateOne method for updating the session ID with empty session ID
        $this->mongoService
            ->expects($this->once())
            ->method('updateOne')
            ->with(
                RegisterService::COLLECTION,
                ['_id' => ['$oid' => 'objectId'], 'push.deviceId' => 'device123'],
                $this->callback(function($fields) {
                    // Check that the commandSession array contains the empty session ID
                    return isset($fields['push.$.commandSession']) &&
                           count($fields['push.$.commandSession']) == 1 &&
                           $fields['push.$.commandSession'][0]['remoteSessionId'] === '' &&
                           isset($fields['push.$.commandSession'][0]['timestamp']);
                })
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, 'sessionId registered successfully'));

        // Call the method under test
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', '');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('sessionId registered successfully', $response->getData());
    }

    /**
     * Test registering session ID with null command session
     */
    public function testRegisterSessionIdWithNullCommandSession(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->exactly(3))
            ->method('info')
            ->withConsecutive(
                ['Registering session ID', ['userId' => 'userId', 'sessionId' => 'sessionId']],
                ['User found', ['userId' => 'userId']],
                ['Session ID already registered', ['userId' => 'userId']]
            );

        // Mock the find method to return a user
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"_id":"objectId"}]}'));

        // Mock the aggregate method to return an existing token with null command session
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn(new WSResponse(Response::HTTP_OK, '{"documents":[{"push":[{"deviceId":"device123","brand":"testBrand","country":"testCountry","pushToken":"testToken","appId":"testApp","timestamp":123456,"commandSession":null}]}]}'));

        // Mock the updateOne method for updating the session ID
        $this->mongoService
            ->expects($this->once())
            ->method('updateOne')
            ->with(
                RegisterService::COLLECTION,
                ['_id' => ['$oid' => 'objectId'], 'push.deviceId' => 'device123'],
                $this->callback(function($fields) {
                    // Check that the commandSession array contains only the new session
                    return isset($fields['push.$.commandSession']) &&
                           count($fields['push.$.commandSession']) == 1 &&
                           $fields['push.$.commandSession'][0]['remoteSessionId'] == 'sessionId' &&
                           isset($fields['push.$.commandSession'][0]['timestamp']);
                })
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, 'sessionId registered successfully'));

        // Call the method under test
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', 'sessionId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('sessionId registered successfully', $response->getData());
    }

    /**
     * Test registering session ID with malformed JSON response
     */
    public function testRegisterSessionIdWithMalformedJsonResponse(): void
    {
        // Set up expectations for the logger
        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('Registering session ID', ['userId' => 'userId', 'sessionId' => 'sessionId']);

        // Mock the find method to return malformed JSON
        $this->mongoService
            ->expects($this->once())
            ->method('find')
            ->with(RegisterService::COLLECTION, ['userId' => 'userId'])
            ->willReturn(new WSResponse(Response::HTTP_OK, '{malformed json}'));

        // Call the method under test
        $response = $this->registerService->registerSessionId($this->fcmTokenRegisterModel, 'userId', 'sessionId');

        // Verify the response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(404, $response->getCode());
        $this->assertEquals('User not found', $response->getData());
    }
}