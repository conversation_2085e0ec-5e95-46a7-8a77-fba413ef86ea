<?php

namespace App\Tests\Message;

use App\Message\VehicleNotificationEnvelopeBody;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Tests for the VehicleNotificationEnvelopeBody class
 *
 * This test suite covers all scenarios for the VehicleNotificationEnvelopeBody:
 * - Constructor and getters
 * - Deserialization from JSON
 * - Validation of valid messages
 * - Validation of invalid messages (missing userId, vin, sessionId, message)
 * - Validation of invalid message types
 */
class VehicleNotificationEnvelopeBodyTest extends KernelTestCase
{
    private ?ValidatorInterface $validator;
    private Serializer $serializer;
    private string $userId;
    private string $vin;
    private string $sessionId;
    private string $xApiKey;
    private array $messageData;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        self::bootKernel();
        $this->validator = self::getContainer()->get(ValidatorInterface::class);
        $this->serializer = new Serializer([new ObjectNormalizer()], [new JsonEncoder()]);

        // Common test data
        $this->userId = 'ca970d679a29a87aa396dff00029cee3';
        $this->vin = 'fjklfsklfsiofjrihg';
        $this->sessionId = '1YYYYYYYY1EzNzhhOGMyZG555';
        $this->xApiKey = 'ndfskjnsnknnc';
        $this->messageData = [
            'notification' => [
                'title' => 'OMI001',
                'body' => 'OMI002',
                'subtitle' => 'OMI003',
                'bannerTitle' => 'new notification related to the car',
                'bannerDesc' => 'car has started'
            ]
        ];
    }

    /**
     * Test constructor and getters
     */
    public function testConstructorAndGetters(): void
    {
        $envelope = new VehicleNotificationEnvelopeBody(
            $this->userId,
            $this->vin,
            $this->sessionId,
            $this->xApiKey,
            $this->messageData
        );

        // Test getters
        $this->assertEquals($this->userId, $envelope->getUserId());
        $this->assertEquals($this->vin, $envelope->getVin());
        $this->assertEquals($this->sessionId, $envelope->getSessionId());
        $this->assertEquals($this->xApiKey, $envelope->getXApiKey());
        $this->assertEquals($this->messageData, $envelope->getMessage());
    }

    /**
     * Test deserialization from a real message
     */
    public function testDeserializationFromRealMessage(): void
    {
        $encoded = $this->getEncodedMessageFixture();

        $envelope = $this->serializer->deserialize($encoded, VehicleNotificationEnvelopeBody::class, 'json');

        $this->assertInstanceOf(VehicleNotificationEnvelopeBody::class, $envelope);
        $message = $envelope->getMessage();
        $this->assertIsArray($message);
        $this->assertArrayHasKey('notification', $message);
        $this->assertEquals('OMI001', $message['notification']['title']);
        $this->assertEquals('OMI002', $message['notification']['body']);
        $this->assertEquals('OMI003', $message['notification']['subtitle']);
        $this->assertEquals('new notification related to the car', $message['notification']['bannerTitle']);
        $this->assertEquals('car has started', $message['notification']['bannerDesc']);
    }

    /**
     * Test validation of a valid message
     */
    public function testValidationOfValidMessage(): void
    {
        $encoded = $this->getEncodedMessageFixture();
        $envelope = $this->serializer->deserialize($encoded, VehicleNotificationEnvelopeBody::class, 'json');

        $violations = $this->validator->validate($envelope);

        $this->assertCount(0, $violations);
    }

    /**
     * Test validation with empty userId
     */
    public function testValidationWithEmptyUserId(): void
    {
        $fixture = $this->getMessageFixture();
        $fixture['userId'] = '';
        $encoded = json_encode($fixture);

        $envelope = $this->serializer->deserialize($encoded, VehicleNotificationEnvelopeBody::class, 'json');

        $violations = $this->validator->validate($envelope);

        $this->assertCount(2, $violations); // NotBlank and Callback violations

        // Find the violation for userId
        $userIdViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'userId') {
                $userIdViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($userIdViolation);
        $this->assertEquals('The userId should not be empty.', $userIdViolation->getMessage());
    }

    /**
     * Test validation with empty vin
     */
    public function testValidationWithEmptyVin(): void
    {
        $fixture = $this->getMessageFixture();
        $fixture['vin'] = '';
        $encoded = json_encode($fixture);

        $envelope = $this->serializer->deserialize($encoded, VehicleNotificationEnvelopeBody::class, 'json');

        $violations = $this->validator->validate($envelope);

        $this->assertCount(2, $violations); // NotBlank and Callback violations

        // Find the violation for vin
        $vinViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'vin') {
                $vinViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($vinViolation);
        $this->assertEquals('The vin should not be empty.', $vinViolation->getMessage());
    }

    /**
     * Test validation with empty sessionId
     */
    public function testValidationWithEmptySessionId(): void
    {
        $fixture = $this->getMessageFixture();
        $fixture['sessionId'] = '';
        $encoded = json_encode($fixture);

        $envelope = $this->serializer->deserialize($encoded, VehicleNotificationEnvelopeBody::class, 'json');

        $violations = $this->validator->validate($envelope);

        $this->assertCount(2, $violations); // NotBlank and Callback violations

        // Find the violation for sessionId
        $sessionIdViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'sessionId') {
                $sessionIdViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($sessionIdViolation);
        $this->assertEquals('The sessionId should not be empty.', $sessionIdViolation->getMessage());
    }

    /**
     * Test validation with empty xApiKey
     */
    public function testValidationWithEmptyXApiKey(): void
    {
        $fixture = $this->getMessageFixture();
        $fixture['xApiKey'] = '';
        $encoded = json_encode($fixture);

        $envelope = $this->serializer->deserialize($encoded, VehicleNotificationEnvelopeBody::class, 'json');

        $violations = $this->validator->validate($envelope);

        $this->assertCount(1, $violations); // Only NotBlank violation
        $this->assertEquals('xApiKey', $violations[0]->getPropertyPath());
        $this->assertEquals('This value should not be blank.', $violations[0]->getMessage());
    }

    /**
     * Test validation with null message
     */
    public function testValidationWithNullMessage(): void
    {
        $fixture = $this->getMessageFixture();
        $fixture['message'] = null;
        $encoded = json_encode($fixture);

        $envelope = $this->serializer->deserialize($encoded, VehicleNotificationEnvelopeBody::class, 'json');

        $violations = $this->validator->validate($envelope);

        $this->assertCount(2, $violations); // NotBlank and Callback violations

        // Find the violation for message
        $messageViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'message') {
                $messageViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($messageViolation);
        $this->assertEquals('The message should not be empty and it should be array.', $messageViolation->getMessage());
    }

    /**
     * Test validation with non-array message
     *
     * Note: We can't actually test with a string message because the property is typed as ?array
     * and PHP won't allow us to set a string value to an array property.
     * Instead, we'll test with null, which should trigger the same validation.
     */
    public function testValidationWithNonArrayMessage(): void
    {
        // Create the object with a null message
        $envelope = new VehicleNotificationEnvelopeBody(
            $this->userId,
            $this->vin,
            $this->sessionId,
            $this->xApiKey,
            null
        );

        $violations = $this->validator->validate($envelope);

        $this->assertGreaterThanOrEqual(1, $violations->count());

        // Find the violation for message
        $messageViolation = null;
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'message') {
                $messageViolation = $violation;
                break;
            }
        }

        $this->assertNotNull($messageViolation);
        $this->assertEquals('The message should not be empty and it should be array.', $messageViolation->getMessage());
    }

    /**
     * Test validation with empty message array
     */
    public function testValidationWithEmptyMessageArray(): void
    {
        $fixture = $this->getMessageFixture();
        $fixture['message'] = [];
        $encoded = json_encode($fixture);

        $envelope = $this->serializer->deserialize($encoded, VehicleNotificationEnvelopeBody::class, 'json');

        $violations = $this->validator->validate($envelope);

        // Empty array is valid for the Callback validation but may trigger NotBlank
        $this->assertLessThanOrEqual(1, $violations->count());
    }

    /**
     * Helper method to get a valid message fixture as JSON string
     */
    private function getEncodedMessageFixture(array $body = [], array $envelope = []): string
    {
        $template = $this->getMessageFixture();
        $message = $this->mergeEnvelope($template, $body, $envelope);
        $encoded = json_encode($message);
        if (false === $encoded) {
            $encoded = '';
        }

        return $encoded;
    }

    /**
     * Helper method to get a valid message fixture as array
     */
    private function getMessageFixture(array $body = [], array $envelope = []): array
    {
        $template = [
            'userId' => $this->userId,
            'vin' => $this->vin,
            'sessionId' => $this->sessionId,
            'xApiKey' => $this->xApiKey,
            'message' => $this->messageData
        ];

        $message = $this->mergeEnvelope($template, $body, $envelope);

        return $message;
    }

    /**
     * Helper method to merge body and envelope data into a template
     */
    private function mergeEnvelope(array $template, array $body = [], array $envelope = []): array
    {
        if (!empty($body) && isset($template['message']) && is_array($template['message'])) {
            $template['message'] = array_merge($template['message'], $body);
        }

        $message = array_merge($template, $envelope);

        return $message;
    }
}
