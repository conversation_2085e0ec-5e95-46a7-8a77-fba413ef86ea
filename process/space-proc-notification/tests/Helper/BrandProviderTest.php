<?php

namespace App\Tests\Helper;

use App\Helper\BrandProvider;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Yaml\Yaml;


class BrandProviderTest extends TestCase
{
    /**
     * Test that getBrands returns an array
     */
    public function testGetBrandsReturnsArray(): void
    {
        $brands = BrandProvider::getBrands();

        $this->assertIsArray($brands);
    }

    /**
     * Test that getBrands returns non-empty array
     */
    public function testGetBrandsReturnsNonEmptyArray(): void
    {
        $brands = BrandProvider::getBrands();

        $this->assertNotEmpty($brands);
    }

    /**
     * Test that getBrands returns array containing expected brands
     */
    public function testGetBrandsContainsExpectedBrands(): void
    {
        $brands = BrandProvider::getBrands();

        // Check for some expected brands based on the YAML file we examined
        $this->assertContains('AC', $brands);
        $this->assertContains('AP', $brands);
        $this->assertContains('DS', $brands);
        $this->assertContains('OP', $brands);
    }

    /**
     * Test that getBrands returns array with string values
     */
    public function testGetBrandsContainsStringValues(): void
    {
        $brands = BrandProvider::getBrands();

        foreach ($brands as $brand) {
            $this->assertIsString($brand);
        }
    }

    /**
     * Test that getBrands returns array with expected structure
     */
    public function testGetBrandsHasExpectedStructure(): void
    {
        $brands = BrandProvider::getBrands();

        // Check that the array is indexed (not associative)
        $this->assertSame(range(0, count($brands) - 1), array_keys($brands));

        // Check that all values are uppercase strings with 2 characters
        foreach ($brands as $brand) {
            $this->assertIsString($brand);
            $this->assertMatchesRegularExpression('/^[A-Z]{2}$/', $brand);
        }
    }

    /**
     * Test that getBrands returns the same data as directly parsing the YAML file
     */
    public function testGetBrandsMatchesDirectYamlParsing(): void
    {
        $brands = BrandProvider::getBrands();

        // Get the file path using reflection
        $reflection = new \ReflectionClass(BrandProvider::class);
        $constant = $reflection->getReflectionConstant('FILE_PATH');
        $filePath = $constant->getValue();

        // Parse the YAML file directly
        $params = Yaml::parseFile($filePath);
        $expectedBrands = $params['brands'];

        // Compare the results
        $this->assertEquals($expectedBrands, $brands);
    }
}
