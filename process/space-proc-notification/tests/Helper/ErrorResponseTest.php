<?php

namespace App\Tests\Helper;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;


class ErrorResponseTest extends TestCase
{
    private string $message;
    private int $code;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->message = 'error occured'; // Note: Misspelling is intentional to match the class implementation
        $this->code = Response::HTTP_BAD_REQUEST;
    }

    /**
     * Clean up the test environment after each test
     */
    protected function tearDown(): void
    {
        // No cleanup needed for this test
        parent::tearDown();
    }

    /**
     * Test constructor with string error
     */
    public function testConstructorWithStringError(): void
    {
        $errorMessage = 'This is an error message';
        $response = new ErrorResponse($errorMessage, $this->code);

        $result = $response->toArray();

        $this->assertEquals($this->code, $result['code']);
        $this->assertEquals($errorMessage, $result['content']['error']['message']);
        $this->assertEquals($errorMessage, $result['content']['error']['errors']);
    }

    /**
     * Test constructor with array error
     */
    public function testConstructorWithArrayError(): void
    {
        $errors = ['field1' => 'Error 1', 'field2' => 'Error 2'];
        $response = new ErrorResponse($errors, $this->code);

        $result = $response->toArray();

        $this->assertEquals($this->code, $result['code']);
        $this->assertEquals('error occured '.json_encode($errors), $result['content']['error']['message']);
        $this->assertEquals($errors, $result['content']['error']['errors']);
    }

    /**
     * Test constructor with object error
     */
    public function testConstructorWithObjectError(): void
    {
        $errors = (object)['field1' => 'Error 1', 'field2' => 'Error 2'];
        $response = new ErrorResponse($errors, $this->code);

        $result = $response->toArray();

        $this->assertEquals($this->code, $result['code']);
        $this->assertEquals('error occured '.json_encode($errors), $result['content']['error']['message']);
        $this->assertEquals($errors, $result['content']['error']['errors']);
    }

    /**
     * Test constructor with default status code
     */
    public function testConstructorWithDefaultStatusCode(): void
    {
        $response = new ErrorResponse($this->message);

        $result = $response->toArray();

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
    }

    /**
     * Test constructor with custom status code
     */
    public function testConstructorWithCustomStatusCode(): void
    {
        $customCode = Response::HTTP_NOT_FOUND;
        $response = new ErrorResponse($this->message, $customCode);

        $result = $response->toArray();

        $this->assertEquals($customCode, $result['code']);
    }

    /**
     * Test constructor with invalid status code (less than 400)
     */
    public function testConstructorWithInvalidStatusCode(): void
    {
        $invalidCode = 200; // OK status, not an error
        $response = new ErrorResponse($this->message, $invalidCode);

        $result = $response->toArray();

        // Should default to HTTP_BAD_REQUEST
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
    }

    /**
     * Test setCode method
     */
    public function testSetCodeMethod(): void
    {
        $response = new ErrorResponse($this->message);
        $newCode = Response::HTTP_INTERNAL_SERVER_ERROR;

        $returnValue = $response->setCode($newCode);
        $result = $response->toArray();

        // Test method chaining
        $this->assertSame($response, $returnValue);
        // Test the code was updated
        $this->assertEquals($newCode, $result['code']);
    }

    /**
     * Test setMessage method
     */
    public function testSetMessageMethod(): void
    {
        $response = new ErrorResponse('original error');
        $newMessage = 'updated error message';

        $returnValue = $response->setMessage($newMessage);
        $result = $response->toArray();

        // Test method chaining
        $this->assertSame($response, $returnValue);
        // Test the message was updated
        $this->assertEquals($newMessage, $result['content']['error']['message']);
    }

    /**
     * Test setErrors method
     */
    public function testSetErrorsMethod(): void
    {
        $response = new ErrorResponse('original error');
        $newErrors = ['field1' => 'New error 1', 'field2' => 'New error 2'];

        $returnValue = $response->setErrors($newErrors);
        $result = $response->toArray();

        // Test method chaining
        $this->assertSame($response, $returnValue);
        // Test the errors were updated
        $this->assertEquals($newErrors, $result['content']['error']['errors']);
    }

    /**
     * Test toArray method with all properties set
     */
    public function testToArrayWithAllPropertiesSet(): void
    {
        $message = 'Custom error message';
        $errors = ['field' => 'Error details'];
        $code = Response::HTTP_FORBIDDEN;

        $response = new ErrorResponse('initial');
        $response->setMessage($message);
        $response->setErrors($errors);
        $response->setCode($code);

        $expectedResponse = [
            'code' => $code,
            'content' => [
                'error' => [
                    'message' => $message,
                    'errors' => $errors,
                ],
            ],
        ];

        $this->assertEquals($expectedResponse, $response->toArray());
    }

    /**
     * Test that ErrorResponse implements ResponseArrayFormat interface
     */
    public function testImplementsResponseArrayFormatInterface(): void
    {
        $response = new ErrorResponse($this->message);

        $this->assertInstanceOf(ResponseArrayFormat::class, $response);
    }

    /**
     * Test with empty string error
     */
    public function testWithEmptyStringError(): void
    {
        $response = new ErrorResponse('');

        $result = $response->toArray();

        // Check that the response has the expected structure
        $this->assertArrayHasKey('code', $result);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('error', $result['content']);

        // The message key should not be present because empty string is falsy in PHP
        $this->assertArrayNotHasKey('message', $result['content']['error']);

        // The errors key should not be present because empty string is falsy in PHP
        $this->assertArrayNotHasKey('errors', $result['content']['error']);

        // The error array should be empty
        $this->assertEmpty($result['content']['error']);
    }

    /**
     * Test with null code
     */
    public function testWithNullCode(): void
    {
        $response = new ErrorResponse($this->message, null);

        $result = $response->toArray();

        // Should default to HTTP_BAD_REQUEST
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
    }

    /**
     * Test with complex nested error object
     */
    public function testWithComplexNestedErrorObject(): void
    {
        $complexError = [
            'validation' => [
                'field1' => ['This field is required', 'This field must be unique'],
                'field2' => ['Invalid format']
            ],
            'system' => 'Database connection error'
        ];

        $response = new ErrorResponse($complexError);

        $result = $response->toArray();

        $this->assertEquals('error occured '.json_encode($complexError), $result['content']['error']['message']);
        $this->assertEquals($complexError, $result['content']['error']['errors']);
    }

    /**
     * Test with numeric error
     */
    public function testWithNumericError(): void
    {
        $numericError = 42;
        $response = new ErrorResponse($numericError);

        $result = $response->toArray();

        $this->assertEquals('error occured 42', $result['content']['error']['message']);
        $this->assertEquals($numericError, $result['content']['error']['errors']);
    }

    /**
     * Test with boolean error
     */
    public function testWithBooleanError(): void
    {
        $booleanError = true;
        $response = new ErrorResponse($booleanError);

        $result = $response->toArray();

        $this->assertEquals('error occured true', $result['content']['error']['message']);
        $this->assertEquals($booleanError, $result['content']['error']['errors']);
    }
}
