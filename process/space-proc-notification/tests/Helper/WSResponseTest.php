<?php

namespace App\Tests\Helper;

use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;


class WSResponseTest extends TestCase
{
    private mixed $testData;
    private int $testCode;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        $this->testData = 'test content';
        $this->testCode = Response::HTTP_NOT_FOUND;
    }

    /**
     * Clean up the test environment after each test
     */
    protected function tearDown(): void
    {
        // No cleanup needed for this test
        parent::tearDown();
    }

    /**
     * Test constructor with string data
     */
    public function testConstructWithStringData(): void
    {
        $response = new WSResponse($this->testCode, $this->testData);

        $this->assertEquals($this->testCode, $response->getCode());
        $this->assertEquals($this->testData, $response->getData());
    }

    /**
     * Test constructor with array data
     */
    public function testConstructWithArrayData(): void
    {
        $arrayData = ['key' => 'value', 'nested' => ['data' => true]];
        $response = new WSResponse($this->testCode, $arrayData);

        $this->assertEquals($this->testCode, $response->getCode());
        $this->assertEquals($arrayData, $response->getData());
    }

    /**
     * Test constructor with object data
     */
    public function testConstructWithObjectData(): void
    {
        $objectData = new \stdClass();
        $objectData->property = 'value';
        $objectData->nested = new \stdClass();
        $objectData->nested->data = true;

        $response = new WSResponse($this->testCode, $objectData);

        $this->assertEquals($this->testCode, $response->getCode());
        $this->assertEquals($objectData, $response->getData());
    }

    /**
     * Test constructor with null data
     */
    public function testConstructWithNullData(): void
    {
        $response = new WSResponse($this->testCode, null);

        $this->assertEquals($this->testCode, $response->getCode());
        $this->assertNull($response->getData());
    }

    /**
     * Test constructor with boolean data
     */
    public function testConstructWithBooleanData(): void
    {
        $booleanData = true;
        $response = new WSResponse($this->testCode, $booleanData);

        $this->assertEquals($this->testCode, $response->getCode());
        $this->assertEquals($booleanData, $response->getData());
    }

    /**
     * Test constructor with numeric data
     */
    public function testConstructWithNumericData(): void
    {
        $numericData = 42;
        $response = new WSResponse($this->testCode, $numericData);

        $this->assertEquals($this->testCode, $response->getCode());
        $this->assertEquals($numericData, $response->getData());
    }

    /**
     * Test constructor with different HTTP status codes
     */
    public function testConstructWithDifferentStatusCodes(): void
    {
        // Test with OK status
        $response1 = new WSResponse(Response::HTTP_OK, $this->testData);
        $this->assertEquals(Response::HTTP_OK, $response1->getCode());

        // Test with Created status
        $response2 = new WSResponse(Response::HTTP_CREATED, $this->testData);
        $this->assertEquals(Response::HTTP_CREATED, $response2->getCode());

        // Test with Bad Request status
        $response3 = new WSResponse(Response::HTTP_BAD_REQUEST, $this->testData);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response3->getCode());

        // Test with Internal Server Error status
        $response4 = new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $this->testData);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response4->getCode());
    }

    /**
     * Test setCode method
     */
    public function testSetCodeMethod(): void
    {
        $response = new WSResponse(Response::HTTP_OK, $this->testData);
        $newCode = Response::HTTP_NOT_FOUND;

        $returnValue = $response->setCode($newCode);

        // Test method chaining
        $this->assertSame($response, $returnValue);
        // Test the code was updated
        $this->assertEquals($newCode, $response->getCode());
    }

    /**
     * Test setData method with string
     */
    public function testSetDataMethodWithString(): void
    {
        $response = new WSResponse($this->testCode, 'original data');
        $newData = 'updated data';

        $returnValue = $response->setData($newData);

        // Test method chaining
        $this->assertSame($response, $returnValue);
        // Test the data was updated
        $this->assertEquals($newData, $response->getData());
    }

    /**
     * Test setData method with array
     */
    public function testSetDataMethodWithArray(): void
    {
        $response = new WSResponse($this->testCode, 'original data');
        $newData = ['key' => 'value', 'nested' => ['data' => true]];

        $returnValue = $response->setData($newData);

        // Test method chaining
        $this->assertSame($response, $returnValue);
        // Test the data was updated
        $this->assertEquals($newData, $response->getData());
    }

    /**
     * Test setData method with object
     */
    public function testSetDataMethodWithObject(): void
    {
        $response = new WSResponse($this->testCode, 'original data');

        $newData = new \stdClass();
        $newData->property = 'value';
        $newData->nested = new \stdClass();
        $newData->nested->data = true;

        $returnValue = $response->setData($newData);

        // Test method chaining
        $this->assertSame($response, $returnValue);
        // Test the data was updated
        $this->assertEquals($newData, $response->getData());
    }

    /**
     * Test setData method with null
     */
    public function testSetDataMethodWithNull(): void
    {
        $response = new WSResponse($this->testCode, 'original data');

        $returnValue = $response->setData(null);

        // Test method chaining
        $this->assertSame($response, $returnValue);
        // Test the data was updated
        $this->assertNull($response->getData());
    }

    /**
     * Test multiple setter calls
     */
    public function testMultipleSetterCalls(): void
    {
        $response = new WSResponse(Response::HTTP_OK, 'original data');

        // Chain multiple setter calls
        $response->setCode(Response::HTTP_BAD_REQUEST)
                 ->setData(['key' => 'value'])
                 ->setCode(Response::HTTP_NOT_FOUND)
                 ->setData('final data');

        // Verify final state
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getCode());
        $this->assertEquals('final data', $response->getData());
    }

    /**
     * Test with a very large array
     */
    public function testWithVeryLargeArray(): void
    {
        // Create a large array with 1000 elements
        $largeArray = [];
        for ($i = 0; $i < 1000; $i++) {
            $largeArray["key$i"] = "value$i";
        }

        $response = new WSResponse($this->testCode, $largeArray);

        // Verify that the data is stored correctly
        $this->assertEquals($largeArray, $response->getData());
    }

    /**
     * Test with complex nested data structure
     */
    public function testWithComplexNestedDataStructure(): void
    {
        $complexData = [
            'user' => [
                'id' => 123,
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'roles' => ['ROLE_USER', 'ROLE_ADMIN'],
                'settings' => [
                    'notifications' => true,
                    'theme' => 'dark',
                    'preferences' => [
                        'language' => 'en',
                        'timezone' => 'UTC'
                    ]
                ]
            ],
            'meta' => [
                'timestamp' => time(),
                'version' => '1.0.0'
            ]
        ];

        $response = new WSResponse($this->testCode, $complexData);

        $this->assertEquals($this->testCode, $response->getCode());
        $this->assertEquals($complexData, $response->getData());
        $this->assertEquals('John Doe', $response->getData()['user']['name']);
        $this->assertEquals('dark', $response->getData()['user']['settings']['theme']);
        $this->assertEquals('en', $response->getData()['user']['settings']['preferences']['language']);
    }

    /**
     * Test with special characters in data
     */
    public function testWithSpecialCharactersInData(): void
    {
        $specialCharsData = "Special characters: !@#$%^&*()_+{}|:<>?[];',./";
        $response = new WSResponse($this->testCode, $specialCharsData);

        $this->assertEquals($specialCharsData, $response->getData());
    }

    /**
     * Test with empty string data
     */
    public function testWithEmptyStringData(): void
    {
        $emptyString = '';
        $response = new WSResponse($this->testCode, $emptyString);

        $this->assertEquals($emptyString, $response->getData());
        $this->assertEmpty($response->getData());
        $this->assertNotNull($response->getData());
    }

    /**
     * Test with zero numeric data
     */
    public function testWithZeroNumericData(): void
    {
        $zeroData = 0;
        $response = new WSResponse($this->testCode, $zeroData);

        $this->assertEquals($zeroData, $response->getData());
        $this->assertNotNull($response->getData());
        $this->assertIsInt($response->getData());
    }

    /**
     * Test with empty array data
     */
    public function testWithEmptyArrayData(): void
    {
        $emptyArray = [];
        $response = new WSResponse($this->testCode, $emptyArray);

        $this->assertEquals($emptyArray, $response->getData());
        $this->assertEmpty($response->getData());
        $this->assertIsArray($response->getData());
    }
}
