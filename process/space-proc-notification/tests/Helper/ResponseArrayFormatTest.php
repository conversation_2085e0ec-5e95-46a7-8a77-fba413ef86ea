<?php

namespace App\Tests\Helper;

use App\Helper\ResponseArrayFormat;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;


class ResponseArrayFormatTest extends TestCase
{
    /**
     * Test that implementations of ResponseArrayFormat return an array from toArray
     */
    public function testToArrayReturnsArray(): void
    {
        // Test with SuccessResponse implementation
        $successResponse = new SuccessResponse(['data' => 'test']);
        $successResult = $successResponse->toArray();

        $this->assertIsArray($successResult);
        $this->assertArrayHasKey('code', $successResult);
        $this->assertArrayHasKey('content', $successResult);

        // Test with ErrorResponse implementation
        $errorResponse = new ErrorResponse('Error message');
        $errorResult = $errorResponse->toArray();

        $this->assertIsArray($errorResult);
        $this->assertArrayHasKey('code', $errorResult);
        $this->assertArrayHasKey('content', $errorResult);
    }

    /**
     * Test that implementations of ResponseArrayFormat have consistent structure
     */
    public function testImplementationsHaveConsistentStructure(): void
    {
        // Create instances of both implementations
        $successResponse = new SuccessResponse(['data' => 'test'], Response::HTTP_OK);
        $errorResponse = new ErrorResponse('Error message', Response::HTTP_BAD_REQUEST);

        // Get the array representations
        $successResult = $successResponse->toArray();
        $errorResult = $errorResponse->toArray();

        // Both should have the same top-level keys (order doesn't matter)
        $successKeys = array_keys($successResult);
        $errorKeys = array_keys($errorResult);
        sort($successKeys);
        sort($errorKeys);
        $this->assertEquals($successKeys, $errorKeys);

        // Both should have 'content' as an array
        $this->assertIsArray($successResult['content']);
        $this->assertIsArray($errorResult['content']);
    }

    /**
     * Test with a custom implementation of ResponseArrayFormat
     */
    public function testCustomImplementation(): void
    {
        // Create a mock implementation of ResponseArrayFormat
        $mockImplementation = new class implements ResponseArrayFormat {
            public function toArray(): array
            {
                return [
                    'code' => 200,
                    'content' => ['custom' => 'data']
                ];
            }
        };

        $result = $mockImplementation->toArray();

        // Verify the result follows the expected structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('code', $result);
        $this->assertArrayHasKey('content', $result);
        $this->assertEquals(200, $result['code']);
        $this->assertEquals(['custom' => 'data'], $result['content']);
    }

    /**
     * Test that implementations handle different data types correctly
     */
    public function testImplementationsHandleDifferentDataTypes(): void
    {
        // Test with string data
        $successWithString = new SuccessResponse('string data');
        $successStringResult = $successWithString->toArray();
        $this->assertIsArray($successStringResult);

        // Test with array data
        $successWithArray = new SuccessResponse(['key' => 'value']);
        $successArrayResult = $successWithArray->toArray();
        $this->assertIsArray($successArrayResult);

        // Test with null data
        $successWithNull = new SuccessResponse(null);
        $successNullResult = $successWithNull->toArray();
        $this->assertIsArray($successNullResult);
        $this->assertArrayHasKey('content', $successNullResult);
        $this->assertArrayHasKey('code', $successNullResult);

        // Test with boolean data
        $errorWithBoolean = new ErrorResponse(true);
        $errorBooleanResult = $errorWithBoolean->toArray();
        $this->assertIsArray($errorBooleanResult);
    }

    /**
     * Test that implementations handle different status codes correctly
     */
    public function testImplementationsHandleDifferentStatusCodes(): void
    {
        // Test with OK status
        $successWithOk = new SuccessResponse(['data' => 'test'], Response::HTTP_OK);
        $successOkResult = $successWithOk->toArray();
        $this->assertEquals(Response::HTTP_OK, $successOkResult['code']);

        // Test with Created status
        $successWithCreated = new SuccessResponse(['data' => 'test'], Response::HTTP_CREATED);
        $successCreatedResult = $successWithCreated->toArray();
        $this->assertEquals(Response::HTTP_CREATED, $successCreatedResult['code']);

        // Test with Bad Request status
        $errorWithBadRequest = new ErrorResponse('Error', Response::HTTP_BAD_REQUEST);
        $errorBadRequestResult = $errorWithBadRequest->toArray();
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $errorBadRequestResult['code']);

        // Test with Not Found status
        $errorWithNotFound = new ErrorResponse('Error', Response::HTTP_NOT_FOUND);
        $errorNotFoundResult = $errorWithNotFound->toArray();
        $this->assertEquals(Response::HTTP_NOT_FOUND, $errorNotFoundResult['code']);
    }
}
