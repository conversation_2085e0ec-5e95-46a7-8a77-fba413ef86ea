<?php

namespace App\Tests\Model;

use App\Model\ProfileModel;
use PHPUnit\Framework\TestCase;

class ProfileModelTest extends TestCase
{
    private $profileModel;

    protected function setUp(): void
    {
        $this->profileModel = new ProfileModel();
    }

    public function testGettersAndSetters(): void
    {
        // Test data
        $civility = 'Mr';
        $lastName = 'Doe';
        $firstName = 'John';
        $address1 = '123 Main St';
        $address2 = 'Apt 4B';
        $city = 'Paris';
        $country = 'FR';
        $zipcode = '75001';
        $phone = '123456789';
        $email = '<EMAIL>';

        // Set values using setters
        $this->profileModel->setCivilityCode($civility)
            ->setLastName($lastName)
            ->setFirstName($firstName)
            ->setAddress1($address1)
            ->setAddress2($address2)
            ->setCity($city)
            ->setCountry($country)
            ->setZipcode($zipcode)
            ->setPhone($phone)
            ->setEmail($email);
        $this->assertEquals($civility, $this->profileModel->getCivilityCode());
        $this->assertEquals($lastName, $this->profileModel->getLastName());
        $this->assertEquals($firstName, $this->profileModel->getFirstName());
        $this->assertEquals($address1, $this->profileModel->getAddress1());
        $this->assertEquals($address2, $this->profileModel->getAddress2());
        $this->assertEquals($city, $this->profileModel->getCity());
        $this->assertEquals($country, $this->profileModel->getCountry());
        $this->assertEquals($zipcode, $this->profileModel->getZipcode());
        $this->assertEquals($phone, $this->profileModel->getPhone());
        $this->assertEquals($email, $this->profileModel->getEmail());
    }

    public function testToArrayWithAllValues(): void
    {
        // Test data
        $id = 'user123';
        $civility = 'Mr';
        $lastName = 'Doe';
        $firstName = 'John';
        $address1 = '123 Main St';
        $address2 = 'Apt 4B';
        $city = 'Paris';
        $country = 'FR';
        $zipcode = '75001';
        $phone = '123456789';
        $email = '<EMAIL>';

        // Set values using setters
        $this->profileModel->setCivilityCode($civility)
            ->setLastName($lastName)
            ->setFirstName($firstName)
            ->setAddress1($address1)
            ->setAddress2($address2)
            ->setCity($city)
            ->setCountry($country)
            ->setZipcode($zipcode)
            ->setPhone($phone)
            ->setEmail($email);

        // Expected array
        $expectedArray = [
            'id' => '',
            'civility_code' => $civility,
            'last_name' => $lastName,
            'first_name' => $firstName,
            'address1' => $address1,
            'address2' => $address2,
            'city' => $city,
            'country' => $country,
            'zip_code' => $zipcode,
            'phone' => $phone,
            'email' => $email
        ];

        // Call toArray method
        $result = $this->profileModel->toArray();

        // Assert the result
        $this->assertEquals($expectedArray, $result);
    }

    public function testToArrayWithSomeEmptyValues(): void
    {
        // Test data - some values are empty
        $id = 'user123';
        $lastName = 'Doe';
        $firstName = 'John';
        $email = '<EMAIL>';

        // Set only some values
        $this->profileModel->setLastName($lastName)
            ->setFirstName($firstName)
            ->setEmail($email);

        // Expected array - only non-empty values should be included
        $expectedArray = [
            'id' => '',
            'civility_code' => '',  // Default empty string values are included
            'last_name' => $lastName,
            'first_name' => $firstName,
            'address1' => '',
            'address2' => '',
            'city' => '',
            'country' => '',
            'zip_code' => '',
            'phone' => '',
            'email' => $email
        ];

        // Call toArray method
        $result = $this->profileModel->toArray();

        // Assert the result
        $this->assertEquals($expectedArray, $result);
    }

    public function testDefaultValues(): void
    {
        // A new ProfileModel should have empty string default values for most properties
        $this->assertEquals('', $this->profileModel->getCivilityCode());
        $this->assertEquals('', $this->profileModel->getLastName());
        $this->assertEquals('', $this->profileModel->getFirstName());
        $this->assertEquals('', $this->profileModel->getAddress1());
        $this->assertEquals('', $this->profileModel->getAddress2());
        $this->assertEquals('', $this->profileModel->getCity());
        $this->assertEquals('', $this->profileModel->getCountry());
        $this->assertEquals('', $this->profileModel->getZipcode());
        $this->assertEquals('', $this->profileModel->getPhone());
        $this->assertEquals('', $this->profileModel->getEmail());
    }
}
