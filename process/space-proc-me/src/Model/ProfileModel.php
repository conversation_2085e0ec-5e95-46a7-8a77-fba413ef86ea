<?php

namespace App\Model;

use Symfony\Component\Serializer\Attribute\SerializedName;

class ProfileModel {
    #[SerializedName('id')]
    private string $id="";
    
    #[SerializedName('civility')]
    private string $civility="";

    #[SerializedName('civility_code')]
    private string $civility_code="";

    #[SerializedName('last_name')]
    private string $last_name="";

    #[SerializedName('first_name')]
    private string $first_name="";

    #[SerializedName('address1')]
    private string $address1="";

    #[SerializedName('address2')]
    private string $address2="";

    #[SerializedName('city')]
    private string $city="";

    #[SerializedName('country')]
    private string $country="";

    #[SerializedName('zip_code')]
    private string $zip_code="";

    #[SerializedName('phone')]
    private string $phone="";

    #[SerializedName('email')]
    private string $email="";

    /**
     * Get the value of civility
     */
    public function getCivilityCode(): string
    {
        return $this->civility_code;
    }

    /**
     * Set the value of civility
     *
     * @return  self
     */
    public function setCivilityCode(string $civilityCode): self
    {
        $this->civility_code = $civilityCode;

        return $this;
    }

    /**
     * Get the value of lastName
     */
    public function getLastName(): string
    {
        return $this->last_name;
    }

    /**
     * Set the value of lastName
     *
     * @return  self
     */
    public function setLastName(string $lastName): self
    {
        $this->last_name = $lastName;

        return $this;
    }

    /**
     * Get the value of firstName
     */
    public function getFirstName(): string
    {
        return $this->first_name;
    }

    /**
     * Set the value of firstName
     *
     * @return  self
     */
    public function setFirstName(string $firstName): self
    {
        $this->first_name = $firstName;

        return $this;
    }

    /**
     * Get the value of address1
     */
    public function getAddress1(): string
    {
        return $this->address1;
    }

    /**
     * Set the value of address1
     *
     * @return  self
     */
    public function setAddress1(string $address1): self
    {
        $this->address1 = $address1;

        return $this;
    }

    /**
     * Get the value of address2
     */
    public function getAddress2(): string
    {
        return $this->address2;
    }

    /**
     * Set the value of address2
     *
     * @return  self
     */
    public function setAddress2(string $address2): self
    {
        $this->address2 = $address2;

        return $this;
    }

    /**
     * Get the value of city
     */
    public function getCity(): string
    {
        return $this->city;
    }

    /**
     * Set the value of city
     *
     * @return  self
     */
    public function setCity(string $city): self
    {
        $this->city = $city;

        return $this;
    }

    /**
     * Get the value of country
     */
    public function getCountry(): string
    {
        return $this->country;
    }

    /**
     * Set the value of country
     *
     * @return  self
     */
    public function setCountry(string $country): self
    {
        $this->country = $country;

        return $this;
    }

    /**
     * Get the value of zipcode
     */
    public function getZipcode(): string
    {
        return $this->zip_code;
    }

    /**
     * Set the value of zipcode
     *
     * @return  self
     */
    public function setZipcode(string $zipcode): self
    {
        $this->zip_code = $zipcode;

        return $this;
    }

    /**
     * Get the value of phone
     */
    public function getPhone(): string
    {
        return $this->phone;
    }

    /**
     * Set the value of phone
     *
     * @return  self
     */
    public function setPhone(string $phone): self
    {
        $this->phone = $phone;

        return $this;
    }

    public function toArray(): array
    {
        $data = [
            'id' => $this->id,
            'civility' => $this->civility,
            'civility_code' => $this->civility_code,
            'last_name' => $this->last_name,
            'first_name' => $this->first_name,
            'address1' => $this->address1,
            'address2' => $this->address2,
            'country' => $this->country,
            'city' => $this->city,
            'zip_code' => $this->zip_code,
            'phone' => $this->phone,
            'email' => $this->email
        ];

        return array_filter($data, function ($value) {
            return $value !== null;
        });
    }

    /**
     * Get the value of email
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * Set the value of email
     *
     * @return  self
     */
    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    /**
     * Get the value of id
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * Set the value of id
     *
     * @return  self
     */
    public function setId(string $id): self
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Get the value of civility
     */ 
    public function getCivility(): string
    {
        return $this->civility;
    }

    /**
     * Set the value of civility
     *
     * @return  self
     */ 
    public function setCivility(string $civility): self
    {
        $this->civility = $civility;

        return $this;
    }
}