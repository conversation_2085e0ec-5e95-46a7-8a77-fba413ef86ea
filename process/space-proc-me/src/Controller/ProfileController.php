<?php

namespace App\Controller;

use App\Manager\ProfileManager;
use App\Model\ProfileModel;
use App\Trait\ValidationResponseTrait;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Nelmio\ApiDocBundle\Annotation\Model;
use Symfony\Component\Serializer\SerializerInterface;
use App\Validator\CountryValidator;
use App\Validator\LanguageValidator;
use App\Validator\BrandValidator;


#[Route('v1', name: 'profile')]
class ProfileController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/profile', name: '_update', methods: ['PUT'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\RequestBody(
        content: new JsonContent(ref: new Model(type: ProfileModel::class))
    )]
    #[OA\Tag(name: 'Profile')]
    #[OA\Response(
        response: 200,
        description: 'Ok',
        content: new Model(type: ProfileModel::class)
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function updateProfileInfo(ValidatorInterface $validator, ProfileManager $profileManager, Request $request, SerializerInterface $serializer): JsonResponse
    {
        $userId = $request->headers->get('userId');
        $reqBody = $request->getContent();

        // Deserialize JSON request into ProfileModel
        $profileModel = $serializer->deserialize($reqBody, ProfileModel::class, 'json');

        $response = $profileManager->putUserData(
            $userId,
            $profileModel->toArray()
        )->toarray();

        return $this->json($response['content'], $response['code']);
    }

    #[Route('/profile', name: '_get', methods: ['GET'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Tag(name: 'Profile')]
    #[OA\Response(
        response: 200,
        description: 'Ok',
        content: new Model(type: ProfileModel::class)
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function getProfileInfo(ValidatorInterface $validator, ProfileManager $profileManager, Request $request): JsonResponse
    {
        $userId = $request->headers->get('userId');

        $response = $profileManager->getUserData(
            $userId
        )->toarray();

        return $this->json($response['content'], $response['code']);
    }

    /**
     * @param ValidatorInterface $validator
     * @param string $brand
     * @param string $country
     * @param string $language
     * @return array
     */
    private function validate(ValidatorInterface $validator,string  $brand, string $country, string $language = null): array
    {
        $inputs = ['brand' => $brand, 'country' => $country];
        $constraints =  [
            'brand'   => BrandValidator::getConstraints(),
            'country' => CountryValidator::getConstraints()
        ];

        if ($language) {
            $inputs['language'] = $language;
            $constraints['language'] = LanguageValidator::getConstraintsForLanguage();
        }

        $errors = $validator->validate(
            $inputs,
            new Assert\Collection($constraints)
        );

        return static::getValidationMessages($errors);
    }
}