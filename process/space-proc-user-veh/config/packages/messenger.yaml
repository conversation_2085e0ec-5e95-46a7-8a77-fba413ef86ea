framework:
    messenger:
        # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
        # failure_transport: failed

        transports:
            # https://symfony.com/doc/current/messenger.html#transport-configuration
            # async: '%env(MESSENGER_TRANSPORT_DSN)%'
            # failed: 'doctrine://default?queue_name=failed'
            # sync: 'sync://'

            aws_sqs_refresh_vehicle_add_vehicle:
                dsn: "%env(MESSENGER_TRANSPORT_DSN)%"
                serializer: App\Messenger\RefreshVehicleMessageSerializer
                
            aws_sqs_refresh_vehicle_subscription: 
                dsn: 'sqs://sqs.%env(AWS_DEFAULT_REGION)%.amazonaws.com'
                options:                
                    access_key: '%env(AWS_ACCESS_KEY_ID)%'
                    secret_key: '%env(AWS_SECRET_ACCESS_KEY)%'
                    queue_name: '%env(AWS_SQS_REFRESH_VEHICLES_SUBSCRIPTION_QUEUE_NAME)%'
                    debug: false
                serializer: messenger.transport.symfony_serializer

            aws_sqs_consumer_rights: 
                dsn: 'sqs://sqs.%env(AWS_DEFAULT_REGION)%.amazonaws.com'
                options:
                    queue_name: '%env(AWS_SQS_CONSUMER_RIGHTS_QUEUE)%'
                    access_key: '%env(AWS_ACCESS_KEY_ID)%'
                    secret_key: '%env(AWS_SECRET_ACCESS_KEY)%'
                    auto_setup: false
                serializer: App\Message\ConsumerRightsCustomSerializer
                retry_strategy:
                    max_retries: 3
                    delay: 1000
                    multiplier: 2
                    max_delay: 0

        routing:
            # Route your messages to the transports
            'App\Message\RefreshVehicleSubscriptionMessage': aws_sqs_refresh_vehicle_subscription
            'App\Model\RefreshVehicleNotificationRequest': aws_sqs_refresh_vehicle_add_vehicle
            'App\Message\ConsumerRightsEnvelopeBody': aws_sqs_consumer_rights

# when@test:
#    framework:
#        messenger:
#            transports:
#                # replace with your transport name here (e.g., my_transport: 'in-memory://')
#                # For more Messenger testing tools, see https://github.com/zenstruck/messenger-test
#                async: 'in-memory://'
