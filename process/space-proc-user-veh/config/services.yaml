# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    mongo_db.app: "%env(MONGO_APP)%"
    mongo_db.database: "%env(MONGO_DATABASE)%"
    mongo_db.datasource: "%env(MONGO_DATASOURCE)%"
    brands.xp: ['AP', 'AC', 'DS', 'OP', 'VX']
    brands.ssdp: ['JE', 'AR']

imports:
    - { resource: 'visual_settings.yaml' }

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    App\Service\MongoAtlasQueryService:
        arguments:
            $database: '%mongo_db.database%'
            $dataSource: '%mongo_db.datasource%'

    App\EventListener\ExceptionListener:
        tags: [kernel.event_listener]

    App\Connector\SystemOmniConnector:
        arguments:
            $url: "%env(MS_SYS_OMNI_URL)%"


    App\Connector\SystemIdpConnector:
        arguments:
            $url: "%env(MS_SYS_IDP_URL)%"

    App\Connector\SystemCorvetDataConnector:
        arguments:
            $url: "%env(MS_SYS_CORVET_DATA_URL)%"

    App\Connector\MongoAtlasApiConnector:
        $mongoApp: '%mongo_db.app%'
    
    App\Connector\ProcessMeConnector:
        arguments:
            $url: "%env(MY_MARQUE_PROC_ME_URL)%"
            $apiKey: "%env(SPACE_MIDDLEWARE_API_KEY)%"
    
    App\Connector\SysSamsDataConnector:
        arguments:
            $url: "%env(MS_SYS_SAMS_DATA_URL)%"
              
    App\Helper\BrandHelper:
        arguments:
            $xpBrands: '%brands.xp%'
            $ssdpBrands: '%brands.ssdp%'

    App\Connector\SystemSdprConnector:
        arguments:
            $url: '%env(MS_SYS_SDPR_URL)%'

    App\Connector\SystemUserDataConnector:
        arguments:
            $url: '%env(MS_MYM1_SYS_USER_DATA_URL)%'
    
    App\Connector\SystemUserDBConnector:
        arguments:
            $url: '%env(MS_SYS_USER_DB_URL)%'

    App\MessageHandler\RefreshVehicleMessageHandler:
        tags: [messenger.aws_sqs_refresh_vehicle_add_vehicle]
    
    App\Service\VehicleVisualService:
        arguments:
            $visualSettings: '%visual3d%'

    App\Service\FeatureCodeService:
        arguments:
            $cdnUrl: '%env(SETTINGS_CDN_URL)%'

    App\MessageHandler\ConsumerRightsMessageHandler:
        tags: [messenger.aws_sqs_consumer_rights]
