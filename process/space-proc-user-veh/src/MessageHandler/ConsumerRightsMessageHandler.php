<?php

namespace App\MessageHandler;

use App\Message\ConsumerRightsEnvelopeBody;
use App\Manager\ConsumerRightsManager;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use App\Trait\LoggerTrait;

#[AsMessageHandler(fromTransport: 'aws_sqs_consumer_rights')]
class ConsumerRightsMessageHandler
{
    use LoggerTrait;

    public function __construct(private ConsumerRightsManager $manager)
    {
    }

    public function __invoke(ConsumerRightsEnvelopeBody $message)
    {
        $this->logger->info('Processing consumer rights message', [
            'event' => $message->getEvent(),
            'vin' => $message->getVin(),
            'customerId' => $message->getCustomerId(),
            'timestamp' => $message->getTimestamp(),
            'gigyaUid' => $message->getGigyaUid(),
            'carAssociationLevel' => $message->getCarAssociationLevel()
        ]);

        return $this->manager->handleConsumerRightsMessage($message);
    }
}
