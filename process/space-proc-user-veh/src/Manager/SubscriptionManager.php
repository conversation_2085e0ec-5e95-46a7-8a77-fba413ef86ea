<?php

namespace App\Manager;

use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Webmozart\Assert\Assert as ArrayAssert;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Serializer\SerializerInterface;

use App\Model\SamsContractSuccessResponse;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use App\Model\SubscriptionModel;
use App\Model\SamsRatePlanModel;
use App\Service\ContribService;
use App\Service\SubscriptionService;
use App\Trait\ValidationResponseTrait;
use App\Helper\BrandHelper;
use App\DataMapper\SubscriptionDataMapper;

class SubscriptionManager
{
    use LoggerTrait;
    use ValidationResponseTrait;

    const SOURCE_APP = 'APP';

    public function __construct(
        private SubscriptionService $subscriptionService,
        private ContribService $contribService,
        private ValidatorInterface $validator,
        private SubscriptionDataMapper $subscriptionDataMapper,
        private SerializerInterface $serializer,
    ) {
    }

    public function addContribData(string $userDbId, array $contracts, string $brand, string $country, string $language, string $source)
    {
        $cultureCode = $language.'-'.$country;
        
        // Extract all product IDs from contracts
        $productIds = array_map(function ($contract) {
            return $contract['subscription']['ratePlans'][0]['product']['productCode'];
        }, $contracts);
        
        // Get all contrib data in a single API call
        $allContribs = $this->contribService->getContribInfoByProductIds(implode(',', $productIds), $brand, $cultureCode, $source);
        $this->logger->info("".__METHOD__." => Response from contrib service --> " . ['allContribs' => $allContribs]);
        // Map the contrib data back to contracts
        return array_map(function ($contract) use ($allContribs) {
            $productId = $contract['subscription']['ratePlans'][0]['product']['productCode'];
            if (isset($allContribs[$productId])) {
                if (!is_array($allContribs[$productId] ?? '')) {
                    throw new \Exception('Contrib data is not an array', Response::HTTP_BAD_REQUEST);
                }
                return array_merge($contract, $allContribs[$productId]);
            }
            return $contract;
        }, $contracts);
    }

    public function addAssociationId(array $contracts): array
    {
        foreach ($contracts as $index => $contract) {
            $contracts[$index]['associationId'] = $contract['subscription']['ratePlans'][0]['product']['vehProvProductRelations'][0]['associationId'] ?? '';
        }
        return $contracts;
    }

    public function getSubscription(string $userDbId, string $vin, string $target, string $brand, string $country, string $language, string $source): SuccessResponse|ErrorResponse
    {  
        $this->logger->info('=> Call Subscription API : '.__METHOD__.' with parameters : ', ['userDbId' => $userDbId, 'vin' => $vin, 'target' => $target]);
        try {
            $response = $this->subscriptionService->getSubscription($userDbId, $vin, $target);
            if ($response->getCode() === Response::HTTP_NOT_FOUND) {
                return new SuccessResponse([], Response::HTTP_OK);
            }
            if ($response->getCode() !== Response::HTTP_OK) {
                $responseData = $response->getData();
                $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;
                $this->logger->error('=> '.__METHOD__.' => error : '.$result);
                return new ErrorResponse($result, $response->getCode());
            }
            else {
                $this->logger->info("Success: [function getSubscription] Call SAMS contract for vin {$vin}");
                $subscriptions = [];
                $contracts = $response->getData()['success'];
                ArrayAssert::isArray($contracts);
                ArrayAssert::keyExists($contracts, 'vehicleProvisionings', 'vehicleProvisionings key not found in the sams responses');
                $subscriptions = $contracts['vehicleProvisionings'];
                $subscriptions = $this->addAssociationId($subscriptions);
                $subscriptions = $this->addContribData($userDbId, $subscriptions, $brand, $country, $language, $source);
                $samsSubscriptions = $this->subscriptionDataMapper->transform($subscriptions);
                $subscriptions = [];
                foreach ($samsSubscriptions as $subscription) {
                    $decodedValues = array_map(function ($item) {
                        return json_decode($this->serializer->serialize($item, 'json'), true);
                    }, array_values($subscription));
                
                    $subscriptions = array_merge($subscriptions, $decodedValues);
                }
                return new SuccessResponse($subscriptions, Response::HTTP_OK);
            }
        } catch (\Exception $e) {
            $this->logger->error('=> '.__METHOD__.'Catched Exception SubscriptionManager::getSubscription '.$e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
