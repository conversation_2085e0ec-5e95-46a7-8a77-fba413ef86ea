<?php

namespace App\Manager;

use App\Message\ConsumerRightsEnvelopeBody;
use App\Service\UserDataService;
use App\Helper\SuccessResponse;
use App\Helper\ErrorResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;

class ConsumerRightsManager
{
    use LoggerTrait;

    public function __construct(
        private UserDataService $userDataService,
        private VehicleManager $vehicleManager
    ) {
    }

    public function handleConsumerRightsMessage(ConsumerRightsEnvelopeBody $message): SuccessResponse|ErrorResponse
    {
        try {
            $event = $message->getEvent();
            $vin = $message->getVin();
            $customerId = $message->getCustomerId();
            $timestamp = $message->getTimestamp();
            $gigyaUid = $message->getGigyaUid();
            $carAssociationLevel = $message->getCarAssociationLevel();

            $this->logger->info('Processing consumer rights message', [
                'event' => $event,
                'vin' => $vin,
                'customerId' => $customerId,
                'timestamp' => $timestamp,
                'gigyaUid' => $gigyaUid,
                'carAssociationLevel' => $carAssociationLevel
            ]);

            // Process the message based on the event type
            $result = $this->vehicleManager->handleCustomerRightsUpdate(
                $event,
                $vin,
                $customerId,
                $timestamp,
                $gigyaUid,
                $carAssociationLevel
            );

            if ($result) {
                return new SuccessResponse('Consumer rights updated successfully', Response::HTTP_OK);
            } else {
                return new ErrorResponse('Failed to update consumer rights', Response::HTTP_INTERNAL_SERVER_ERROR);
            }
        } catch (\Throwable $e) {
            $this->logger->error('Error processing consumer rights message', [
                'exception' => $e->getMessage(),
                'event' => $message->getEvent(),
                'vin' => $message->getVin(),
                'customerId' => $message->getCustomerId()
            ]);
            
            return new ErrorResponse('Error processing consumer rights message: ' . $e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
