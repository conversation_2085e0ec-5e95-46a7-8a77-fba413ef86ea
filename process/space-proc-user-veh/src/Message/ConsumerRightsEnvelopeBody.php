<?php

namespace App\Message;

use Symfony\Component\Serializer\Annotation\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class ConsumerRightsEnvelopeBody
{
    public function __construct(
        #[SerializedName('event')]
        #[Assert\NotBlank]
        private string $event,
        
        #[SerializedName('vin')]
        #[Assert\NotBlank]
        private string $vin,
        
        #[SerializedName('customer_id')]
        #[Assert\NotBlank]
        private string $customerId,
        
        #[SerializedName('timestamp')]
        #[Assert\NotBlank]
        private string $timestamp,
        
        #[SerializedName('gigya_uid')]
        #[Assert\NotBlank]
        private string $gigyaUid,
        
        #[SerializedName('car_association_level')]
        private ?string $carAssociationLevel = null
    ) {
    }

    #[Assert\Callback]
    public function validateConsumerRightsEnvelopeBody(ExecutionContextInterface $context): void
    {
        if ($this->getEvent() === '') {
            $context->buildViolation('The event should not be empty.')
                ->atPath('event')
                ->addViolation();
        }
        
        if ($this->getVin() === '') {
            $context->buildViolation('The vin should not be empty.')
                ->atPath('vin')
                ->addViolation();
        }

        if ($this->getCustomerId() === '') {
            $context->buildViolation('The customer_id should not be empty.')
                ->atPath('customer_id')
                ->addViolation();
        }

        if ($this->getTimestamp() === '') {
            $context->buildViolation('The timestamp should not be empty.')
                ->atPath('timestamp')
                ->addViolation();
        }
        
        if ($this->getGigyaUid() === '') {
            $context->buildViolation('The gigya_uid should not be empty.')
                ->atPath('gigya_uid')
                ->addViolation();
        }
    }

    public function getEvent(): string
    {
        return $this->event;
    }

    public function getVin(): string
    {
        return $this->vin;
    }

    public function getCustomerId(): string
    {
        return $this->customerId;
    }

    public function getTimestamp(): string
    {
        return $this->timestamp;
    }

    public function getGigyaUid(): string
    {
        return $this->gigyaUid;
    }

    public function getCarAssociationLevel(): ?string
    {
        return $this->carAssociationLevel;
    }
}
