<?php

namespace App\Message;

use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use <PERSON><PERSON>fony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Transport\Serialization\Serializer;
use Symfony\Component\Messenger\Exception\InvalidArgumentException;

use App\Trait\LoggerTrait;

class ConsumerRightsCustomSerializer extends Serializer
{
    use LoggerTrait;

    public function decode(array $encodedEnvelope): Envelope
    {
        $flags = JSON_INVALID_UTF8_IGNORE |
            JSON_UNESCAPED_UNICODE |
            JSON_UNESCAPED_SLASHES |
            JSON_THROW_ON_ERROR;

        try {
            $this->logger->info('Decoding Consumer Rights message: ', ['encodedEnvelope' => $encodedEnvelope]);
            
            $decodedBody = json_decode($encodedEnvelope['body'], true, 512, $flags);
            
            // If the message is wrapped in a Message or Records field (common in SQS)
            if (isset($decodedBody['Message'])) {
                $decodedBody = json_decode($decodedBody['Message'], true, 512, $flags);
            } elseif (isset($decodedBody['Records']) && is_array($decodedBody['Records']) && !empty($decodedBody['Records'])) {
                $record = $decodedBody['Records'][0];
                if (isset($record['body'])) {
                    $decodedBody = json_decode($record['body'], true, 512, $flags);
                }
            }
            
            return $this->createEnvelope($decodedBody);
            
        } catch (\JsonException $e) {
            $this->logger->error(
                'Json decoding exception for Consumer Rights message: encoded envelope body is not valid JSON: ',
                [
                    'exception' => $e->getMessage(),
                    'messageQueueId' => $encodedEnvelope['headers']['message_id'] ?? '',
                    'consumerRightsMessageBody' => $encodedEnvelope['body'],
                ]
            );
            
            // Return an empty envelope to avoid retrying
            return new Envelope(new ConsumerRightsEnvelopeBody(
                '', '', '', '', ''
            ));
        } catch (\Throwable $e) {
            $this->logger->error(
                'Exception processing Consumer Rights message: ',
                [
                    'exception' => $e->getMessage(),
                    'messageQueueId' => $encodedEnvelope['headers']['message_id'] ?? '',
                    'consumerRightsMessageBody' => $encodedEnvelope['body'] ?? '',
                ]
            );
            
            // Return an empty envelope to avoid retrying
            return new Envelope(new ConsumerRightsEnvelopeBody(
                '', '', '', '', ''
            ));
        }
    }

    private function createEnvelope(array $decodedBody): Envelope
    {
        $event = $decodedBody['event'] ?? '';
        $vin = $decodedBody['vin'] ?? '';
        $customerId = $decodedBody['customer_id'] ?? '';
        $timestamp = $decodedBody['timestamp'] ?? '';
        $gigyaUid = $decodedBody['gigya_uid'] ?? '';
        $carAssociationLevel = $decodedBody['car_association_level'] ?? null;
        
        $message = new ConsumerRightsEnvelopeBody(
            $event,
            $vin,
            $customerId,
            $timestamp,
            $gigyaUid,
            $carAssociationLevel
        );
        
        return new Envelope($message);
    }
}
