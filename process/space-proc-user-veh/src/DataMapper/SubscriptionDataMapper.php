<?php

namespace App\DataMapper;

use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Serializer\SerializerInterface;

use App\Trait\ValidationResponseTrait;
use App\Trait\LoggerTrait;
use App\Model\SubscriptionModel;
use App\Model\SamsRatePlanModel;
use App\Model\DateRange;
use App\Model\ContractRemoteLev;
use App\Helper\ErrorResponse;

class SubscriptionDataMapper
{

    use LoggerTrait;
    use ValidationResponseTrait;

    public function __construct(
        private ValidatorInterface $validator,
        private SerializerInterface $serializer,
    ) {}

    private function calculateDuration(string $samsDuration): string
    {
        $duration = '';
        if (intval($samsDuration)) {
            ($samsDuration % 12) >= 1 ? $duration = 'per year' : $duration = 'per month';
        }

        return $duration;
    }

    public function getCategory(?string $familyName): string
    {
        $connectedServices = ['NAVCOZAR', 'TMTS', 'NAVCO', 'ZAR', 'LEV', 'PHEV', 'BEV', 'RACCESS', 'CONNECTEDALARM', 'DIGITALKEY', 'AE_CALL', 'EV_ROUTING_APP', 'PARTNERSERVICE', 'TRIPS_IN_THE_CLOUD', 'STOLEN_VEHICLE'];
        $afterSalesServices = ['DIMBO', 'PRIVILEGE'];
        if (in_array($familyName, $connectedServices)) {
            return 'CONNECTED_SERVICES';
        } elseif (in_array($familyName, $afterSalesServices)) {
            return 'AFTERSALES_SERVICES';
        } else {
            return 'OTHERS';
        }
    }

    private static function samsStatusToMymStatus($samsStatus)
    {

        switch (strtoupper($samsStatus)) {
            case 'ACTIVATED':
                return SubscriptionModel::CONTRACT_ACTIVE;
            case 'PENDING ACTIVATION':
                return ContractRemoteLev::CONTRACT_SAMS_PENDING_ACTIVATION;
            case 'PENDING IDENTIFICATION':
                return ContractRemoteLev::CONTRACT_SAMS_PENDING_IDENTIFICATION;
            case 'PENDING SUBSCRIPTION':
                return ContractRemoteLev::CONTRACT_SAMS_PENDING_SUBSCRIPTION;
            case 'EN ATTENTE DE SOUSCRIPTION':
                return ContractRemoteLev::CONTRACT_SAMS_PENDING_SUBSCRIPTION;
            case 'CANCELLED':
                return ContractRemoteLev::CONTRACT_SAMS_CANCELLED;
            case 'PENDING CANCELLATION':
                return ContractRemoteLev::CONTRACT_SAMS_PENDING_CANCELLATION;
            case 'EXPIRED':
                return ContractRemoteLev::CONTRACT_SAMS_EXPIRED;
            case 'EXPIRED IN':
                return ContractRemoteLev::CONTRACT_SAMS_EXPIRED_IN;
            case 'TERMINATED':
                return ContractRemoteLev::CONTRACT_SAMS_TERMINATED;
            case 'PENDING TERMINATION':
                return ContractRemoteLev::CONTRACT_SAMS_PENDING_TERMINATION;
            case 'FAILED PAYMENT':
                return ContractRemoteLev::CONTRACT_SAMS_FAILED_PAYMENT;
            case 'DEACTIVATED':
                return ContractRemoteLev::CONTRACT_SAMS_DEACTIVATED;
            case 'PENDING DEACTIVATION':
                return ContractRemoteLev::CONTRACT_SAMS_PENDING_DEACTIVATION;
        }
        return false;
    }

    private static function isMorePrior(SubscriptionModel $currentOne, ?SubscriptionModel $oldOne)
    {
        if (!$oldOne) {
            return true;
        }
        if ($currentOne->getPriority() == $oldOne->getPriority()) {
            if ($oldOne->getValidity()->getStart() < $currentOne->getValidity()->getStart()) {
                return true;
            } elseif ($oldOne->getValidity()->getStart() == $currentOne->getValidity()->getStart()) {
                if ($oldOne->getSubscriptionTechCreationDate() < $currentOne->getSubscriptionTechCreationDate()) {
                    return true;
                }
            }
        }
        return $currentOne->getPriority() < $oldOne->getPriority();
    }

    public function map(SubscriptionModel $subscriptionModel, array $subscription, $status, ?bool $isLev = false)
    {
        if ('Periodical' == ($subscription['subscription']['ratePlans'][0]['pricingModel'] ?? '')) {
            $subscription['subscriptionEndDate'] = null;
        }
        $category = $this->getCategory($subscription['subscription']['ratePlans'][0]['product']['productFamily']);
        $dateRange = new DateRange($subscription['startDate'], $subscription['endDate']);
        $subscriptionModel->setType($subscription['subscription']['ratePlans'][0]['product']['productFamily']);
        $subscriptionModel->setStatus($status);
        $subscriptionModel->setValidity($dateRange);
        $subscriptionModel->setIsExtensible($subscription['subscription']['isExtensible'] ?? false);
        $subscriptionModel->setTitle($subscription['subscription']['ratePlans'][0]['product']['productCommercialName'] ?? '');
        $subscriptionModel->setCategory($category);
        $subscriptionModel->setHasFreeTrial(strtolower($subscription['subscription']['hasFreeTrial'] ?? '') === 'true');

        if ($isLev === true) {
            if ($subscription['subscription']['ratePlans'][0]['product']['productFamily'] !== 'RACCESS') {
                $subscriptionModel->setType('REMOTELEV');
            }

            //Set Fds 
            if ($subscription['subscription']['ratePlans'][0]['product']['productFamily'] == 'RACCESS') {
                $subscriptionModel->setFds(['NEE02', 'NEF02', 'NEF01']);
            } else {
                $subscriptionModel->setFds(['NCG01', 'NBM01', 'NAS01', 'NAO02']);
            }

            $subscriptionModel->setAssociationId($subscription['associationId'] ?? '');
        }
        else {
            $subscriptionModel->setAssociationId($subscription['associationId'] ?? '');
        }
        $subscriptionModel->setStatusReason($subscription['statusReason'] ?? '');
        $subscriptionModel->setSubscriptionTechCreationDate($subscription['techCreationDate'] ?? '');
        $subscriptionModel->setPriority($subscription['subscription']['status'] ?? '');
        $subscriptionModel->setTopMainImage($subscription['topMainImage'] ?? '');
        $subscriptionModel->setUrlSso($subscription['productUrlSso'] ?? '');
        $subscriptionModel->setUrlCvs($subscription['productUrlCvs'] ?? '');

        return $subscriptionModel;
    }

    public function transform(array $samsSubscriptions): array|ErrorResponse
    {
        $subscriptions = $nacSubscriptions = [];
        foreach ($samsSubscriptions as $subscription) {
            $status = self::samsStatusToMymStatus($subscription['subscription']['status']);
            if (!array_key_exists('associationId', $subscription) && ContractRemoteLev::CONTRACT_SAMS_PENDING_IDENTIFICATION === $status) {
                $status = ContractRemoteLev::CONTRACT_SAMS_PENDING_SUBSCRIPTION;
            }
            if ($status > 0) {
                if (in_array($subscription['subscription']['ratePlans'][0]['product']['productFamily'], ['ZAR', 'NAVCO', 'NAVCOZAR', 'DIMBO'])) {
                    $samsSubscription = self::map(new SubscriptionModel(), $subscription, $status);
                    if (self::isMorePrior($samsSubscription, $nacSubscriptions[$samsSubscription->getType()] ?? null)) {
                        $nacSubscriptions[$samsSubscription->getType()] = $samsSubscription;
                    }
                } elseif (in_array($subscription['subscription']['ratePlans'][0]['product']['productFamily'], ['PHEV', 'BEV'])) {
                    $samsSubscription =  self::map(new ContractRemoteLev(), $subscription, $status, true);
                    if (self::isMorePrior($samsSubscription, $subscriptions['remoteLev'][0] ?? null)) {
                        $subscriptions['remoteLev'][0] = $samsSubscription;
                    }
                } elseif ($subscription['subscription']['ratePlans'][0]['product']['productFamily']) {
                    $type = strtolower($subscription['subscription']['ratePlans'][0]['product']['productFamily']);
                    $samsSubscription = self::map(new SubscriptionModel(), $subscription, $status);
                    if (self::isMorePrior($samsSubscription, $subscriptions[$type][0] ?? null)) {
                        $subscriptions[$type][0] = $samsSubscription;
                    }
                }

            }
        }
        $subscriptions['nac'] = array_values($nacSubscriptions);
        return $subscriptions;
    }
}
