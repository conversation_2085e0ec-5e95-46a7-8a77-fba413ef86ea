<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\Request;

/**
 * SystemUserDB service.
 */
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Connector\SystemUserDBConnector;
use App\Dto\AddVehicleInputDTO;
use App\Dto\EditVehicleInputDTO;

class SystemUserDBService
{
    use LoggerTrait;

    public function __construct(
        private SystemUserDBConnector $connector
    ) {
    }

    public function listVehicles(string $customerId): WSResponse
    {
        $options = [];
        $url = '/v1/customer/' . $customerId . '/garage';
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", ['options' => $options]);

        $response = $this->connector->call(Request::METHOD_GET, $url, $options);
        return $response;
    }

    public function addVehicle(string $customerId,array $params): WSResponse
    {
        $options = [
            'json' => $params,
        ];
        $url = '/v1/customer/' . $customerId . '/garage';
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", ['options' => $options]);

        $response = $this->connector->call(Request::METHOD_POST, $url, $options);
        return $response;
    }

    public function updateVehicle(string $customerId,array $params): WSResponse
    {
        $options = [
            'json' => $params,
        ];
        $url = '/v1/customer/' . $customerId . '/garage';
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", ['options' => $options]);

        $response = $this->connector->call(Request::METHOD_PUT, $url, $options);
        return $response;
    }

    /**
     * update Customer garage.
     */
    public function updateCustomerGarage(EditVehicleInputDTO $parameters): WSResponse
    {
        $options = [
            'json' => [
                'vin' => $parameters->getVin(),
                'brand' => $parameters->getBrand(),
                'country' => $parameters->getCountry(),
                'VehicleNickName' => $parameters->getNickName(),
                'plateName' => $parameters->getLicencePlate(),
                'commercialName' => $parameters->getCommercialName(),
                'pictureUrl' => $parameters->getPictureUrl(),
            ],
        ];
        $url = "/v1/customer/". $parameters->getCustomerId() ."/garage";
        $this->logger->info('=> ' . __METHOD__ . " => Call API [$url] with options ", ['options' => $options]);

        return $this->connector->call(Request::METHOD_PUT, $url, $options);
    }

    /**
     * Delete Customer garage.
     */
    public function deleteCustomerVehicle(string $userDbId, string $vin): WSResponse
    {
        $url = "/v1/customer/". $userDbId ."/garage/". $vin;
        $this->logger->info('=> ' . __METHOD__ . " => Call API [$url] with no options.");

        return $this->connector->call(Request::METHOD_DELETE, $url);
    }
}
