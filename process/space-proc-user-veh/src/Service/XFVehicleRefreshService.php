<?php 

namespace App\Service;

use App\Model\SystemVehicleData;
use App\MongoDB\UserData\UserDataDocument\UserDataDocument;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Throwable;
use Webmozart\Assert\Assert as WebMozartAssert;

/**
 * XFVehicleRefreshService retrieve XF vehicles data.
 */
class XFVehicleRefreshService implements RefreshVehicleInterface
{
    use LoggerTrait;

    public function __construct(
        private SystemSdprClient $sdprClient,
        private ValidatorInterface $validator
    )
    {
    }

    public function getxFVehicleServices(): array
    {
        return [];
    }

    /**
     * @return array<SystemVehicleData>
     */
    public function retrieveVehicles(UserDataDocument $userDataDocument, string $brand, string $language, string $country): array
    {
        try{
            // skip requests for not xF brands
            if (!in_array($brand, self::BRANDS_XF)) {
                return [];
            }

            $vehiclesList = $this->getSdprUserVehicles($userDataDocument->userId);
            $this->logger->debug(__METHOD__.' SDPR /v1/user/vehicles call response', [
                'userId' => $userDataDocument->userId,
                'vehiclesList' => $vehiclesList
            ]);
            $vehicles = [];
            if(!empty($vehiclesList)){
                $vehicles = $this->mapSdprVehiclesToSystemVehicleDataList($vehiclesList, [
                    'userId' => $userDataDocument->userId]);
            }

            // Apply filters by brand to the resulted vehicles list
            $vehicles = $this->filterByBrand($vehicles, $brand);

            return $vehicles;
        } catch (Throwable $e) {
            $this->logger->error(__METHOD__.' Error while retrieving XF user vehicles', [
                'userId' => $userDataDocument->userId, 'exception' => $e]);
            throw $e;
        }
    }

    /**
     * @param array $vehicles<SystemVehicleData>
     * @return array<SystemVehicleData>
     */
    private function filterByBrand(array $vehicles, ?string $brand=null): array
    {
        $filteredVehicles = [];
        foreach ($vehicles as $vehicle) {
            WebMozartAssert::isInstanceOf($vehicle, SystemVehicleData::class);
            if ( $brand === null |
                (null !== $brand && $brand === trim(strtoupper($vehicle->getBrandCode())))
            ) {
                $filteredVehicles[$vehicle->getVin()] = $vehicle;
            }
        }        

        return $filteredVehicles;
    }

    public function getSdprUserVehicles(string $userId): array
    {
        $response = $this->sdprClient->getUserVehicles($userId);
        $vehicles = [];
        if (Response::HTTP_OK != $response->getCode()) {
            $this->logger->error(__METHOD__.' Error while retrieving user vehicles', [
                'userId' => $userId, 
                'response' => $response->getData()
            ]);
            return $vehicles;
        }

        $this->logger->debug(__METHOD__.' SDPR /v1/user/vehicles call response', [
            'userId' => $userId, 
            'response' => json_encode($response->getData())]);

        $vehicles = $response->getData();
        $errors = $this->validator->validate($vehicles, $this->getSdprV1UserVehiclesSuccessResponseConstraints());
        if (count($errors) > 0) {
            $this->logger->error(__METHOD__ . ' Error while obtaining vehicles list from SDPR system', [
                'userId' => $userId, 
                'errors' => $errors,
                'response' => $vehicles
            ]);
            return [];
        }

        return $vehicles;
    }

    private function getSdprV1UserVehiclesSuccessResponseConstraints(): Assert\Collection
    {
        $constraints = new Assert\Collection([
                'success' => new Assert\Collection([
                    'fields' => [
                        'vehicles' => new Assert\All([
                            'constraints' => [
                                new Assert\Collection([
                                    'fields' => [
                                        'vin' => [
                                            new Assert\Required(),
                                            new Assert\NotBlank(),
                                        ],
                                    ],
                                    'allowExtraFields' => true,
                                ]),
                            ],
                        ]),
                    ],
                    'allowExtraFields' => true,
                ]),
            ]);

        return $constraints;
    }

    /**
     * @return SystemVehicleData[]
     * 
     * 
     * MilkyWay                    		MongoDB                     	SDPR/Valorization
     * id                          		id                          	generated by space middelware uuid
     * vin                         		vin                         	sdpr.vehicles.vin     * 
     * brand                       		brand                       	MAPPING(sdpr.vehicles.brandCode) " with transformation
     * shortLabel                  		shortLabel                  	sdpr.vehicles.nickname
     * modelDescription(name)        	label                       	sdpr.vehicles.modelDescription
     * picture                     		visual                      	null
     * sdp                         	    sdp                         	"SDPR" : constant value for all vehicles retrived by sdpr
     * lcdv                        		versionId                   	null
     * type                        	    type                        	sdpr.vehicles.fuelType
     * regTimeStamp                		regTimeStamp                	sdpr.vehicles.regTimestamp
     * enrollmentStatus            		enrollmentStatus            	sdpr.vehicles.enrollmentStatus
     * connectorType               		connectorType               	sdpr.vehicles.connectorType
     * make                        		make                        	sdpr.vehicles.make
     * subMake                     		subMake                     	sdpr.vehicles.subMake
     * market                      		market                      	sdpr.vehicles.market
     * lastUpdate                  		lastUpdate                  	NOW() (calculated)
     * year                        		year                        	sdpr.vehicles.year
	 *                                  warrantyStartDate               null
     */
    public function mapSdprVehiclesToSystemVehicleDataList(array $sdprVehiclesList, $options = []): array
    {
        try{
            $list = [];
            foreach ($sdprVehiclesList['success']['vehicles'] as $sdprVehicle) {

                $vin = $sdprVehicle['vin'];
                $systemVehicleData = new SystemVehicleData();
                $systemVehicleData->setVin($vin)
                    ->setUserId($options['userId'])
                    ->setVin($vin)
                    ->setBrandCode($this->mapSdprBrandCode($sdprVehicle['brandCode']))
                    ->setNickname($sdprVehicle['nickname'] ?? null)
                    ->setLabel($sdprVehicle['modelDescription'] ?? null)
                    ->setImageUrl(null)
                    ->setSdp(self::SDP_GSDP)
                    ->setLcdv(null)
                    ->setType($sdprVehicle['fuelType'] ?? null)
                    ->setRegTimestamp($sdprVehicle['regTimestamp'] ?? null)
                    ->setEnrollmentStatus($sdprVehicle['enrollmentStatus'] ?? null)
                    ->setConnectorType($sdprVehicle['connectorType'] ?? null)
                    ->setMake($sdprVehicle['make'] ?? null)
                    ->setSubMake($sdprVehicle['subMake'] ?? null)
                    ->setMarket($sdprVehicle['market'] ?? null)
                    ->setLastUpdate(strval(time()))
                    ->setYear($sdprVehicle['year'] ?? null)
                    ->setWarrantyStartDate(null);
                $list[$vin] = $systemVehicleData;
            }
        } catch (Throwable $e) {
            $this->logger->error(__METHOD__.' Error while mapping SDPR vehicles to SystemVehicleData', [
                'exception' => $e]);
            // just logging
            throw $e;
        }
        
        return $list;
    }

    private function mapSdprBrandCode(?string $sdprBrandCode): ?string
    {
        $mapping=[
            '56' => 'DG',
            '57' => 'JE',
            '58' => 'RM',
            '66' => 'AH',
            '70' => 'LA',
            '77' => 'FO',
            '83' => 'AR',
            '92' => 'CY',
            '98' => 'MA',
            '00' => 'FT',     
        ];

        $sdprBrandCode = trim($sdprBrandCode) ?? null;
        return $mapping[$sdprBrandCode] ?? null;
    }   
}