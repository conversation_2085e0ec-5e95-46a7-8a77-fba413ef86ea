<?php

namespace App\Model;

class FeatureCodeConfig 
{
    # https://stla-swx-confluence.atlassian.net/wiki/spaces/CAPS/pages/**********/FeatureCodes+Space+Middleware

    const NAE01 = [
        'VEHICLE_INFO' => [
            'code' => 'VEHICLE_INFO',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAE01',
            'backend' => 'SpaceMid',
            'config' => [
                'engine' => 'UNKNOWN',
            ],
        ],
        'VEHICLE_HEALTH_ALERTS' => [
            'code' => 'VEHICLE_HEALTH_ALERTS',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAE01',
            'backend' => 'SpaceMid',
            // 'config' => [],
        ],
        'VEHICLE_HEALTH_REPORT' => [
            'code' => 'VEHICLE_HEALTH_REPORT',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAE01',
            'backend' => 'SpaceMid',
            // 'config' => [],
        ],
    ];

    const NAK01 = [
        'SEND_TO_NAV' => [
            'code' => 'SEND_TO_NAV',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAK01',
            'config' => [
                'protocol' => 'network | ble'
            ],
        ],
    ];

    // const NAL01 = [
    //     'EV_ROUTING' => [
    //         'code' => 'EV_ROUTING',
    //         'status' => 'enable',
    //         'calcTimestamp' => null,
    //         'value' => 'NAL01',
    //         'backend' => 'SpaceMid',
    //         'config' => [
    //             'version' => '',
    //         ],
    //     ],
    // ];

    const NAM01 = [
        'DIGITAL_KEY' => [
            'code' => 'DIGITAL_KEY',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAM01',
            'backend' => 'SpaceMid',
            'config' => [
                'type' => 'NEA | Atlantis | Brain',
            ],
        ],
    ];

    const NAO01 = [
        'VEHICLE_INFO' => [
            'code' => 'VEHICLE_INFO',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAO01',
            'backend' => 'APIM',
            'config' => [
                'engine' => 'BEV',
            ],
        ],
    ];

    const NAO02 = [
        'VEHICLE_INFO' => [
            'code' => 'VEHICLE_INFO',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAO02',
            'backend' => 'APIM',
            'config' => [
                'engine' => 'PHEV',
            ],
        ],
    ];

    const NAS01 = [
        'CLIMATE_SCHEDULING' => [
            'code' => 'CLIMATE_SCHEDULING',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAS01',
            'backend' => 'APIM',
            'config' => [
                'schedule' => 4,
                'repeatBehaviour' => 'repeat',
                'shared' => false,
                'daysType' => [
                    'chooseDays',
                ],
                'editable' => [
                    'temperature' => [
                        'enable' => false,
                        'unit' => 'celsius',
                        'default' => 21,
                    ],
                ],
            ],
        ],
    ];

    const NAS02 = [
        'CLIMATE_SCHEDULING' => [
            'code' => 'CLIMATE_SCHEDULING',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAS02',
            'backend' => 'APIM',
            'config' => [
                'schedule' => 4,
                'repeatBehaviour' => 'repeat',
                'shared' => false,
                'daysType' => [
                    'chooseDays',
                ],
                'editable' => [
                    'temperature' => [
                        'enable' => false,
                        'unit' => 'celsius',
                        'default' => 21,
                    ],
                ],
            ],
        ],
    ];

    const NAU01 = [
        'E_ROUTES' => [
            'code' => 'E_ROUTES',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAU01',
            'backend' => 'SpaceMid',
            'config' => [
                'linkAndroid' => '',
                'linkIos' => '',
            ],
        ],
    ];

    const NAW01 = [
        'TRIPS' => [
            'code' => 'TRIPS',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAW01',
            'backend' => 'APIM',
            'config' => [
                'protocol' => 'network | ble',
            ],
        ],
        'VEHICLE_LOCATOR' => [
            'code' => 'VEHICLE_LOCATOR',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAW01',
            'backend' => 'APIM',
            'config' => [
                'location' => [
                    'trip',
                    'manual',
                    'remote',
                ],
                'refresh' => false,
            ],
        ],
    ];

    # All vehicles without NAW01
    const NON_NAW01 = [
        'VEHICLE_LOCATOR' => [
            'code' => 'VEHICLE_LOCATOR',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => '',
            'backend' => 'APIM',
            'config' => [
                'location' => [
                    'manual',
                ],
                'refresh' => false,
            ],
        ],
    ];

    const NBM01 = [
        'CHARGE_NOW_START' => [
            'code' => 'CHARGE_NOW_START',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NBM01',
            'backend' => 'APIM',
        ],
        'CHARGE_DEFERRED_START' => [
            'code' => 'CHARGE_DEFERRED_START',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NBM01',
            'backend' => 'APIM',
        ],
    ];

    # OUT OF J4U EMEA SCOPE
    const NBM02 = [
        'CHARGE_NOW_START' => [
            'code' => 'CHARGE_NOW_START',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NBM02',
            'backend' => 'APIM',
        ],
        'CHARGE_SCHEDULING' => [
            'code' => 'CHARGE_SCHEDULING',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NBM02',
            'backend' => 'APIM',
            'config' => [
                'schedule' => 1,
                'repeatBehaviour' => 'once',
                'shared' => false,
                'daysType' => [
                    'daily',
                ],
            ],
        ],
        'CHARGE_DEFERRED_START' => [
            'code' => 'CHARGE_DEFERRED_START',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NBM02',
            'backend' => 'APIM',
        ],
    ];
        
    # OUT OF J4U EMEA SCOPE
    const NBI01 = [
        'CHARGE_DEFERRED_STOP' => [
            'code' => 'CHARGE_DEFERRED_STOP',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NBI01',
            // 'backend' => 'SpaceMid | APIM | SDPR | MyMMid | MPH',
        ],
    ];

    const NBG01 = [
        'CHARGE_LIMIT' => [
            'code' => 'CHARGE_LIMIT',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NBG01',
            'backend' => 'APIM',
            'config' => [
                'version' => 1,
                'limits' => [
                    'regular',
                    'full',
                ],
            ],
        ],
    ];

    const NCG01 = [
        'BATTERY_USAGE_TIPS' => [
            'code' => 'BATTERY_USAGE_TIPS',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NCG01',
            // 'backend' => 'SpaceMid | APIM | SDPR | MyMMid | MPH',
        ],
    ];

    const NEE02 = [
        'DOOR_LOCK' => [
            'code' => 'DOOR_LOCK',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NEE02',
            'backend' => 'APIM',
            'config' => [
                'version' => 1,
            ],
        ],
        'DOOR_UNLOCK' => [
            'code' => 'DOOR_UNLOCK',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NEE02',
            'backend' => 'APIM',
            'config' => [
                'version' => 1,
            ],
        ],
        'VEHICLE_OPENINGS_INFO' => [
            'code' => 'VEHICLE_OPENINGS_INFO',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NEE02',
            'backend' => 'APIM',
        ],
    ];

    const NEF01 = [
        'HORN' => [
            'code' => 'HORN',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NEF01',
            'backend' => 'APIM',
            'config' => [
                'version' => 1,
            ],
        ],
        'LIGHTS' => [
            'code' => 'LIGHTS',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NEF01',
            'backend' => 'APIM',
            'config' => [
                'version' => 1,
            ],
        ],
    ];

    const NFC01 = [
        'CONNECTED_ALARM_STATUS' => [
            'code' => 'CONNECTED_ALARM_STATUS',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NDC01',
            'backend' => 'APIM',
            // 'config' => [],
        ],
    ];

    const NFD01 = [
        'CONNECTED_ALARM' => [
            'code' => 'CONNECTED_ALARM',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NDF01',
            'backend' => 'APIM',
            'config' => [
                'suppression' => 'disable',
            ],
        ],
    ];

    # "":
    #   STOLEN_CALL:
    #     code: STOLEN_CALL
    #     status: enable
    #     value: ""


    # Non FDS codes, to be added duing add vehicle flow
    const ADD_VEHICLE = [
        'GAS_STATION_LOCATOR' => [
            'code' => 'GAS_STATION_LOCATOR',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => '',
            'backend' => 'SpaceMid',
            'config' => [
                'partner' => 'google',
            ],
        ],
        'HYDROGEN_STATION_LOCATOR' => [
            'code' => 'HYDROGEN_STATION_LOCATOR',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => '',
            'backend' => 'SpaceMid',
            'config' => [
                'partner' => 'tomtom',
            ],
        ],
        'CHARGE_STATION_LOCATOR' => [
            'code' => 'CHARGE_STATION_LOCATOR',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => '',
            'backend' => 'SpaceMid',
            'config' => [
                'type' => 'partner',
                'url' => 'free2move://',
                'appName' => 'eSolution Charging',
                'packageName' => 'com.f2m.esolutions.esolutions',
                'appId' => '1608553487',
            ],
        ],
        // 'SMARTPHONE_STATION' => [
        //     'code' => 'SMARTPHONE_STATION',
        //     'status' => 'enable',
        //     'calcTimestamp' => null,
        //     'value' => '',
        //     'config' => [
        //         'type' => 'dashboard',
        //     ],
        // ],
        'CHARGING_STATION_MANAGEMENT' => [
            'code' => 'CHARGING_STATION_MANAGEMENT',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => '',
            'backend' => 'SpaceMid',
            'config' => [
                'partner' => 'f2mc',
                'enrolmentStatus' => 'noAccountLinking',
            ],
        ],
    ];

    #This Feature code is only added once if any of these FDS encountered NAW01 | NEF01 | NAO01 | NAO02, the first FDS encountered
    const VEHICLE_DEEP_REFRESH = [
        'code' => 'VEHICLE_DEEP_REFRESH',
        'status' => 'enable',
        'calcTimestamp' => null,
        'value' => 'NAW01',
        // 'backend' => 'SpaceMid',
        // 'config' => [],
    ];

    # OUT OF J4U EMEA SCOPE
    const NAB01 = [
        'UBI_PHYD' => [
            'code' => 'UBI_PHYD',
            'status' => 'enable',
            'calcTimestamp' => null,
            'value' => 'NAB01',
            'backend' => 'SpaceMid',
            // 'config' => [],
        ],
    ];
    
    // Get a specific feature config by name
    public static function getFeatureConfig(string $name)
    {
        $reflection = new \ReflectionClass(self::class);
        return $reflection->hasConstant($name) ? $reflection->getConstant($name) : null;
    }
    
    // Get all feature configs
    public static function getAllFeatureConfig(): array
    {
        $reflection = new \ReflectionClass(self::class);
        return $reflection->getConstants();
    }
}
