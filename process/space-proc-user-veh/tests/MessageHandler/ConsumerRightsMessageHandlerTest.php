<?php

namespace App\Tests\MessageHandler;

use App\Message\ConsumerRightsEnvelopeBody;
use App\MessageHandler\ConsumerRightsMessageHandler;
use App\Manager\ConsumerRightsManager;
use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class ConsumerRightsMessageHandlerTest extends TestCase
{
    private ConsumerRightsMessageHandler $handler;
    private ConsumerRightsManager $manager;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->manager = $this->createMock(ConsumerRightsManager::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->handler = new ConsumerRightsMessageHandler($this->manager);
        $this->handler->setLogger($this->logger);
    }

    public function testInvoke(): void
    {
        $message = new ConsumerRightsEnvelopeBody(
            'UPDATED_VEHICLE_CONTRACTS',
            'VR3ATTENTKJ622762',
            '6736320116974042a60293d1694a6029',
            '2025-05-13T09:02:04.941Z',
            'test-gigya-uid',
            'MEDIUM'
        );

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Processing consumer rights message', $this->anything());

        $this->manager->expects($this->once())
            ->method('handleConsumerRightsMessage')
            ->with($message)
            ->willReturn(new SuccessResponse('Success', Response::HTTP_OK));

        $result = $this->handler->__invoke($message);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }
}