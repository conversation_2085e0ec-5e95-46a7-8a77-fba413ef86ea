<?php

namespace App\Tests\Manager;

use App\Event\SpaceVehicleUpdatedEvent;
use App\Helper\BrandHelper;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\CatalogManager;
use App\Manager\SubscriptionManager;
use App\Manager\VehicleManager;
use App\Manager\Visual3DManager;
use App\Model\VehicleModel;
use App\Model\VehicleOrderModel;
use App\Service\CorvetService;
use App\Service\FeatureCodeService;
use App\Service\MongoAtlasQueryService;
use App\Service\SystemSdprClient;
use App\Service\SystemUserDataClient;
use App\Service\UserDataService;
use App\Service\VehicleLabelService;
use App\Service\VehicleService;
use App\Service\XFVehicleRefreshService;
use App\Service\XPVehicleRefreshService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class VehicleManagerTest extends TestCase
{
    private VehicleManager $vehicleManager;

    private VehicleService $vehicleService;
    private SerializerInterface $serializer;
    private DenormalizerInterface $denormalizer;
    private ValidatorInterface $validator;
    private MongoAtlasQueryService $mongoService;
    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;
    private VehicleLabelService $vehicleLabelService;
    private NormalizerInterface $normalizer;
    private CatalogManager $catalogManager;
    private SubscriptionManager $subscriptionManager;
    private UserDataService $userDataService;
    private FeatureCodeService $featureCodeService;

    public function setUp(): void
    {
        $this->vehicleService = $this->createMock(VehicleService::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->denormalizer = $this->createMock(DenormalizerInterface::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->dispatcher = $this->createMock(EventDispatcherInterface::class);
        $this->vehicleLabelService = $this->createMock(VehicleLabelService::class);
        $this->normalizer = $this->createMock(NormalizerInterface::class);
        $this->catalogManager = $this->createMock(CatalogManager::class);
        $this->subscriptionManager = $this->createMock(SubscriptionManager::class);
        $this->userDataService = $this->createMock(UserDataService::class);
        $this->featureCodeService = $this->createMock(FeatureCodeService::class);

        $this->vehicleManager = new VehicleManager(
            $this->vehicleService,
            $this->serializer,
            $this->denormalizer,
            $this->validator,
            $this->mongoService,
            $this->dispatcher,
            $this->createMock(XPVehicleRefreshService::class),
            $this->createMock(XFVehicleRefreshService::class),
            $this->userDataService,
            $this->createMock(CorvetService::class),
            $this->createMock(SystemUserDataClient::class),
            $this->createMock(Visual3DManager::class),
            $this->createMock(BrandHelper::class),
            $this->createMock(SystemSdprClient::class),
            $this->featureCodeService,
            $this->vehicleLabelService,
            $this->normalizer,
            $this->catalogManager,
            $this->subscriptionManager,
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );

        $this->logger = $this->createMock(LoggerInterface::class);
        $this->vehicleManager->setLogger($this->logger);
    }

    public function testVehicleValuesValidate(): void
    {
        $vehicleModel = new VehicleModel();
        $vehicleModel->setBrand('AP');
        $vehicleModel->setVehicleOrder(new VehicleOrderModel());
        $response = $this->vehicleManager->vehicleValuesValidate($vehicleModel);
        $this->assertIsArray($response);
    }

    public function testVehicleValuesValidateWithErrorsMessages(): void
    {
        $this->mockVehicleValuesValidate([
            $this->createError('Error 1'),
            $this->createError('Error 2'),
        ]);

        $vehicleModel = new VehicleModel();
        $vehicleModel->setBrand('AP');
        $vehicleModel->setVehicleOrder(new VehicleOrderModel());
        $response = $this->vehicleManager->vehicleValuesValidate($vehicleModel);
        $this->assertIsArray($response);
        $this->assertCount(4, $response);
    }

    public function testGetVehicleModel(): void
    {
        $this->mockGetVehicleModel(new VehicleModel());
        $content = ['brand' => 'OP', 'visual' => '', 'culture' => 'fr_FR', 'versionId' => '1GJOA5UMDKBDA0B0M09VD6FX'];

        $response = $this->vehicleManager->getVehicleModel($content);
        $this->assertInstanceOf(VehicleModel::class, $response);
    }

    public function testGetVehiclesWithEmptyDBOrders(): void
    {
        $service = $this->createMock(VehicleService::class);
        $service
            ->expects($this->once())
            ->method('getVehiclesOnOrder')
            ->willReturn(new WSResponse(200, ''));

        $serializer = $this->createMock(SerializerInterface::class);
        $denormalizer = $this->createMock(DenormalizerInterface::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $mongoService = $this->createMock(MongoAtlasQueryService::class);
        $dispatcher = $this->createMock(EventDispatcherInterface::class);
        $vehicleLabelService = $this->createMock(VehicleLabelService::class);
        $normalizer = $this->createMock(NormalizerInterface::class);
        $catalogManager = $this->createMock(CatalogManager::class);
        $subscriptionManager = $this->createMock(SubscriptionManager::class);

        $vehicleManager = new VehicleManager(
            $service,
            $serializer,
            $denormalizer,
            $validator,
            $mongoService,
            $dispatcher,
            $this->createMock(XPVehicleRefreshService::class),
            $this->createMock(XFVehicleRefreshService::class),
            $this->createMock(UserDataService::class),
            $this->createMock(CorvetService::class),
            $this->createMock(SystemUserDataClient::class),
            $this->createMock(Visual3DManager::class),
            $this->createMock(BrandHelper::class),
            $this->createMock(SystemSdprClient::class),
            $this->createMock(FeatureCodeService::class),
            $vehicleLabelService,
            $normalizer,
            $catalogManager,
            $subscriptionManager,
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        $actualResponse = $vehicleManager->getVehicles('123');

        $this->assertSame($actualResponse->getData(), ['vehicles' => []]);
        $this->assertSame($actualResponse->getCode(), 200);
    }

    public function testGetVehicleSummaryWithSuccess(): void
    {
        // mock getVehicleInfo response
        $data = json_encode([
            'documents' => [['vehicle' => ['vehicleInfo']]],
        ]);
        $mongoResponse = new WSResponse(Response::HTTP_OK, $data);
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn($mongoResponse);

        $vehicleModel = $this->createVehicleModelFixture();

        $this->denormalizer
            ->expects($this->once())
            ->method('denormalize')
            ->willReturn($vehicleModel);

        // mock service response
        $wsSuccessResponse = new WSResponse(Response::HTTP_OK, ['success' => ['pdf_url' => 'pdf_url']]);
        $this->vehicleService
            ->expects($this->once())
            ->method('getOrderSummary')
            ->willReturn($wsSuccessResponse);

        $vehicleId = '222';
        $userId = '444';
        $response = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);

        $arrResponse = $response->toArray();
        $code = $arrResponse['code'] ?? null;
        $data = $arrResponse['content']['success'] ?? [];

        $this->assertSame(Response::HTTP_OK, $code);
        $this->assertArrayHasKey('url', $data);
        $this->assertSame('pdf_url', $data['url']);
    }

    public function testGetVehicleSummaryWithError(): void
    {
        // mock getVehicleInfo response
        $data = json_encode([
            'documents' => [['vehicle' => ['vehicleInfo']]],
        ]);
        $mongoResponse = new WSResponse(Response::HTTP_OK, $data);
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn($mongoResponse);

        $vehicleModel = $this->createVehicleModelFixture();

        $this->denormalizer
            ->expects($this->once())
            ->method('denormalize')
            ->willReturn($vehicleModel);

        // mock service response
        $wsResponse = new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, ['success' => ['pdf_url' => 'pdf_url']]);
        $this->vehicleService
            ->expects($this->once())
            ->method('getOrderSummary')
            ->willReturn($wsResponse);

        $this->logger
            ->expects($this->once())
            ->method('error');

        $vehicleId = '222';
        $userId = '444';
        $response = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);

        $arrResponse = $response->toArray();

        $code = $arrResponse['code'] ?? null;
        $message = $arrResponse['content']['error']['message'] ?? null;
        $this->assertSame(Response::HTTP_INTERNAL_SERVER_ERROR, $code);
        $this->assertSame('server error', $message);
    }

    public function testGetVehicleSummaryWillThrowManagedException(): void
    {
        // mock getVehicleInfo response..that throw internal exception
        $data = json_encode([
            'documents' => [['vehicle' => null]],
        ]);
        $mongoResponse = new WSResponse(Response::HTTP_OK, $data);
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn($mongoResponse);

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('VehicleManager::getVehicleSummary Catched Exception'));

        $vehicleId = '222';
        $userId = '444';
        $response = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);

        $arrResponse = $response->toArray();

        $code = $arrResponse['code'] ?? null;
        $message = $arrResponse['content']['error']['message'] ?? null;
        $this->assertSame(Response::HTTP_NOT_FOUND, $code);
        $this->assertSame('vehicle not found', $message);
    }

    private function createVehicleModelFixture(): VehicleModel
    {
        $vehicleOrder = new VehicleOrderModel();
        $vehicleOrder->setOrderFormId('123')
            ->setMopId('MOP333');

        $vehicleModel = new VehicleModel();
        $vehicleModel->setBrand('Peugeot')
            ->setVehicleOrder($vehicleOrder)
            ->setCountry('FR');

        return $vehicleModel;
    }

    public function testGetVehiclesException(): void
    {
        $logger = $this->CreateMock(LoggerInterface::class);
        $service = $this->createMock(VehicleService::class);
        $service
            ->expects($this->once())
            ->method('getVehiclesOnOrder')
            ->willThrowException(new \Exception());
        $service->setLogger($logger);
        $serializer = $this->createMock(SerializerInterface::class);
        $denormalizer = $this->createMock(DenormalizerInterface::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $mongoService = $this->createMock(MongoAtlasQueryService::class);
        $dispatcher = $this->createMock(EventDispatcherInterface::class);
        $vehicleLabelService = $this->createMock(VehicleLabelService::class);
        $normalizer = $this->createMock(NormalizerInterface::class);
        $catalogManager = $this->createMock(CatalogManager::class);
        $subscriptionManager = $this->createMock(SubscriptionManager::class);

        $vehicleManager = new VehicleManager(
            $service,
            $serializer,
            $denormalizer,
            $validator,
            $mongoService,
            $dispatcher,
            $this->createMock(XPVehicleRefreshService::class),
            $this->createMock(XFVehicleRefreshService::class),
            $this->createMock(UserDataService::class),
            $this->createMock(CorvetService::class),
            $this->createMock(SystemUserDataClient::class),
            $this->createMock(Visual3DManager::class),
            $this->createMock(BrandHelper::class),
            $this->createMock(SystemSdprClient::class),
            $this->createMock(FeatureCodeService::class),
            $vehicleLabelService,
            $normalizer,
            $catalogManager,
            $subscriptionManager,
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        $vehicleManager->setLogger($logger);
        $actualResponse = $vehicleManager->getVehicles('123');

        $this->assertInstanceOf(ErrorResponse::class, $actualResponse);
    }

    private function mockGetVehicleModel(VehicleModel $vehicleModel): void
    {
        $this->serializer
            ->expects($this->once())
            ->method('deserialize')
            ->willReturn($vehicleModel);
    }

    private function mockVehicleValuesValidate(array $messages = []): void
    {
        $this->validator
            ->expects($this->exactly(2))
            ->method('validate')
            ->willReturn(new ConstraintViolationList($messages));
    }

    public function testCreateOrUpdateVehicleReturnSuccess(): void
    {
        $userId = '123';

        $vehicleModel = $this->getVehicleModel();
        $this->mockMethodGetVehicleModel($vehicleModel);

        $this->mockVehicleValuesValidate([]);

        $wsResponse = new WSResponse(Response::HTTP_OK, 'Success response');
        $this->mockServiceMethodCreateOrUpdateVehicle($userId, $vehicleModel, $wsResponse);

        $this->dispatcher->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(SpaceVehicleUpdatedEvent::class));

        $content = [
            'userId' => $userId,
            'visual' => 'FIAT',
            'mopId' => 'MOP-123',
            'culture' => 'fr_FR',
            'versionId' => 'version-id-test',
        ];

        $result = $this->vehicleManager->createOrUpdateVehicle($content);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $data = $result->getData();
        $this->assertSame('MOP-123', $data['mopId'] ?? '');
        $this->assertSame('Vehicle data has been saved successfully', $data['message'] ?? '');
    }

    public function testCreateOrUpdateVehicleReturnValidationErrors(): void
    {
        $vehicleModel = $this->getVehicleModel();
        $this->mockMethodGetVehicleModel($vehicleModel);

        $this->mockMethodVehicleValuesValidate([]);

        $content = [
            'visual' => 'FIAT',
            'mopId' => 'MOP-123',
            'culture' => 'fr_FR',
            'brand' => 'OP',
            'versionId' => 'version-id-test',
        ];
        $result = $this->vehicleManager->createOrUpdateVehicle($content);
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $arrResult = $result->toArray();
        $this->assertSame(Response::HTTP_BAD_REQUEST, $arrResult['code'] ?? 0);
        $message = $arrResult['content']['error']['message'] ?? '';
        $this->assertStringContainsString('The user ID value should not be blank.', $message);
    }

    public function testCreateOrUpdateVehicleReturnErrorOnSaving(): void
    {
        $userId = '123';

        $vehicleModel = $this->getVehicleModel();
        $this->mockMethodGetVehicleModel($vehicleModel);

        $this->mockMethodVehicleValuesValidate([]);

        $wsResponse = new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, 'Error response');
        $this->mockServiceMethodCreateOrUpdateVehicle($userId, $vehicleModel, $wsResponse);

        $content = [
            'userId' => $userId,
            'visual' => 'FIAT',
            'mopId' => 'MOP-123',
            'brand' => 'OP',
            'culture' => 'fr_FR',
            'versionId' => 'version-id-test',
        ];
        $result = $this->vehicleManager->createOrUpdateVehicle($content);
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $arrResult = $result->toArray();
        $this->assertSame(Response::HTTP_BAD_REQUEST, $arrResult['code'] ?? 0);
        $message = $arrResult['content']['error']['message'] ?? '';
        $this->assertStringContainsString('An error has occurred, vehicle data has not been saved', $message);
    }

    public function testCreateOrUpdateVehicleWillThrowExceptionAndReturnError(): void
    {
        $userId = '123';
        $this->mockMethodGetVehicleModelWillThrowException(new \Exception('Exception message', 123));

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('VehicleManager::createOrUpdateVehicle Catched Exception'));

        $content = [
            'userId' => $userId,
            'visual' => 'FIAT',
            'mopId' => 'MOP-123',
            'brand' => 'OP',
            'culture' => 'fr_FR',
            'versionId' => 'version-id-test',
        ];

        $result = $this->vehicleManager->createOrUpdateVehicle($content);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $arrResult = $result->toArray();
        $this->assertSame(400, $arrResult['code'] ?? 0);
        $message = $arrResult['content']['error']['message'] ?? '';
        $this->assertStringContainsString('Exception message', $message);
    }

    private function mockMethodGetVehicleModel(VehicleModel $vehicleModel): void
    {
        $this->serializer
            ->expects($this->once())
            ->method('deserialize')
            ->willReturn($vehicleModel);
    }

    private function mockMethodGetVehicleModelWillThrowException(\Throwable $e): void
    {
        $this->serializer
            ->expects($this->once())
            ->method('deserialize')
            ->willThrowException($e);
    }

    private function mockMethodVehicleValuesValidate(array $messages = []): void
    {
        $this->validator
            ->expects($this->exactly(2))
            ->method('validate')
            ->willReturn(new ConstraintViolationList($messages));
    }

    private function mockServiceMethodCreateOrUpdateVehicle(
        string $userId,
        VehicleModel $vehicleModel,
        WSResponse $response
    ): void {
        $this->vehicleService
            ->expects($this->once())
            ->method('createOrUpdateVehicle')
            ->with(
                $userId,
                $vehicleModel
            )
            ->willReturn($response);
    }

    private function getVehicleModel(): VehicleModel
    {
        $vehicleModel = new VehicleModel();
        $vehicleModel->setId('678');
        $vehicleModel->setBrand('AP');
        $vehicleModel->setVisual('AP-test');
        $vehicleModel->setLabel('Label Test');
        $vehicleModel->setVersionId('1.2.3');

        $vehicleOrder = new VehicleOrderModel();
        $vehicleOrder->setMopId('MOP-123');
        $vehicleOrder->setOrderFormId('556788');

        $vehicleModel->setVehicleOrder($vehicleOrder);

        return $vehicleModel;
    }

    private function getVehicleModelToArray(): array
    {
        $vehicle = [
            'id' => '678',
            'brand' => 'AP',
            'visual' => 'AP-test',
            'label' => 'Label Test',
            'versionId' => '1.2.3',
            'vehicleOrder' => [
                'mopId' => 'MOP-123',
                'orderFormId' => '556788',
            ],
        ];

        return $vehicle;
    }

    public function testMarkOrdersAsRead(): void
    {
        $logger = $this->CreateMock(LoggerInterface::class);
        $service = $this->createMock(VehicleService::class);
        $service->expects($this->any())
            ->method('setOrderIsUpdated')
            ->willReturn(new WSResponse(200, 'updated OK'));
        $service->setLogger($logger);
        $serializer = $this->createMock(SerializerInterface::class);
        $denormalizer = $this->createMock(DenormalizerInterface::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $mongoService = $this->createMock(MongoAtlasQueryService::class);
        $dispatcher = $this->createMock(EventDispatcherInterface::class);
        $vehicleLabelService = $this->createMock(VehicleLabelService::class);
        $normalizer = $this->createMock(NormalizerInterface::class);
        $catalogManager = $this->createMock(CatalogManager::class);
        $subscriptionManager = $this->createMock(SubscriptionManager::class);

        $vehicleManager = new VehicleManager(
            $service,
            $serializer,
            $denormalizer,
            $validator,
            $mongoService,
            $dispatcher,
            $this->createMock(XPVehicleRefreshService::class),
            $this->createMock(XFVehicleRefreshService::class),
            $this->createMock(UserDataService::class),
            $this->createMock(CorvetService::class),
            $this->createMock(SystemUserDataClient::class),
            $this->createMock(Visual3DManager::class),
            $this->createMock(BrandHelper::class),
            $this->createMock(SystemSdprClient::class),
            $this->createMock(FeatureCodeService::class),
            $vehicleLabelService,
            $normalizer,
            $catalogManager,
            $subscriptionManager,
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        $vehicleManager->setLogger($logger);
        $response = $vehicleManager->markOrdersAsRead('123', ['v1', 'v2']);
        $this->assertEquals($response, null);
    }

    public function testGetVehicleSummaryReturnSuccessResponse(): void
    {
        $vehicleModel = $this->getVehicleModel();
        $this->mockMethodGetVehicleInfo($vehicleModel);
        $wsResponse = new WSResponse(Response::HTTP_OK, ['success' => ['pdf_url' => 'pdf url']]);
        $this->mockServiceMethodGetOrderSummary($wsResponse);

        $userId = '123';
        $vehicleId = 'MOP-123';
        $result = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('pdf url', $result->getData()['url'] ?? '');
    }

    public function testGetVehicleSummaryReturnErrorResponse(): void
    {
        $vehicleModel = $this->getVehicleModel();
        $this->mockMethodGetVehicleInfo($vehicleModel);
        $wsResponse = new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, ['error' => ['message' => 'Error message']]);
        $this->mockServiceMethodGetOrderSummary($wsResponse);

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('VehicleManager::getVehicleSummary Error response'));

        $userId = '123';
        $vehicleId = 'MOP-123';
        $result = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $data = $result->toArray();
        $this->assertSame(Response::HTTP_INTERNAL_SERVER_ERROR, $data['code'] ?? 0);
        $this->assertSame('Error message', $data['content']['error']['message'] ?? '');
    }

    public function testGetVehicleSummaryThrowExceptionAndReturnErrorResponse(): void
    {
        $this->mockMethodGetVehicleInfoWillThrowException(new \Exception('Exception message', Response::HTTP_INTERNAL_SERVER_ERROR));

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('VehicleManager::getVehicleSummary Catched Exception'));

        $userId = '123';
        $vehicleId = 'MOP-123';
        $result = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $data = $result->toArray();
        $this->assertSame(Response::HTTP_INTERNAL_SERVER_ERROR, $data['code'] ?? 0);
        $this->assertSame('Exception message', $data['content']['error']['message'] ?? '');
    }

    public function testManageOVData(): void
    {
        $versionId = '1GJOA5UMDKBDA0B0M09VD6FX';
        $vin = 'test-vin';
        $vehicle = [
            'versionId' => $versionId,
            'vin' => $vin,
            'brand' => 'OP',
            'country' => 'FR',
        ];
        $attributes['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] = ['DZJ20CD', 'DZVCBCD', 'DZZ0VCD'];
        $vehicleExpected = [
            'versionId' => $versionId,
            'vin' => $vin,
            'brand' => 'VX',
            'country' => 'GB',
            'language' => 'en',
        ];

        $this->vehicleService->expects($this->once())
        ->method('isOVVehicle')
        ->willReturn(true);

        $this->vehicleService->expects($this->once())
        ->method('getVehicleBrand')
        ->willReturn('VX');

        $response = $this->vehicleManager->manageOVData($vehicle);

        $this->assertSame($vehicleExpected, $response);
        $this->assertIsArray($response);
    }

    private function mockMethodGetVehicleInfoWillThrowException(\Exception $e): void
    {
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willThrowException($e);
    }

    private function mockMethodGetVehicleInfo(VehicleModel $vehicleModel): void
    {
        $data = json_encode([
            'documents' => [
                ['vehicle' => $this->getVehicleModelToArray()],
            ],
        ]);
        $response = new WSResponse(Response::HTTP_OK, $data);

        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn($response);

        $this->denormalizer
            ->expects($this->once())
            ->method('denormalize')
            ->willReturn($vehicleModel);
    }

    private function mockServiceMethodGetOrderSummary(WSResponse $wsResponse): void
    {
        $this->vehicleService
            ->expects($this->once())
            ->method('getOrderSummary')
            ->willReturn($wsResponse);
    }

    private function createError($message)
    {
        $errorMock = $this->createMock(ConstraintViolationInterface::class);
        $errorMock->expects($this->any())
            ->method('getMessage')
            ->willReturn($message);

        return $errorMock;
    }

    public function testGetVehicleDetailWithMockedResponse(): void
    {
        // Skip this test for now as it requires more complex mocking
        $this->markTestSkipped('This test requires more complex mocking of the VehicleManager::getVehicleDetail method');

        $userId = 'testUserId';
        $critariaValue = 'testVin';
        $critariaKey = 'vin';
        $language = 'en';
        $country = 'FR';

        $mockedVehicleData = [
            "documents" => [
                [
                    'vehicle' => [
                        [
                            "modelDescription" => "Peugeot 3008",
                            "shortLabel" => "3008",
                            "regTimeStamp" => 1609459200, // January 1, 2021
                            "brand" => "Peugeot",
                            "country" => "FR",
                            "versionId" => "1.2 PureTech",
                            "year" => "2021",
                            "type" => "SUV",
                            "mileage" => [
                                "value" => 15000,
                                "date" => 1612137600 // February 1, 2021
                            ],
                            "vin" => "VF3ATTENTGY182416",
                            "picture" => "https://example.com/picture.jpg",
                            "sdp" => "SDP1",
                            "isOrder" => true,
                            "featureCode" => [
                                [
                                    "code" => "FC1",
                                    "status" => "active",
                                    "value" => "value1",
                                    "config" => [
                                        "type" => "type1"
                                    ]
                                ],
                                [
                                    "code" => "FC2",
                                    "status" => "inactive",
                                    "value" => "value2",
                                    "config" => [
                                        "type" => "type2"
                                    ]
                                ]
                            ],
                            "addStatus" => "added",
                            "lastUpdate" => "2021-02-01T12:00:00Z",
                            "make" => "Peugeot",
                            "market" => "Europe",
                            "warrantyStartDate" => "2021-01-01T00:00:00Z",
                            "isO2x" => true
                        ]
                    ]
            ]
        ]];

        $mockedResponse = new WSResponse(
            Response::HTTP_OK,
            json_encode($mockedVehicleData)
        );

        $this->mongoService->expects($this->any())
            ->method('find')
            ->willReturn($mockedResponse);

        $response = $this->vehicleManager->getVehicleDetail($userId, $critariaValue, $language, $country, $critariaKey);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());

        $expectedVehicleXPFormat = [
            'vehicleInfo' => [
                'vin' => 'VF3ATTENTGY182416',
                'lcdv' => null,
                'visual' => 'https://example.com/picture.jpg',
                'short_label' => '3008',
                'warranty_start_date' => '2021-01-01T00:00:00Z',
                'attributes' => [],
                'type_vehicle' => 'SUV',
                'mileage' => [
                    'value' => 15000,
                    'date' => 1612137600
                ],
            ],
            'eligibility' => [],
            'vehicleProducts' => [
                'productsCatalog' => [],
                'purchasedProducts' => [],
                'productGroupNameStatus' => []
            ],
            'settingsUpdate' => '2021-02-01T12:00:00Z'
        ];

        $this->assertEquals($expectedVehicleXPFormat, $response->getData());
   }

   public function testRemoveNullValues()
    {
        $input = [
            'key1' => 'value1',
            'key2' => null,
            'key3' => 'value3',
            'key4' => [
                'nestedKey1' => 'nestedValue1',
                'nestedKey2' => null,
                'nestedKey3' => 'nestedValue3',
            ],
            'key5' => null,
        ];

        $expectedOutput = [
            'key1' => 'value1',
            'key3' => 'value3',
            'key4' => [
                'nestedKey1' => 'nestedValue1',
                'nestedKey3' => 'nestedValue3',
            ],
        ];

        $result = $this->vehicleManager->removeNullValues($input);

        $this->assertEquals($expectedOutput, $result);
    }

    public function testRemoveNullValuesARealCase(): void
    {
        $jsonContent = '{"success":[{"vin":"VIN123456789012345","lcdv":null,"visual":"http:\/\/example.com\/picture1.jpg","short_label":"My Car","warranty_start_date":null,"command":null,"sdp":"SDP1"},{"vin":"VIN987654321098765","lcdv":"LCDV2","visual":"http:\/\/example.com\/picture2.jpg","short_label":"Family Car","warranty_start_date":1234567890,"command":"command2","sdp":"SDP2"}]}';
        $input = json_decode($jsonContent, true);

        $expectedOutput = [
            'success' => [
                [
                    'vin' => 'VIN123456789012345',
                    'visual' => 'http://example.com/picture1.jpg',
                    'short_label' => 'My Car',
                    'sdp' => 'SDP1',
                ],
                [
                    'vin' => 'VIN987654321098765',
                    'lcdv' => 'LCDV2',
                    'visual' => 'http://example.com/picture2.jpg',
                    'short_label' => 'Family Car',
                    'warranty_start_date' => 1234567890,
                    'command' => 'command2',
                    'sdp' => 'SDP2',
                ],
            ],
        ];

        $result = $this->vehicleManager->removeNullValues($input);

        $this->assertEquals($expectedOutput, $result);
    }

    public function testHandleCustomerRightsUpdateSuccess(): void
    {
        // Create a vehicle object with methods
        $vehicleMock = $this->createMock(\App\MongoDB\UserData\UserDataDocument\Vehicle::class);
        $vehicleMock->method('getType')->willReturn('CAR');
        $vehicleMock->method('getVersionId')->willReturn('version123');
        $vehicleMock->method('getBrand')->willReturn('brand123');
        $vehicleMock->method('getCountry')->willReturn('FR');
        
        $userDocument = [
            'userId' => 'test-user-123',
            'vehicle' => $vehicleMock,
            'f2mc' => ['some' => 'data']
        ];
        
        // Setup service mocks
        $this->userDataService->expects($this->once())
            ->method('getVehicleAndUserIdByVin')
            ->with('VIN123')
            ->willReturn($userDocument);
        
        // Create a real VehicleManager but with a mocked CorvetService
        $corvetServiceMock = $this->createMock(\App\Service\CorvetService::class);
        $corvetServiceMock->expects($this->once())
            ->method('getData')  // Try getData instead of getVehicleAttributes
            ->willReturn([
                'VEHICULE' => [
                    'LISTE_ATTRIBUTES_7' => [
                        'ATTRIBUT' => ['attr1', 'attr2']
                    ],
                    'LCDV16' => 'lcdv123'
                ]
            ]);
        
        // Replace the existing CorvetService in the vehicleManager with our mock
        $this->vehicleManager = new VehicleManager(
            $this->vehicleService,
            $this->serializer,
            $this->denormalizer,
            $this->validator,
            $this->mongoService,
            $this->dispatcher,
            $this->createMock(XPVehicleRefreshService::class),
            $this->createMock(XFVehicleRefreshService::class),
            $this->userDataService,
            $corvetServiceMock,  // Use our mocked CorvetService
            $this->createMock(SystemUserDataClient::class),
            $this->createMock(Visual3DManager::class),
            $this->createMock(BrandHelper::class),
            $this->createMock(SystemSdprClient::class),
            $this->featureCodeService,
            $this->vehicleLabelService,
            $this->normalizer,
            $this->catalogManager,
            $this->subscriptionManager,
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        
        // Add debug logging expectations
        $this->logger->expects($this->atLeastOnce())
            ->method('info')
            ->with($this->stringContains('Handling customer rights update'));
        
        $this->vehicleManager->setLogger($this->logger);
        
        $result = $this->vehicleManager->handleCustomerRightsUpdate(
            'UPDATED_VEHICLE_CONTRACTS',
            'VIN123',
            'test-user-123',
            '2023-01-01T12:00:00Z',
            'gigya-123',
            'OWNER'
        );
        
        $this->assertIsBool($result);
    }

    public function testHandleCustomerRightsUpdateNoUserDocument(): void
    {
        $this->userDataService->expects($this->once())
            ->method('getVehicleAndUserIdByVin')
            ->with('VIN123')
            ->willReturn(null);
        
        $result = $this->vehicleManager->handleCustomerRightsUpdate(
            'UPDATED_VEHICLE_CONTRACTS',
            'VIN123',
            'test-user-123',
            '2023-01-01T12:00:00Z',
            'gigya-123'
        );
        
        $this->assertFalse($result);
    }

    public function testHandleCustomerRightsUpdateNoLcdv(): void
    {
        // Create a vehicle object with methods
        $vehicleMock = $this->createMock(\App\MongoDB\UserData\UserDataDocument\Vehicle::class);
        $vehicleMock->method('getType')->willReturn('CAR');
        $vehicleMock->method('getVersionId')->willReturn('version123');
        $vehicleMock->method('getBrand')->willReturn('brand123');
        $vehicleMock->method('getCountry')->willReturn('FR');
        
        $userDocument = [
            'userId' => 'test-user-123',
            'vehicle' => $vehicleMock
        ];
        
        $this->userDataService->expects($this->once())
            ->method('getVehicleAndUserIdByVin')
            ->willReturn($userDocument);
        
        // Create a real VehicleManager but with a mocked CorvetService
        $corvetServiceMock = $this->createMock(\App\Service\CorvetService::class);
        $corvetServiceMock->expects($this->once())
            ->method('getData')  // Try getData instead of getVehicleAttributes
            ->willReturn(['VEHICULE' => []]);
        
        // Replace the existing CorvetService in the vehicleManager with our mock
        $this->vehicleManager = new VehicleManager(
            $this->vehicleService,
            $this->serializer,
            $this->denormalizer,
            $this->validator,
            $this->mongoService,
            $this->dispatcher,
            $this->createMock(XPVehicleRefreshService::class),
            $this->createMock(XFVehicleRefreshService::class),
            $this->userDataService,
            $corvetServiceMock,  // Use our mocked CorvetService
            $this->createMock(SystemUserDataClient::class),
            $this->createMock(Visual3DManager::class),
            $this->createMock(BrandHelper::class),
            $this->createMock(SystemSdprClient::class),
            $this->featureCodeService,
            $this->vehicleLabelService,
            $this->normalizer,
            $this->catalogManager,
            $this->subscriptionManager,
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Error lcdv not found while calling corvet'));
        
        $this->vehicleManager->setLogger($this->logger);
        
        $result = $this->vehicleManager->handleCustomerRightsUpdate(
            'UPDATED_VEHICLE_CONTRACTS',
            'VIN123',
            'test-user-123',
            '2023-01-01T12:00:00Z',
            'gigya-123'
        );
        
        $this->assertFalse($result);
    }

    public function testHandleCustomerRightsUpdateExceptionHandling(): void
    {
        $this->userDataService->expects($this->once())
            ->method('getVehicleAndUserIdByVin')
            ->willThrowException(new \Exception('Test exception'));
        
        $result = $this->vehicleManager->handleCustomerRightsUpdate(
            'UPDATED_VEHICLE_CONTRACTS',
            'VIN123',
            'test-user-123',
            '2023-01-01T12:00:00Z',
            'gigya-123'
        );
        
        $this->assertFalse($result);
    }

    public function testHandleCustomerRightsUpdateImplementation(): void
    {
        // Let's examine the VehicleManager::handleCustomerRightsUpdate method
        $reflectionClass = new \ReflectionClass(VehicleManager::class);
        $method = $reflectionClass->getMethod('handleCustomerRightsUpdate');
        
        // Get the method's source code to understand its implementation
        $fileName = $reflectionClass->getFileName();
        $startLine = $method->getStartLine();
        $endLine = $method->getEndLine();
        
        $fileContent = file($fileName);
        $methodSource = implode('', array_slice($fileContent, $startLine - 1, $endLine - $startLine + 1));
        
        // Output the method source for debugging
        echo "\nMethod implementation:\n" . $methodSource . "\n";
        
        $this->addToAssertionCount(1); // Add a dummy assertion
    }

    /**
     * Test successful retrieval of vehicle features
     */
    public function testGetVehicleFeaturesSuccess(): void
    {
        // Mock dependencies
        $userDataServiceMock = $this->createMock(\App\Service\UserDataService::class);
        $loggerMock = $this->createMock(\Psr\Log\LoggerInterface::class);
        
        // Create the manager with mocked dependencies
        $vehicleManager = new \App\Manager\VehicleManager(
            $this->vehicleService,
            $this->serializer,
            $this->denormalizer,
            $this->validator,
            $this->mongoService,
            $this->dispatcher,
            $this->createMock(\App\Service\XPVehicleRefreshService::class),
            $this->createMock(\App\Service\XFVehicleRefreshService::class),
            $userDataServiceMock,
            $this->createMock(\App\Service\CorvetService::class),
            $this->createMock(\App\Service\SystemUserDataClient::class),
            $this->createMock(\App\Manager\Visual3DManager::class),
            $this->createMock(\App\Helper\BrandHelper::class),
            $this->createMock(\App\Service\SystemSdprClient::class),
            $this->createMock(\App\Service\FeatureCodeService::class),
            $this->createMock(\App\Service\VehicleLabelService::class),
            $this->createMock(\Symfony\Component\Serializer\Normalizer\NormalizerInterface::class),
            $this->createMock(\App\Manager\CatalogManager::class),
            $this->createMock(\App\Manager\SubscriptionManager::class),
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        
        $vehicleManager->setLogger($loggerMock);
        
        // Test data
        $userId = 'test-user-123';
        $vin = 'TEST-VIN-456';
        
        // Mock vehicle data that would be returned by the UserDataService
        $vehicleData = [
            'vehicle' => [
                [
                    'vin' => $vin,
                    'brand' => 'OP',
                    'featureCode' => [
                        [
                            'code' => 'VEHICLE_INFO',
                            'status' => 'enable',
                            'value' => 'NAE01',
                            'config' => [
                                'engine' => 'ICE'
                            ]
                        ],
                        [
                            'code' => 'CHARGING_STATION_MANAGEMENT',
                            'status' => 'enable',
                            'value' => 'NAO01',
                            'config' => [
                                'partner' => 'f2mc',
                                'enrolmentStatus' => 'complete'
                            ]
                        ]
                    ]
                ]
            ]
        ];
        
        // Configure the mock to return our test data
        $userDataServiceMock->expects($this->once())
            ->method('getVehicle')
            ->with($userId, $vin)
            ->willReturn($vehicleData);
        
        // Call the method under test
        $result = $vehicleManager->getVehicleFeatures($userId, $vin);
        
        // Assertions
        $this->assertInstanceOf(\App\Helper\SuccessResponse::class, $result);
        $this->assertEquals(200, $result->getCode());
        
        $resultData = $result->getData();
        $this->assertIsArray($resultData);
        $this->assertArrayHasKey('features', $resultData);
        $this->assertCount(2, $resultData['features']);
        
        // Check first feature
        $this->assertEquals('VEHICLE_INFO', $resultData['features'][0]['code']);
        $this->assertEquals('enable', $resultData['features'][0]['status']);
        $this->assertEquals('NAE01', $resultData['features'][0]['value']);
        $this->assertEquals('ICE', $resultData['features'][0]['config']['engine']);
        
        // Check second feature
        $this->assertEquals('CHARGING_STATION_MANAGEMENT', $resultData['features'][1]['code']);
        $this->assertEquals('enable', $resultData['features'][1]['status']);
        $this->assertEquals('NAO01', $resultData['features'][1]['value']);
        $this->assertEquals('f2mc', $resultData['features'][1]['config']['partner']);
        $this->assertEquals('complete', $resultData['features'][1]['config']['enrolmentStatus']);
    }

    /**
     * Test when vehicle is not found
     */
    public function testGetVehicleFeaturesVehicleNotFound(): void
    {
        // Mock dependencies
        $userDataServiceMock = $this->createMock(\App\Service\UserDataService::class);
        $loggerMock = $this->createMock(\Psr\Log\LoggerInterface::class);
        
        // Create the manager with mocked dependencies
        $vehicleManager = new \App\Manager\VehicleManager(
            $this->vehicleService,
            $this->serializer,
            $this->denormalizer,
            $this->validator,
            $this->mongoService,
            $this->dispatcher,
            $this->createMock(\App\Service\XPVehicleRefreshService::class),
            $this->createMock(\App\Service\XFVehicleRefreshService::class),
            $userDataServiceMock,
            $this->createMock(\App\Service\CorvetService::class),
            $this->createMock(\App\Service\SystemUserDataClient::class),
            $this->createMock(\App\Manager\Visual3DManager::class),
            $this->createMock(\App\Helper\BrandHelper::class),
            $this->createMock(\App\Service\SystemSdprClient::class),
            $this->createMock(\App\Service\FeatureCodeService::class),
            $this->createMock(\App\Service\VehicleLabelService::class),
            $this->createMock(\Symfony\Component\Serializer\Normalizer\NormalizerInterface::class),
            $this->createMock(\App\Manager\CatalogManager::class),
            $this->createMock(\App\Manager\SubscriptionManager::class),
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        
        $vehicleManager->setLogger($loggerMock);
        
        // Test data
        $userId = 'test-user-123';
        $vin = 'TEST-VIN-456';
        
        // Configure the mock to return null (vehicle not found)
        $userDataServiceMock->expects($this->once())
            ->method('getVehicle')
            ->with($userId, $vin)
            ->willReturn(null);
        
        // Expect a log message
        $loggerMock->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Error vehicle not exist'));
        
        // Call the method under test
        $result = $vehicleManager->getVehicleFeatures($userId, $vin);
        
        // Assertions
        $this->assertInstanceOf(\App\Helper\ErrorResponse::class, $result);
        $this->assertEquals(404, $result->getCode());
        
        // Convert to array to check the content
        $resultArray = $result->toArray();
        $this->assertIsArray($resultArray['content']);
        $this->assertArrayHasKey('error', $resultArray['content']);
        $this->assertEquals('Vehicle does not exist', $resultArray['content']['error']['message']);
    }

    /**
     * Test when vehicle data is empty
     */
    public function testGetVehicleFeaturesEmptyVehicleData(): void
    {
        // Mock dependencies
        $userDataServiceMock = $this->createMock(\App\Service\UserDataService::class);
        $loggerMock = $this->createMock(\Psr\Log\LoggerInterface::class);
        
        // Create the manager with mocked dependencies
        $vehicleManager = new \App\Manager\VehicleManager(
            $this->vehicleService,
            $this->serializer,
            $this->denormalizer,
            $this->validator,
            $this->mongoService,
            $this->dispatcher,
            $this->createMock(\App\Service\XPVehicleRefreshService::class),
            $this->createMock(\App\Service\XFVehicleRefreshService::class),
            $userDataServiceMock,
            $this->createMock(\App\Service\CorvetService::class),
            $this->createMock(\App\Service\SystemUserDataClient::class),
            $this->createMock(\App\Manager\Visual3DManager::class),
            $this->createMock(\App\Helper\BrandHelper::class),
            $this->createMock(\App\Service\SystemSdprClient::class),
            $this->createMock(\App\Service\FeatureCodeService::class),
            $this->createMock(\App\Service\VehicleLabelService::class),
            $this->createMock(\Symfony\Component\Serializer\Normalizer\NormalizerInterface::class),
            $this->createMock(\App\Manager\CatalogManager::class),
            $this->createMock(\App\Manager\SubscriptionManager::class),
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        
        $vehicleManager->setLogger($loggerMock);
        
        // Test data
        $userId = 'test-user-123';
        $vin = 'TEST-VIN-456';
        
        // Mock vehicle data with empty vehicle array
        $vehicleData = [
            'vehicle' => []
        ];
        
        // Configure the mock to return our test data
        $userDataServiceMock->expects($this->once())
            ->method('getVehicle')
            ->with($userId, $vin)
            ->willReturn($vehicleData);
        
        // Call the method under test
        $result = $vehicleManager->getVehicleFeatures($userId, $vin);
        
        // Assertions
        $this->assertInstanceOf(\App\Helper\ErrorResponse::class, $result);
        $this->assertEquals(404, $result->getCode());
        
        // Convert to array to check the content
        $resultArray = $result->toArray();
        $this->assertIsArray($resultArray['content']);
        $this->assertArrayHasKey('error', $resultArray['content']);
        $this->assertEquals('Vehicle data not found', $resultArray['content']['error']['message']);
    }

    /**
     * Test when vehicle has no feature codes
     */
    public function testGetVehicleFeaturesNoFeatureCodes(): void
    {
        // Mock dependencies
        $userDataServiceMock = $this->createMock(\App\Service\UserDataService::class);
        $loggerMock = $this->createMock(\Psr\Log\LoggerInterface::class);
        
        // Create the manager with mocked dependencies
        $vehicleManager = new \App\Manager\VehicleManager(
            $this->vehicleService,
            $this->serializer,
            $this->denormalizer,
            $this->validator,
            $this->mongoService,
            $this->dispatcher,
            $this->createMock(\App\Service\XPVehicleRefreshService::class),
            $this->createMock(\App\Service\XFVehicleRefreshService::class),
            $userDataServiceMock,
            $this->createMock(\App\Service\CorvetService::class),
            $this->createMock(\App\Service\SystemUserDataClient::class),
            $this->createMock(\App\Manager\Visual3DManager::class),
            $this->createMock(\App\Helper\BrandHelper::class),
            $this->createMock(\App\Service\SystemSdprClient::class),
            $this->createMock(\App\Service\FeatureCodeService::class),
            $this->createMock(\App\Service\VehicleLabelService::class),
            $this->createMock(\Symfony\Component\Serializer\Normalizer\NormalizerInterface::class),
            $this->createMock(\App\Manager\CatalogManager::class),
            $this->createMock(\App\Manager\SubscriptionManager::class),
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        
        $vehicleManager->setLogger($loggerMock);
        
        // Test data
        $userId = 'test-user-123';
        $vin = 'TEST-VIN-456';
        
        // Mock vehicle data without feature codes
        $vehicleData = [
            'vehicle' => [
                [
                    'vin' => $vin,
                    'brand' => 'OP'
                    // No featureCode field
                ]
            ]
        ];
        
        // Configure the mock to return our test data
        $userDataServiceMock->expects($this->once())
            ->method('getVehicle')
            ->with($userId, $vin)
            ->willReturn($vehicleData);
        
        // Expect a log message
        $loggerMock->expects($this->once())
            ->method('info')
            ->with($this->stringContains('No feature codes found for vehicle'));
        
        // Call the method under test
        $result = $vehicleManager->getVehicleFeatures($userId, $vin);
        
        // Assertions
        $this->assertInstanceOf(\App\Helper\SuccessResponse::class, $result);
        $this->assertEquals(200, $result->getCode());
        
        $resultData = $result->getData();
        $this->assertIsArray($resultData);
        $this->assertArrayHasKey('features', $resultData);
        $this->assertEmpty($resultData['features']);
    }

    /**
     * Test when an exception occurs
     */
    public function testGetVehicleFeaturesException(): void
    {
        // Mock dependencies
        $userDataServiceMock = $this->createMock(\App\Service\UserDataService::class);
        $loggerMock = $this->createMock(\Psr\Log\LoggerInterface::class);
        
        // Create the manager with mocked dependencies
        $vehicleManager = new \App\Manager\VehicleManager(
            $this->vehicleService,
            $this->serializer,
            $this->denormalizer,
            $this->validator,
            $this->mongoService,
            $this->dispatcher,
            $this->createMock(\App\Service\XPVehicleRefreshService::class),
            $this->createMock(\App\Service\XFVehicleRefreshService::class),
            $userDataServiceMock,
            $this->createMock(\App\Service\CorvetService::class),
            $this->createMock(\App\Service\SystemUserDataClient::class),
            $this->createMock(\App\Manager\Visual3DManager::class),
            $this->createMock(\App\Helper\BrandHelper::class),
            $this->createMock(\App\Service\SystemSdprClient::class),
            $this->createMock(\App\Service\FeatureCodeService::class),
            $this->createMock(\App\Service\VehicleLabelService::class),
            $this->createMock(\Symfony\Component\Serializer\Normalizer\NormalizerInterface::class),
            $this->createMock(\App\Manager\CatalogManager::class),
            $this->createMock(\App\Manager\SubscriptionManager::class),
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        
        $vehicleManager->setLogger($loggerMock);
        
        // Test data
        $userId = 'test-user-123';
        $vin = 'TEST-VIN-456';
        
        // Configure the mock to throw an exception
        $userDataServiceMock->expects($this->once())
            ->method('getVehicle')
            ->with($userId, $vin)
            ->willThrowException(new \Exception('Database connection error'));
        
        // Expect a log message
        $loggerMock->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Error getting  vehicle features'));
        
        // Call the method under test
        $result = $vehicleManager->getVehicleFeatures($userId, $vin);
        
        // Assertions
        $this->assertInstanceOf(\App\Helper\ErrorResponse::class, $result);
        $this->assertEquals(500, $result->getCode());
        
        // Convert to array to check the content
        $resultArray = $result->toArray();
        $this->assertIsArray($resultArray['content']);
        $this->assertArrayHasKey('error', $resultArray['content']);
        $this->assertEquals('Failed to retrieve vehicle features', $resultArray['content']['error']['message']);
    }
}
