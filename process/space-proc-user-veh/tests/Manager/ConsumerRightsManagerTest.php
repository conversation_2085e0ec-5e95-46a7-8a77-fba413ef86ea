<?php

namespace App\Tests\Manager;

use App\Manager\ConsumerRightsManager;
use App\Manager\VehicleManager;
use App\Message\ConsumerRightsEnvelopeBody;
use App\Service\UserDataService;
use App\Helper\SuccessResponse;
use App\Helper\ErrorResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class ConsumerRightsManagerTest extends TestCase
{
    private ConsumerRightsManager $manager;
    private UserDataService $userDataService;
    private VehicleManager $vehicleManager;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->userDataService = $this->createMock(UserDataService::class);
        $this->vehicleManager = $this->createMock(VehicleManager::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->manager = new ConsumerRightsManager(
            $this->userDataService,
            $this->vehicleManager
        );
        $this->manager->setLogger($this->logger);
    }

    public function testHandleConsumerRightsMessageSuccess(): void
    {
        $message = new ConsumerRightsEnvelopeBody(
            'UPDATED_VEHICLE_CONTRACTS',
            'VR3ATTENTKJ622762',
            '6736320116974042a60293d1694a6029',
            '2025-05-13T09:02:04.941Z',
            'test-gigya-uid',
            'MEDIUM'
        );

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Processing consumer rights message', $this->anything());

        $this->vehicleManager->expects($this->once())
            ->method('handleCustomerRightsUpdate')
            ->with(
                'UPDATED_VEHICLE_CONTRACTS',
                'VR3ATTENTKJ622762',
                '6736320116974042a60293d1694a6029',
                '2025-05-13T09:02:04.941Z',
                'test-gigya-uid',
                'MEDIUM'
            )
            ->willReturn(true);

        $result = $this->manager->handleConsumerRightsMessage($message);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals('Consumer rights updated successfully', $result->getData());
    }

    public function testHandleConsumerRightsMessageFailure(): void
    {
        $message = new ConsumerRightsEnvelopeBody(
            'UPDATED_VEHICLE_CONTRACTS',
            'VR3ATTENTKJ622762',
            '6736320116974042a60293d1694a6029',
            '2025-05-13T09:02:04.941Z',
            'test-gigya-uid',
            'MEDIUM'
        );

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Processing consumer rights message', $this->anything());

        $this->vehicleManager->expects($this->once())
            ->method('handleCustomerRightsUpdate')
            ->willReturn(false);

        $result = $this->manager->handleConsumerRightsMessage($message);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $result->getCode());
        $this->assertEquals('Failed to update consumer rights', $result->getMessage());
    }

    public function testHandleConsumerRightsMessageException(): void
    {
        $message = new ConsumerRightsEnvelopeBody(
            'UPDATED_VEHICLE_CONTRACTS',
            'VR3ATTENTKJ622762',
            '6736320116974042a60293d1694a6029',
            '2025-05-13T09:02:04.941Z',
            'test-gigya-uid',
            'MEDIUM'
        );

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Processing consumer rights message', $this->anything());

        $this->vehicleManager->expects($this->once())
            ->method('handleCustomerRightsUpdate')
            ->willThrowException(new \Exception('Test exception'));

        $this->logger->expects($this->once())
            ->method('error')
            ->with('Error processing consumer rights message', $this->anything());

        $result = $this->manager->handleConsumerRightsMessage($message);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $result->getCode());
        $this->assertEquals('Error processing consumer rights message: Test exception', $result->getMessage());
    }
}