<?php

namespace App\Tests\Message;

use App\Message\ConsumerRightsCustomSerializer;
use App\Message\ConsumerRightsEnvelopeBody;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Serializer\SerializerInterface;

class ConsumerRightsCustomSerializerTest extends TestCase
{
    private ConsumerRightsCustomSerializer $serializer;
    private LoggerInterface $logger;
    private SerializerInterface $symfonySerializer;

    protected function setUp(): void
    {
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->symfonySerializer = $this->createMock(SerializerInterface::class);
        
        $this->serializer = new ConsumerRightsCustomSerializer($this->symfonySerializer);
        $this->serializer->setLogger($this->logger);
    }

    public function testDecodeWithDirectMessage(): void
    {
        $encodedEnvelope = [
            'body' => json_encode([
                'event' => 'UPDATED_VEHICLE_CONTRACTS',
                'vin' => 'VR3ATTENTKJ622762',
                'customer_id' => '6736320116974042a60293d1694a6029',
                'timestamp' => '2025-05-13T09:02:04.941Z',
                'gigya_uid' => 'test-gigya-uid',
                'car_association_level' => 'MEDIUM'
            ]),
            'headers' => [
                'message_id' => 'test-message-id'
            ]
        ];

        $envelope = $this->serializer->decode($encodedEnvelope);

        $this->assertInstanceOf(Envelope::class, $envelope);
        $message = $envelope->getMessage();
        $this->assertInstanceOf(ConsumerRightsEnvelopeBody::class, $message);
        $this->assertEquals('UPDATED_VEHICLE_CONTRACTS', $message->getEvent());
        $this->assertEquals('VR3ATTENTKJ622762', $message->getVin());
        $this->assertEquals('6736320116974042a60293d1694a6029', $message->getCustomerId());
        $this->assertEquals('2025-05-13T09:02:04.941Z', $message->getTimestamp());
        $this->assertEquals('test-gigya-uid', $message->getGigyaUid());
        $this->assertEquals('MEDIUM', $message->getCarAssociationLevel());
    }

    public function testDecodeWithSQSMessageWrapper(): void
    {
        $encodedEnvelope = [
            'body' => json_encode([
                'Message' => json_encode([
                    'event' => 'UPDATED_VEHICLE_CONTRACTS',
                    'vin' => 'VR3ATTENTKJ622762',
                    'customer_id' => '6736320116974042a60293d1694a6029',
                    'timestamp' => '2025-05-13T09:02:04.941Z',
                    'gigya_uid' => 'test-gigya-uid',
                    'car_association_level' => 'MEDIUM'
                ])
            ]),
            'headers' => [
                'message_id' => 'test-message-id'
            ]
        ];

        $envelope = $this->serializer->decode($encodedEnvelope);

        $this->assertInstanceOf(Envelope::class, $envelope);
        $message = $envelope->getMessage();
        $this->assertInstanceOf(ConsumerRightsEnvelopeBody::class, $message);
        $this->assertEquals('UPDATED_VEHICLE_CONTRACTS', $message->getEvent());
        $this->assertEquals('VR3ATTENTKJ622762', $message->getVin());
    }

    public function testDecodeWithSQSRecordsWrapper(): void
    {
        $encodedEnvelope = [
            'body' => json_encode([
                'Records' => [
                    [
                        'body' => json_encode([
                            'event' => 'UPDATED_VEHICLE_CONTRACTS',
                            'vin' => 'VR3ATTENTKJ622762',
                            'customer_id' => '6736320116974042a60293d1694a6029',
                            'timestamp' => '2025-05-13T09:02:04.941Z',
                            'gigya_uid' => 'test-gigya-uid',
                            'car_association_level' => 'MEDIUM'
                        ])
                    ]
                ]
            ]),
            'headers' => [
                'message_id' => 'test-message-id'
            ]
        ];

        $envelope = $this->serializer->decode($encodedEnvelope);

        $this->assertInstanceOf(Envelope::class, $envelope);
        $message = $envelope->getMessage();
        $this->assertInstanceOf(ConsumerRightsEnvelopeBody::class, $message);
        $this->assertEquals('UPDATED_VEHICLE_CONTRACTS', $message->getEvent());
        $this->assertEquals('VR3ATTENTKJ622762', $message->getVin());
    }

    public function testDecodeWithInvalidJson(): void
    {
        $encodedEnvelope = [
            'body' => '{invalid-json',
            'headers' => [
                'message_id' => 'test-message-id'
            ]
        ];

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Json decoding exception for Consumer Rights message'));

        $envelope = $this->serializer->decode($encodedEnvelope);

        $this->assertInstanceOf(Envelope::class, $envelope);
        $message = $envelope->getMessage();
        $this->assertInstanceOf(ConsumerRightsEnvelopeBody::class, $message);
        $this->assertEquals('', $message->getEvent());
        $this->assertEquals('', $message->getVin());
    }
}