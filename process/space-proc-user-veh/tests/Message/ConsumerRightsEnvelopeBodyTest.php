<?php

namespace App\Tests\Message;

use App\Message\ConsumerRightsEnvelopeBody;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Violation\ConstraintViolationBuilderInterface;

class ConsumerRightsEnvelopeBodyTest extends TestCase
{
    public function testGetters(): void
    {
        $message = new ConsumerRightsEnvelopeBody(
            'UPDATED_VEHICLE_CONTRACTS',
            'VR3ATTENTKJ622762',
            '6736320116974042a60293d1694a6029',
            '2025-05-13T09:02:04.941Z',
            'test-gigya-uid',
            'MEDIUM'
        );

        $this->assertEquals('UPDATED_VEHICLE_CONTRACTS', $message->getEvent());
        $this->assertEquals('VR3ATTENTKJ622762', $message->getVin());
        $this->assertEquals('6736320116974042a60293d1694a6029', $message->getCustomerId());
        $this->assertEquals('2025-05-13T09:02:04.941Z', $message->getTimestamp());
        $this->assertEquals('test-gigya-uid', $message->getGigyaUid());
        $this->assertEquals('MEDIUM', $message->getCarAssociationLevel());
    }

    public function testValidationWithEmptyEvent(): void
    {
        $message = new ConsumerRightsEnvelopeBody(
            '',
            'VR3ATTENTKJ622762',
            '6736320116974042a60293d1694a6029',
            '2025-05-13T09:02:04.941Z',
            'test-gigya-uid',
            'MEDIUM'
        );

        $violationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $violationBuilder->expects($this->once())
            ->method('atPath')
            ->with('event')
            ->willReturnSelf();
        $violationBuilder->expects($this->once())
            ->method('addViolation');

        $context = $this->createMock(ExecutionContextInterface::class);
        $context->expects($this->once())
            ->method('buildViolation')
            ->with('The event should not be empty.')
            ->willReturn($violationBuilder);

        $message->validateConsumerRightsEnvelopeBody($context);
    }

    public function testValidationWithEmptyVin(): void
    {
        $message = new ConsumerRightsEnvelopeBody(
            'UPDATED_VEHICLE_CONTRACTS',
            '',
            '6736320116974042a60293d1694a6029',
            '2025-05-13T09:02:04.941Z',
            'test-gigya-uid',
            'MEDIUM'
        );

        $violationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $violationBuilder->expects($this->once())
            ->method('atPath')
            ->with('vin')
            ->willReturnSelf();
        $violationBuilder->expects($this->once())
            ->method('addViolation');

        $context = $this->createMock(ExecutionContextInterface::class);
        $context->expects($this->once())
            ->method('buildViolation')
            ->with('The vin should not be empty.')
            ->willReturn($violationBuilder);

        $message->validateConsumerRightsEnvelopeBody($context);
    }

    public function testValidationWithEmptyCustomerId(): void
    {
        $message = new ConsumerRightsEnvelopeBody(
            'UPDATED_VEHICLE_CONTRACTS',
            'VR3ATTENTKJ622762',
            '',
            '2025-05-13T09:02:04.941Z',
            'test-gigya-uid',
            'MEDIUM'
        );

        $violationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $violationBuilder->expects($this->once())
            ->method('atPath')
            ->with('customer_id')
            ->willReturnSelf();
        $violationBuilder->expects($this->once())
            ->method('addViolation');

        $context = $this->createMock(ExecutionContextInterface::class);
        $context->expects($this->once())
            ->method('buildViolation')
            ->with('The customer_id should not be empty.')
            ->willReturn($violationBuilder);

        $message->validateConsumerRightsEnvelopeBody($context);
    }
}