<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\SDPRManager;
use App\Service\SDPRService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class SDPRManagerTest extends TestCase
{
    private $mockService;
    private $mockLogger;
    private $manager;

    protected function setUp(): void
    {
        $this->mockService = $this->createMock(SDPRService::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);

        $this->manager = new SDPRManager($this->mockService);

        // Set logger using reflection since it's a trait
        $reflection = new \ReflectionClass($this->manager);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->manager, $this->mockLogger);
    }

    public function testGetUserVehiclesSuccess(): void
    {
        // Test data
        $userId = 123;
        $vehiclesData = [
            ['id' => 1, 'make' => 'Toyota', 'model' => 'Corolla'],
            ['id' => 2, 'make' => 'Honda', 'model' => 'Civic']
        ];

        // Create a mock response using a concrete class that implements ResponseArrayFormat
        $mockResponse = $this->getMockBuilder(WSResponse::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockResponse->method('getCode')
            ->willReturn(Response::HTTP_OK);

        $mockResponse->method('getData')
            ->willReturn($vehiclesData);

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with('SDPRManager::getUserVehicles');

        $this->mockService->expects($this->once())
            ->method('getUserVehicles')
            ->with($userId)
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->manager->getUserVehicles($userId);

        // Assert results
        $this->assertInstanceOf(SuccessResponse::class, $result);

        // Use toArray() to verify the content
        $resultArray = $result->toArray();
        $this->assertEquals($vehiclesData, $resultArray['content']['success']);
        $this->assertEquals(Response::HTTP_OK, $resultArray['code']);
    }

    public function testGetUserVehiclesUnauthorized(): void
    {
        // Test data
        $userId = 456;

        // Create a mock response
        $mockResponse = $this->getMockBuilder(WSResponse::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockResponse->method('getCode')
            ->willReturn(Response::HTTP_UNAUTHORIZED);

        $mockResponse->method('getData')
            ->willReturn('Unauthorized access');

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with('SDPRManager::getUserVehicles');

        $this->mockService->expects($this->once())
            ->method('getUserVehicles')
            ->with($userId)
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->manager->getUserVehicles($userId);

        // Assert results
        $this->assertInstanceOf(ErrorResponse::class, $result);

        // Use toArray() to verify the content
        $resultArray = $result->toArray();
        $this->assertEquals('Unauthorized', $resultArray['content']['error']['message']);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $resultArray['code']);
    }

    public function testGetUserVehiclesOtherError(): void
    {
        // Test data
        $userId = 789;
        $errorMessage = 'Resource not found';
        $errorCode = Response::HTTP_NOT_FOUND;

        // Create a mock response
        $mockResponse = $this->getMockBuilder(WSResponse::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockResponse->method('getCode')
            ->willReturn($errorCode);

        $mockResponse->method('getData')
            ->willReturn($errorMessage);

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with('SDPRManager::getUserVehicles');

        $this->mockService->expects($this->once())
            ->method('getUserVehicles')
            ->with($userId)
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->manager->getUserVehicles($userId);

        // Assert results
        $this->assertInstanceOf(ErrorResponse::class, $result);

        // Use toArray() to verify the content
        $resultArray = $result->toArray();
        $this->assertEquals($errorMessage, $resultArray['content']['error']['message']);
        $this->assertEquals($errorCode, $resultArray['code']);
    }

    public function testGetUserVehiclesException(): void
    {
        // Test data
        $userId = 999;
        $exceptionMessage = 'Connection timeout';
        $exceptionCode = 408;

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with('SDPRManager::getUserVehicles');

        $this->mockLogger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('SDPRManager::getSDPR User Vehicles: Cached Exception ' . $exceptionMessage));

        $this->mockService->expects($this->once())
            ->method('getUserVehicles')
            ->with($userId)
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));

        // Execute the method
        $result = $this->manager->getUserVehicles($userId);

        // Assert results
        $this->assertInstanceOf(ErrorResponse::class, $result);

        // Use toArray() to verify the content
        $resultArray = $result->toArray();
        $this->assertEquals($exceptionMessage, $resultArray['content']['error']['message']);
        $this->assertEquals($exceptionCode, $resultArray['code']);
    }

    public function testGetUserVehiclesExceptionWithZeroCode(): void
    {
        // Test data
        $userId = 111;
        $exceptionMessage = 'Unknown error';
        $exceptionCode = 0;

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with('SDPRManager::getUserVehicles');

        $this->mockLogger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('SDPRManager::getSDPR User Vehicles: Cached Exception ' . $exceptionMessage));

        $this->mockService->expects($this->once())
            ->method('getUserVehicles')
            ->with($userId)
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));

        // Execute the method
        $result = $this->manager->getUserVehicles($userId);

        // Assert results
        $this->assertInstanceOf(ErrorResponse::class, $result);

        // Use toArray() to verify the content
        $resultArray = $result->toArray();
        $this->assertEquals($exceptionMessage, $resultArray['content']['error']['message']);
        // For zero code, it should default to HTTP_BAD_REQUEST (400)
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $resultArray['code']);
    }

    public function testGetNaftaUserVehiclesSuccess(): void
    {
        // Test data
        $userId = 123;
        $vehiclesData = [
            ['id' => 1, 'make' => 'Toyota', 'model' => 'Corolla'],
            ['id' => 2, 'make' => 'Honda', 'model' => 'Civic']
        ];

        // Create a mock response using a concrete class that implements ResponseArrayFormat
        $mockResponse = $this->getMockBuilder(WSResponse::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockResponse->method('getCode')
            ->willReturn(Response::HTTP_OK);

        $mockResponse->method('getData')
            ->willReturn($vehiclesData);

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with('SDPRManager::getUserVehicles');

        $this->mockService->expects($this->once())
            ->method('getNaftaUserVehicles')
            ->with($userId)
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->manager->getNaftaUserVehicles($userId);

        // Assert results
        $this->assertInstanceOf(SuccessResponse::class, $result);

        // Use toArray() to verify the content
        $resultArray = $result->toArray();
        $this->assertEquals($vehiclesData, $resultArray['content']['success']);
        $this->assertEquals(Response::HTTP_OK, $resultArray['code']);
    }

    public function testGetNaftaUserVehiclesUnauthorized(): void
    {
        // Test data
        $userId = 456;

        // Create a mock response
        $mockResponse = $this->getMockBuilder(WSResponse::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockResponse->method('getCode')
            ->willReturn(Response::HTTP_UNAUTHORIZED);

        $mockResponse->method('getData')
            ->willReturn('Unauthorized access');

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with('SDPRManager::getUserVehicles');

        $this->mockService->expects($this->once())
            ->method('getNaftaUserVehicles')
            ->with($userId)
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->manager->getNaftaUserVehicles($userId);

        // Assert results
        $this->assertInstanceOf(ErrorResponse::class, $result);

        // Use toArray() to verify the content
        $resultArray = $result->toArray();
        $this->assertEquals('Unauthorized', $resultArray['content']['error']['message']);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $resultArray['code']);
    }

    public function testGetNaftaUserVehiclesException(): void
    {
        // Test data
        $userId = 999;
        $exceptionMessage = 'Connection timeout';
        $exceptionCode = 408;

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with('SDPRManager::getUserVehicles');

        $this->mockLogger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('SDPRManager::getSDPR User Vehicles: Cached Exception ' . $exceptionMessage));

        $this->mockService->expects($this->once())
            ->method('getNaftaUserVehicles')
            ->with($userId)
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));

        // Execute the method
        $result = $this->manager->getNaftaUserVehicles($userId);

        // Assert results
        $this->assertInstanceOf(ErrorResponse::class, $result);

        // Use toArray() to verify the content
        $resultArray = $result->toArray();
        $this->assertEquals($exceptionMessage, $resultArray['content']['error']['message']);
        $this->assertEquals($exceptionCode, $resultArray['code']);
    }
}
