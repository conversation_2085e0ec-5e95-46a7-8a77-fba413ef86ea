<?php

namespace App\Tests\Controller;

use App\Controller\UserController;
use App\Helper\SuccessResponse;
use App\Manager\SDPRManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class UserControllerTest extends KernelTestCase
{
    private $validator;
    private $sdprManager;
    private $controller;
    private $container;

    protected function setUp(): void
    {
        self::bootKernel();
        $this->container = static::getContainer();

        // Set a request in the RequestStack manually
        $requestStack = $this->container->get(RequestStack::class);
        $fakeRequest = Request::create('v1/user/vehicles', 'GET');
        $requestStack->push($fakeRequest);

        $this->validator = $this->container->get(ValidatorInterface::class);

        // Create a mock for SDPRManager
        $this->sdprManager = $this->getMockBuilder(SDPRManager::class)
            ->disableOriginalConstructor()
            ->getMock();

        // Create and set up the controller
        $this->controller = new UserController();
        $this->controller->setContainer($this->container);
    }

    public function testGetVehiclesWithMissingUserId(): void
    {
        // Create a request without userId header
        $request = Request::create('v1/user/vehicles', 'GET');

        // Execute the controller method
        $response = $this->controller->getVehicles($request, $this->sdprManager, $this->validator);

        // Assert response
        $this->assertSame(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetVehiclesWithEmptyUserId(): void
    {
        // Create a request with empty userId header
        $request = Request::create('/v1/user/vehicles', 'GET');
        $request->headers->set('userId', '');

        // Execute the controller method
        $response = $this->controller->getVehicles($request, $this->sdprManager, $this->validator);

        // Assert response
        $this->assertSame(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetVehiclesWithValidUserId(): void
    {
        // Create a request with valid userId header
        $request = Request::create('/v1/user/vehicles', 'GET');
        $request->headers->set('userId', 'valid_user_123');

        // Create a mock success response
        $mockSuccessResponse = new SuccessResponse([
            'vehicles' => [
                ['id' => 'v1', 'make' => 'Toyota', 'model' => 'Corolla'],
                ['id' => 'v2', 'make' => 'Honda', 'model' => 'Civic']
            ]
        ]);

        // Configure the mock to return the success response
        $this->sdprManager->expects($this->once())
            ->method('getUserVehicles')
            ->with('valid_user_123')
            ->willReturn($mockSuccessResponse);

        // Execute the controller method
        $response = $this->controller->getVehicles($request, $this->sdprManager, $this->validator);

        // Assert response
        $this->assertSame(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertArrayHasKey('vehicles', $responseData['success']);
    }

    public function testGetNaftaVehiclesWithMissingUserId(): void
    {
        // Create a request without userId header
        $request = Request::create('v1/nafta/user/vehicles', 'GET');

        // Execute the controller method
        $response = $this->controller->getNaftaVehicles($request, $this->sdprManager, $this->validator);

        // Assert response
        $this->assertSame(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetNaftaVehiclesWithEmptyUserId(): void
    {
        // Create a request with empty userId header
        $request = Request::create('/v1/nafta/user/vehicles', 'GET');
        $request->headers->set('userId', '');

        // Execute the controller method
        $response = $this->controller->getNaftaVehicles($request, $this->sdprManager, $this->validator);

        // Assert response
        $this->assertSame(422, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
    }

    public function testGetNaftaVehiclesWithValidUserId(): void
    {
        // Create a request with valid userId header
        $request = Request::create('/v1/nafta/user/vehicles', 'GET');
        $request->headers->set('userId', 'valid_nafta_user_456');

        // Create a mock success response
        $mockSuccessResponse = new SuccessResponse([
            'vehicles' => [
                ['id' => 'nv1', 'make' => 'Ford', 'model' => 'F-150'],
                ['id' => 'nv2', 'make' => 'Chevrolet', 'model' => 'Silverado']
            ]
        ]);

        // Configure the mock to return the success response
        $this->sdprManager->expects($this->once())
            ->method('getNaftaUserVehicles')
            ->with('valid_nafta_user_456')
            ->willReturn($mockSuccessResponse);

        // Execute the controller method
        $response = $this->controller->getNaftaVehicles($request, $this->sdprManager, $this->validator);

        // Assert response
        $this->assertSame(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertArrayHasKey('vehicles', $responseData['success']);
    }

    public function tearDown(): void
    {
        unset($this->sdprManager);
        unset($this->validator);
        unset($this->controller);
        unset($this->container);
    }
}
