<?php

namespace App\Tests\Trait;

use App\Trait\LoggerTrait;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;


class LoggerTraitDummy
{
    use LoggerTrait;

    public function getLogger(): ?LoggerInterface
    {
        return $this->logger;
    }
}

class LoggerTraitTest extends TestCase
{
    public function testSetLogger(): void
    {
        $mockLogger = $this->createMock(LoggerInterface::class);
        $dummy = new LoggerTraitDummy();
        $dummy->setLogger($mockLogger);

        $this->assertSame($mockLogger, $dummy->getLogger());
    }

    public function testLoggerInitialization(): void
    {
        $dummy = new LoggerTraitDummy();
        $dummy->setLogger(new NullLogger());

        $this->assertInstanceOf(LoggerInterface::class, $dummy->getLogger());
    }

    public function testSetLoggerThrowsTypeErrorWhenNull(): void
    {
        $this->expectException(\TypeError::class);

        $dummy = new LoggerTraitDummy();
        $dummy->setLogger(null);
    }
}
