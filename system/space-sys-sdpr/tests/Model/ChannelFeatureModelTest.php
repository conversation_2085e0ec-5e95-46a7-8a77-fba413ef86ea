<?php

namespace App\Tests\Model;

use App\Model\ChannelFeatureModel;
use PHPUnit\Framework\TestCase;

class ChannelFeatureModelTest extends TestCase
{
    private ChannelFeatureModel $model;

    protected function setUp(): void
    {
        $this->model = new ChannelFeatureModel();
    }

    public function testInitialState(): void
    {
        // Test that properties are initially empty/null
        $this->expectException(\Error::class); // Accessing uninitialized property
        $this->model->getFeatureCode();
    }

    public function testSetAndGetFeatureCode(): void
    {
        // Test setting and getting feature code
        $featureCode = 'FEATURE_123';
        
        // Test fluent interface returns self
        $result = $this->model->setFeatureCode($featureCode);
        $this->assertSame($this->model, $result);
        
        // Test getter returns correct value
        $this->assertSame($featureCode, $this->model->getFeatureCode());
    }

    public function testSetAndGetChannels(): void
    {
        // Test setting and getting channels
        $channels = ['web', 'mobile', 'api'];
        
        // Test fluent interface returns self
        $result = $this->model->setChannels($channels);
        $this->assertSame($this->model, $result);
        
        // Test getter returns correct value
        $this->assertSame($channels, $this->model->getChannels());
    }

    public function testFeatureCodeMutability(): void
    {
        // Test that feature code can be changed
        $this->model->setFeatureCode('FEATURE_A');
        $this->assertSame('FEATURE_A', $this->model->getFeatureCode());
        
        $this->model->setFeatureCode('FEATURE_B');
        $this->assertSame('FEATURE_B', $this->model->getFeatureCode());
    }

    public function testChannelsMutability(): void
    {
        // Test that channels can be changed
        $channels1 = ['web', 'mobile'];
        $this->model->setChannels($channels1);
        $this->assertSame($channels1, $this->model->getChannels());
        
        $channels2 = ['api', 'desktop'];
        $this->model->setChannels($channels2);
        $this->assertSame($channels2, $this->model->getChannels());
    }

    public function testEmptyChannelsArray(): void
    {
        // Test setting empty channels array
        $this->model->setChannels([]);
        $this->assertSame([], $this->model->getChannels());
    }

    public function testComplexChannelsArray(): void
    {
        // Test with complex nested array structure
        $channels = [
            'web' => ['desktop', 'mobile'],
            'api' => ['v1', 'v2', 'v3'],
            'other' => [
                'internal' => true,
                'external' => false
            ]
        ];
        
        $this->model->setChannels($channels);
        $this->assertSame($channels, $this->model->getChannels());
    }

    public function testChainedSetters(): void
    {
        // Test chaining multiple setters
        $featureCode = 'FEATURE_XYZ';
        $channels = ['channel1', 'channel2'];
        
        $this->model->setFeatureCode($featureCode)
                    ->setChannels($channels);
        
        $this->assertSame($featureCode, $this->model->getFeatureCode());
        $this->assertSame($channels, $this->model->getChannels());
    }

    public function testFeatureCodeWithSpecialCharacters(): void
    {
        // Test feature code with special characters
        $featureCode = 'FEATURE-123_$#@!';
        $this->model->setFeatureCode($featureCode);
        $this->assertSame($featureCode, $this->model->getFeatureCode());
    }

    public function testLongFeatureCode(): void
    {
        // Test with a very long feature code
        $featureCode = str_repeat('A', 1000);
        $this->model->setFeatureCode($featureCode);
        $this->assertSame($featureCode, $this->model->getFeatureCode());
    }

    public function testLargeChannelsArray(): void
    {
        // Test with a large channels array
        $channels = array_fill(0, 1000, 'channel');
        $this->model->setChannels($channels);
        $this->assertSame($channels, $this->model->getChannels());
    }

    protected function tearDown(): void
    {
        unset($this->model);
    }
}