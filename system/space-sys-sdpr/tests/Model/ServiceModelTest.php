<?php

namespace App\Tests\Model;

use App\Model\ServiceModel;
use PHPUnit\Framework\TestCase;

class ServiceModelTest extends TestCase
{
    private ServiceModel $model;

    protected function setUp(): void
    {
        $this->model = new ServiceModel();
    }

    public function testInitialState(): void
    {
        // Test that properties are initially uninitialized
        $this->expectException(\Error::class);
        $this->model->getService();
    }

    public function testSetAndGetService(): void
    {
        // Test setting and getting service
        $service = 'premium_service';
        
        // Test fluent interface returns self
        $result = $this->model->setService($service);
        $this->assertSame($this->model, $result);
        
        // Test getter returns correct value
        $this->assertSame($service, $this->model->getService());
    }

    public function testSetAndGetVehicleCapable(): void
    {
        // Test setting and getting vehicleCapable
        $vehicleCapable = true;
        
        // Test fluent interface returns self
        $result = $this->model->setVehicleCapable($vehicleCapable);
        $this->assertSame($this->model, $result);
        
        // Test getter returns correct value
        $this->assertSame($vehicleCapable, $this->model->isVehicleCapable());
    }

    public function testSetAndGetServiceEnabled(): void
    {
        // Test setting and getting serviceEnabled
        $serviceEnabled = true;
        
        // Test fluent interface returns self
        $result = $this->model->setServiceEnabled($serviceEnabled);
        $this->assertSame($this->model, $result);
        
        // Test getter returns correct value
        $this->assertSame($serviceEnabled, $this->model->isServiceEnabled());
    }

    public function testServiceMutability(): void
    {
        // Test that service can be changed
        $this->model->setService('service_a');
        $this->assertSame('service_a', $this->model->getService());
        
        $this->model->setService('service_b');
        $this->assertSame('service_b', $this->model->getService());
    }

    public function testVehicleCapableMutability(): void
    {
        // Test that vehicleCapable can be changed
        $this->model->setVehicleCapable(true);
        $this->assertTrue($this->model->isVehicleCapable());
        
        $this->model->setVehicleCapable(false);
        $this->assertFalse($this->model->isVehicleCapable());
    }

    public function testServiceEnabledMutability(): void
    {
        // Test that serviceEnabled can be changed
        $this->model->setServiceEnabled(true);
        $this->assertTrue($this->model->isServiceEnabled());
        
        $this->model->setServiceEnabled(false);
        $this->assertFalse($this->model->isServiceEnabled());
    }

    public function testChainedSetters(): void
    {
        // Test chaining multiple setters
        $service = 'premium_service';
        $vehicleCapable = true;
        $serviceEnabled = false;
        
        $this->model->setService($service)
                    ->setVehicleCapable($vehicleCapable)
                    ->setServiceEnabled($serviceEnabled);
        
        $this->assertSame($service, $this->model->getService());
        $this->assertSame($vehicleCapable, $this->model->isVehicleCapable());
        $this->assertSame($serviceEnabled, $this->model->isServiceEnabled());
    }

    public function testServiceWithSpecialCharacters(): void
    {
        // Test service with special characters
        $service = 'service-123_$#@!';
        $this->model->setService($service);
        $this->assertSame($service, $this->model->getService());
    }

    public function testLongServiceName(): void
    {
        // Test with a very long service name
        $service = str_repeat('A', 1000);
        $this->model->setService($service);
        $this->assertSame($service, $this->model->getService());
    }

    public function testEmptyServiceName(): void
    {
        // Test with empty service name
        $service = '';
        $this->model->setService($service);
        $this->assertSame($service, $this->model->getService());
    }

    protected function tearDown(): void
    {
        unset($this->model);
    }
}