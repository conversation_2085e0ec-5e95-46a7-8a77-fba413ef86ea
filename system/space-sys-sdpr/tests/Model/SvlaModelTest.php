<?php

namespace App\Tests\Model;

use App\Model\SvlaModel;
use PHPUnit\Framework\TestCase;

class SvlaModelTest extends TestCase
{
    private SvlaModel $model;

    protected function setUp(): void
    {
        $this->model = new SvlaModel();
    }

    public function testInitialState(): void
    {
        // Test that properties are initially uninitialized
        $this->expectException(\Error::class);
        $this->model->getStatus();
    }

    public function testSetAndGetStatus(): void
    {
        // Test setting and getting status
        $status = 'active';
        
        // Test fluent interface returns self
        $result = $this->model->setStatus($status);
        $this->assertSame($this->model, $result);
        
        // Test getter returns correct value
        $this->assertSame($status, $this->model->getStatus());
    }

    public function testSetAndGetTimestamp(): void
    {
        // Test setting and getting timestamp
        $timestamp = time();
        
        // Test fluent interface returns self
        $result = $this->model->setTimestamp($timestamp);
        $this->assertSame($this->model, $result);
        
        // Test getter returns correct value
        $this->assertSame($timestamp, $this->model->getTimestamp());
    }

    public function testStatusMutability(): void
    {
        // Test that status can be changed
        $this->model->setStatus('pending');
        $this->assertSame('pending', $this->model->getStatus());
        
        $this->model->setStatus('completed');
        $this->assertSame('completed', $this->model->getStatus());
    }

    public function testTimestampMutability(): void
    {
        // Test that timestamp can be changed
        $timestamp1 = 1609459200; // 2021-01-01 00:00:00
        $this->model->setTimestamp($timestamp1);
        $this->assertSame($timestamp1, $this->model->getTimestamp());
        
        $timestamp2 = 1640995200; // 2022-01-01 00:00:00
        $this->model->setTimestamp($timestamp2);
        $this->assertSame($timestamp2, $this->model->getTimestamp());
    }

    public function testChainedSetters(): void
    {
        // Test chaining multiple setters
        $status = 'active';
        $timestamp = time();
        
        $this->model->setStatus($status)
                    ->setTimestamp($timestamp);
        
        $this->assertSame($status, $this->model->getStatus());
        $this->assertSame($timestamp, $this->model->getTimestamp());
    }

    public function testStatusWithSpecialCharacters(): void
    {
        // Test status with special characters
        $status = 'status-123_$#@!';
        $this->model->setStatus($status);
        $this->assertSame($status, $this->model->getStatus());
    }

    public function testLongStatus(): void
    {
        // Test with a very long status
        $status = str_repeat('A', 1000);
        $this->model->setStatus($status);
        $this->assertSame($status, $this->model->getStatus());
    }

    public function testEmptyStatus(): void
    {
        // Test with empty status
        $status = '';
        $this->model->setStatus($status);
        $this->assertSame($status, $this->model->getStatus());
    }

    public function testMinimumTimestamp(): void
    {
        // Test with minimum possible timestamp (0 = January 1, 1970 00:00:00 GMT)
        $timestamp = 0;
        $this->model->setTimestamp($timestamp);
        $this->assertSame($timestamp, $this->model->getTimestamp());
    }

    public function testMaximumTimestamp(): void
    {
        // Test with maximum possible timestamp for 32-bit systems (2147483647 = January 19, 2038 03:14:07 GMT)
        $timestamp = PHP_INT_MAX;
        $this->model->setTimestamp($timestamp);
        $this->assertSame($timestamp, $this->model->getTimestamp());
    }

    public function testNegativeTimestamp(): void
    {
        // Test with negative timestamp (represents dates before January 1, 1970)
        $timestamp = -1000000;
        $this->model->setTimestamp($timestamp);
        $this->assertSame($timestamp, $this->model->getTimestamp());
    }

    public function testCurrentTimestamp(): void
    {
        // Test with current timestamp
        $timestamp = time();
        $this->model->setTimestamp($timestamp);
        $this->assertSame($timestamp, $this->model->getTimestamp());
    }

    protected function tearDown(): void
    {
        unset($this->model);
    }
}