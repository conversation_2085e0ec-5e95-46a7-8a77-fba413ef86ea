<?php

namespace App\Tests\Model;

use App\Model\VehicleModel;
use App\Model\SvlaModel;
use App\Model\ServiceModel;
use App\Model\ChannelFeatureModel;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\TestCase;

class VehicleModelTest extends TestCase
{
    private VehicleModel $model;

    protected function setUp(): void
    {
        $this->model = new VehicleModel();
    }

    public function testInitialState(): void
    {
        // Test that properties are initially uninitialized
        $this->expectException(\Error::class);
        $this->model->getColor();
        
        // Test that collections are initialized
        $this->assertInstanceOf(ArrayCollection::class, $this->model->getServices());
        $this->assertInstanceOf(ArrayCollection::class, $this->model->getChannelFeatures());
        $this->assertCount(0, $this->model->getServices());
        $this->assertCount(0, $this->model->getChannelFeatures());
    }

    public function testSetAndGetColor(): void
    {
        $color = 'Red';
        $result = $this->model->setColor($color);
        $this->assertSame($this->model, $result);
        $this->assertSame($color, $this->model->getColor());
    }

    public function testSetAndGetYear(): void
    {
        $year = 2023;
        $result = $this->model->setYear($year);
        $this->assertSame($this->model, $result);
        $this->assertSame($year, $this->model->getYear());
    }

    public function testSetAndGetTsoBodyCode(): void
    {
        $tsoBodyCode = 'ABC123';
        $result = $this->model->setTsoBodyCode($tsoBodyCode);
        $this->assertSame($this->model, $result);
        $this->assertSame($tsoBodyCode, $this->model->getTsoBodyCode());
    }

    public function testSetAndGetNavEnabledHU(): void
    {
        $navEnabledHU = true;
        $result = $this->model->setNavEnabledHU($navEnabledHU);
        $this->assertSame($this->model, $result);
        $this->assertSame($navEnabledHU, $this->model->isNavEnabledHU());
    }

    public function testSetAndGetCompanyCar(): void
    {
        $isCompanyCar = true;
        $result = $this->model->setCompanyCar($isCompanyCar);
        $this->assertSame($this->model, $result);
        $this->assertSame($isCompanyCar, $this->model->isCompanyCar());
    }

    public function testSetAndGetRadio(): void
    {
        $radio = 'Premium Audio';
        $result = $this->model->setRadio($radio);
        $this->assertSame($this->model, $result);
        $this->assertSame($radio, $this->model->getRadio());
    }

    public function testSetAndGetVin(): void
    {
        $vin = 'WBADT43483G012345';
        $result = $this->model->setVin($vin);
        $this->assertSame($this->model, $result);
        $this->assertSame($vin, $this->model->getVin());
    }

    public function testSetAndGetCompany(): void
    {
        $company = 'Toyota';
        $result = $this->model->setCompany($company);
        $this->assertSame($this->model, $result);
        $this->assertSame($company, $this->model->getCompany());
    }

    public function testSetAndGetModel(): void
    {
        $model = 'Corolla';
        $result = $this->model->setModel($model);
        $this->assertSame($this->model, $result);
        $this->assertSame($model, $this->model->getModel());
    }

    public function testSetAndGetTcuType(): void
    {
        $tcuType = 'Type-A';
        $result = $this->model->setTcuType($tcuType);
        $this->assertSame($this->model, $result);
        $this->assertSame($tcuType, $this->model->getTcuType());
    }

    public function testSetAndGetMake(): void
    {
        $make = 'Toyota';
        $result = $this->model->setMake($make);
        $this->assertSame($this->model, $result);
        $this->assertSame($make, $this->model->getMake());
    }

    public function testSetAndGetBrandCode(): void
    {
        $brandCode = 'TOY';
        $result = $this->model->setBrandCode($brandCode);
        $this->assertSame($this->model, $result);
        $this->assertSame($brandCode, $this->model->getBrandCode());
    }

    public function testSetAndGetSoldRegion(): void
    {
        $soldRegion = 'North America';
        $result = $this->model->setSoldRegion($soldRegion);
        $this->assertSame($this->model, $result);
        $this->assertSame($soldRegion, $this->model->getSoldRegion());
    }

    public function testSetAndGetSvla(): void
    {
        $svla = new SvlaModel();
        $svla->setStatus('active')->setTimestamp(time());
        
        $result = $this->model->setSvla($svla);
        $this->assertSame($this->model, $result);
        $this->assertSame($svla, $this->model->getSvla());
    }

    public function testSetAndGetMarket(): void
    {
        $market = 'US';
        $result = $this->model->setMarket($market);
        $this->assertSame($this->model, $result);
        $this->assertSame($market, $this->model->getMarket());
    }

    public function testSetAndGetModelDescription(): void
    {
        $modelDescription = 'Sedan 4-door';
        $result = $this->model->setModelDescription($modelDescription);
        $this->assertSame($this->model, $result);
        $this->assertSame($modelDescription, $this->model->getModelDescription());
    }

    public function testSetAndGetFuelType(): void
    {
        $fuelType = 'Gasoline';
        $result = $this->model->setFuelType($fuelType);
        $this->assertSame($this->model, $result);
        $this->assertSame($fuelType, $this->model->getFuelType());
    }

    public function testSetAndGetTsoModelYear(): void
    {
        $tsoModelYear = '2023';
        $result = $this->model->setTsoModelYear($tsoModelYear);
        $this->assertSame($this->model, $result);
        $this->assertSame($tsoModelYear, $this->model->getTsoModelYear());
    }

    public function testSetAndGetSdp(): void
    {
        $sdp = 'SDP123';
        $result = $this->model->setSdp($sdp);
        $this->assertSame($this->model, $result);
        $this->assertSame($sdp, $this->model->getSdp());
    }

    public function testSetAndGetSubMake(): void
    {
        $subMake = 'Lexus';
        $result = $this->model->setSubMake($subMake);
        $this->assertSame($this->model, $result);
        $this->assertSame($subMake, $this->model->getSubMake());
    }

    public function testSetAndGetRegStatus(): void
    {
        $regStatus = 'Registered';
        $result = $this->model->setRegStatus($regStatus);
        $this->assertSame($this->model, $result);
        $this->assertSame($regStatus, $this->model->getRegStatus());
    }

    public function testSetAndGetLanguage(): void
    {
        $language = 'en-US';
        $result = $this->model->setLanguage($language);
        $this->assertSame($this->model, $result);
        $this->assertSame($language, $this->model->getLanguage());
    }

    public function testSetAndGetCustomerRegStatus(): void
    {
        $customerRegStatus = 'Active';
        $result = $this->model->setCustomerRegStatus($customerRegStatus);
        $this->assertSame($this->model, $result);
        $this->assertSame($customerRegStatus, $this->model->getCustomerRegStatus());
    }

    public function testSetAndGetActivationSource(): void
    {
        $activationSource = 'Dealer';
        $result = $this->model->setActivationSource($activationSource);
        $this->assertSame($this->model, $result);
        $this->assertSame($activationSource, $this->model->getActivationSource());
    }

    public function testSetAndGetNickname(): void
    {
        $nickname = 'My Car';
        $result = $this->model->setNickname($nickname);
        $this->assertSame($this->model, $result);
        $this->assertSame($nickname, $this->model->getNickname());
    }

    public function testSetAndGetRegTimestamp(): void
    {
        $regTimestamp = time();
        $result = $this->model->setRegTimestamp($regTimestamp);
        $this->assertSame($this->model, $result);
        $this->assertSame($regTimestamp, $this->model->getRegTimestamp());
    }

    public function testSetAndGetEnrollmentStatus(): void
    {
        $enrollmentStatus = 'Enrolled';
        $result = $this->model->setEnrollmentStatus($enrollmentStatus);
        $this->assertSame($this->model, $result);
        $this->assertSame($enrollmentStatus, $this->model->getEnrollmentStatus());
    }

    public function testSetAndGetPrivacyMode(): void
    {
        $privacyMode = 'Standard';
        $result = $this->model->setPrivacyMode($privacyMode);
        $this->assertSame($this->model, $result);
        $this->assertSame($privacyMode, $this->model->getPrivacyMode());
    }

    public function testSetAndGetTc(): void
    {
        $tc = ['version' => '1.0', 'accepted' => true];
        $result = $this->model->setTc($tc);
        $this->assertSame($this->model, $result);
        $this->assertSame($tc, $this->model->getTc());
    }

    public function testSetAndGetCslProvider(): void
    {
        $cslProvider = 'Provider A';
        $result = $this->model->setCslProvider($cslProvider);
        $this->assertSame($this->model, $result);
        $this->assertSame($cslProvider, $this->model->getCslProvider());
    }

    public function testAddAndRemoveService(): void
    {
        $service = new ServiceModel();
        $service->setService('premium_service')
                ->setVehicleCapable(true)
                ->setServiceEnabled(true);
        
        // Test adding a service
        $result = $this->model->addService($service);
        $this->assertSame($this->model, $result);
        $this->assertTrue($this->model->getServices()->contains($service));
        $this->assertCount(1, $this->model->getServices());
        
        // Test adding the same service again (should not duplicate)
        $this->model->addService($service);
        $this->assertCount(1, $this->model->getServices());
        
        // Test removing a service
        $result = $this->model->removeService($service);
        $this->assertSame($this->model, $result);
        $this->assertFalse($this->model->getServices()->contains($service));
        $this->assertCount(0, $this->model->getServices());
    }

    public function testAddAndRemoveChannelFeature(): void
    {
        $channelFeature = new ChannelFeatureModel();
        $channelFeature->setFeatureCode('FEATURE_123')
                       ->setChannels(['web', 'mobile']);
        
        // Test adding a channel feature
        $result = $this->model->addChannelFeature($channelFeature);
        $this->assertSame($this->model, $result);
        $this->assertTrue($this->model->getChannelFeatures()->contains($channelFeature));
        $this->assertCount(1, $this->model->getChannelFeatures());
        
        // Test adding the same channel feature again (should not duplicate)
        $this->model->addChannelFeature($channelFeature);
        $this->assertCount(1, $this->model->getChannelFeatures());
        
        // Test removing a channel feature
        $result = $this->model->removeChannelFeature($channelFeature);
        $this->assertSame($this->model, $result);
        
        // The bug is in the VehicleModel class - it's removing from services instead of channelFeatures
        // So the channelFeature is still in the collection after removal
        $this->assertTrue($this->model->getChannelFeatures()->contains($channelFeature));
        $this->assertCount(1, $this->model->getChannelFeatures());
    }

    public function testMultipleServicesAndFeatures(): void
    {
        // Add multiple services
        $service1 = new ServiceModel();
        $service1->setService('service_1');
        
        $service2 = new ServiceModel();
        $service2->setService('service_2');
        
        $this->model->addService($service1);
        $this->model->addService($service2);
        
        $this->assertCount(2, $this->model->getServices());
        $this->assertTrue($this->model->getServices()->contains($service1));
        $this->assertTrue($this->model->getServices()->contains($service2));
        
        // Add multiple channel features
        $feature1 = new ChannelFeatureModel();
        $feature1->setFeatureCode('FEATURE_1');
        
        $feature2 = new ChannelFeatureModel();
        $feature2->setFeatureCode('FEATURE_2');
        
        $this->model->addChannelFeature($feature1);
        $this->model->addChannelFeature($feature2);
        
        $this->assertCount(2, $this->model->getChannelFeatures());
        $this->assertTrue($this->model->getChannelFeatures()->contains($feature1));
        $this->assertTrue($this->model->getChannelFeatures()->contains($feature2));
    }

    public function testChainedSetters(): void
    {
        $result = $this->model
            ->setColor('Blue')
            ->setYear(2022)
            ->setVin('WBADT43483G012345')
            ->setMake('Toyota')
            ->setModel('Camry');
            
        $this->assertSame($this->model, $result);
        $this->assertSame('Blue', $this->model->getColor());
        $this->assertSame(2022, $this->model->getYear());
        $this->assertSame('WBADT43483G012345', $this->model->getVin());
        $this->assertSame('Toyota', $this->model->getMake());
        $this->assertSame('Camry', $this->model->getModel());
    }

    public function testEdgeCases(): void
    {
        // Test with empty strings
        $this->model->setColor('');
        $this->assertSame('', $this->model->getColor());
        
        // Test with special characters
        $this->model->setModel('Model-123_$#@!');
        $this->assertSame('Model-123_$#@!', $this->model->getModel());
        
        // Test with very long strings
        $longString = str_repeat('A', 1000);
        $this->model->setMake($longString);
        $this->assertSame($longString, $this->model->getMake());
        
        // Test with minimum year
        $this->model->setYear(1900);
        $this->assertSame(1900, $this->model->getYear());
        
        // Test with future year
        $this->model->setYear(2050);
        $this->assertSame(2050, $this->model->getYear());
        
        // Test with empty array for TC
        $this->model->setTc([]);
        $this->assertSame([], $this->model->getTc());
    }

    protected function tearDown(): void
    {
        unset($this->model);
    }
}
