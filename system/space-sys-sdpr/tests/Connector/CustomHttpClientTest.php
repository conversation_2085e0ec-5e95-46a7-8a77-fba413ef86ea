<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\InternalHttpClientService;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\ResponseInterface;

class CustomHttpClientTest extends TestCase
{
    private $mockLogger;
    private $mockInternalClient;
    private $customHttpClient;
    private $mockResponse;

    protected function setUp(): void
    {
        // Create mock for logger
        $this->mockLogger = $this->createMock(LoggerInterface::class);

        // Create mock for HTTP response
        $this->mockResponse = $this->createMock(ResponseInterface::class);

        // Create the client to test
        /** @var \App\Connector\InternalHttpClientService&\PHPUnit\Framework\MockObject\MockObject */
        $this->mockInternalClient = $this->createMock(InternalHttpClientService::class);
        $this->customHttpClient = new CustomHttpClient($this->mockInternalClient);

        // Set the logger using reflection (since it's using a trait)
        $reflection = new \ReflectionClass($this->customHttpClient);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->customHttpClient, $this->mockLogger);
    }

    protected function tearDown(): void
    {
        echo "Tearing down objects !! \n";
        unset($this->mockLogger);
        unset($this->mockInternalClient);
        unset($this->customHttpClient);
        unset($this->mockResponse);
    }

    public function testSuccessfulRequest(): void
    {
        // Test data
        $method = 'GET';
        $url = 'http://example.com/api';
        $options = ['headers' => ['X-API-Key' => 'test-key']];
        $responseData = ['id' => 123, 'name' => 'Test Data'];

        // Configure mocks
        $this->mockLogger->expects($this->exactly(2))
            ->method('info')
            ->with($this->logicalOr(
                $this->equalTo("REQUEST: {$method} {$url}"),
                $this->equalTo("RESPONSE: {$method} {$url}")
            ), $this->anything());

        $this->mockResponse->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(200);

        $this->mockResponse->expects($this->once())
            ->method('toArray')
            ->with(false)
            ->willReturn($responseData);

        $this->mockInternalClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($this->mockResponse);

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(200, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testNoContentResponse(): void
    {
        // Test data
        $method = 'DELETE';
        $url = 'http://example.com/api';
        $options = [];

        // Configure mocks
        $this->mockLogger->expects($this->exactly(2))
            ->method('info')
            ->with($this->logicalOr(
                $this->equalTo("REQUEST: {$method} {$url}"),
                $this->equalTo("RESPONSE: {$method} {$url}")
            ), $this->anything());

        $this->mockResponse->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_NO_CONTENT);

        $this->mockResponse->expects($this->never())
            ->method('toArray');

        $this->mockInternalClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($this->mockResponse);

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NO_CONTENT, $result->getCode());
        $this->assertEquals([], $result->getData());
    }

    public function testRequestWithException(): void
    {
        // Test data
        $method = 'POST';
        $url = 'http://example.com/api';
        $options = ['json' => ['name' => 'New Data']];
        $exceptionMessage = 'Connection timeout';
        $exceptionCode = 408;

        // Create exception
        $exception = new \Exception($exceptionMessage, $exceptionCode);

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with("REQUEST: {$method} {$url}", $this->anything());

        $this->mockLogger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Cached Exception : CustomHttpClient::request ' . $exceptionMessage), $this->anything());

        $this->mockInternalClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willThrowException($exception);

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }

    public function testRequestWithZeroExceptionCode(): void
    {
        // Test data
        $method = 'GET';
        $url = 'http://example.com/api';
        $options = [];
        $exceptionMessage = 'Invalid response format';
        $exceptionCode = 0; // Some exceptions have code 0

        // Create exception
        $exception = new \Exception($exceptionMessage, $exceptionCode);

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');

        $this->mockLogger->expects($this->once())
            ->method('error');

        $this->mockInternalClient->expects($this->once())
            ->method('request')
            ->willThrowException($exception);

        // Execute the method
        $result = $this->customHttpClient->request($method, $url, $options);

        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }

    public function testRequestWithDifferentHttpMethods(): void
    {
        // Test different HTTP methods
        $methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
        $url = 'http://example.com/api';

        foreach ($methods as $method) {
            // Reset mocks
            $this->setUp();

            // Configure mocks
            $this->mockLogger->expects($this->exactly(2))
                ->method('info')
                ->with($this->logicalOr(
                    $this->equalTo("REQUEST: {$method} {$url}"),
                    $this->equalTo("RESPONSE: {$method} {$url}")
                ), $this->anything());

            $this->mockResponse->expects($this->once())
                ->method('getStatusCode')
                ->willReturn(200);

            $this->mockResponse->expects($this->once())
                ->method('toArray')
                ->with(false)
                ->willReturn(['success' => true]);

            $this->mockInternalClient->expects($this->once())
                ->method('request')
                ->with($method, $url, [])
                ->willReturn($this->mockResponse);

            // Execute the method
            $result = $this->customHttpClient->request($method, $url);

            // Assert results
            $this->assertInstanceOf(WSResponse::class, $result);
            $this->assertEquals(200, $result->getCode());
        }
    }
}
