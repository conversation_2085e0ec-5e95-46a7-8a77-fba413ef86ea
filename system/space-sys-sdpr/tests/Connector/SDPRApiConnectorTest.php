<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\SDPRApiConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class SDPRApiConnectorTest extends TestCase
{
    private $mockClient;
    private $mockLogger;
    private $connector;
    private $apiKey;
    private $url;

    protected function setUp(): void
    {
        echo "Setting up objects for testing !! \n";
        // Create mocks
        $this->mockClient = $this->createMock(CustomHttpClient::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);
        
        // Set test values
        $this->apiKey = 'test-api-key';
        $this->url = 'https://api.example.com';
        
        // Create the connector to test
        /** @var CustomHttpClient&\PHPUnit\Framework\MockObject\MockObject */
        $this->connector = new SDPRApiConnector($this->mockClient, $this->apiKey, $this->url);
        
        // Inject mock logger
        $reflection = new \ReflectionClass($this->connector);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->connector, $this->mockLogger);
    }

    protected function tearDown(): void
    {
        echo "Tearing down objects !! \n";
        unset($this->mockClient);
        unset($this->mockLogger);
        unset($this->connector);
        unset($this->apiKey);
        unset($this->url);
    }

    public function testSuccessfulApiCall(): void
    {
        // Test data
        $method = 'GET';
        $uri = 'users';
        $options = ['query' => ['page' => 1]];
        $expectedUrl = $this->url . '/' . $uri;
        $expectedOptions = [
            'query' => ['page' => 1],
            'headers' => ['x-api-key' => $this->apiKey],
            'verify_peer' => false,
            'verify_host' => false
        ];
        
        // Expected response
        $expectedResponse = new WSResponse(200, ['id' => 123, 'name' => 'Test User']);
        
        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('SdprApiConnector::call ' . $expectedUrl));
            
        $this->mockClient->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $expectedOptions)
            ->willReturn($expectedResponse);
        
        // Execute the method
        $result = $this->connector->call($method, $uri, $options);
        
        // Assert results
        $this->assertSame($expectedResponse, $result);
    }
    
    public function testApiCallWithException(): void
    {
        // Test data
        $method = 'POST';
        $uri = 'orders';
        $options = ['json' => ['product_id' => 456]];
        $exceptionMessage = 'API connection failed';
        $exceptionCode = 503;
        
        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');
            
        $this->mockLogger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Error occured while calling ' . $uri));
            
        $this->mockClient->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
        
        // Execute the method
        $result = $this->connector->call($method, $uri, $options);
        
        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }
    
    public function testApiCallWithZeroExceptionCode(): void
    {
        // Test data
        $method = 'GET';
        $uri = 'products';
        $options = [];
        $exceptionMessage = 'Unknown error';
        $exceptionCode = 0;
        
        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');
            
        $this->mockLogger->expects($this->once())
            ->method('error');
            
        $this->mockClient->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
        
        // Execute the method
        $result = $this->connector->call($method, $uri, $options);
        
        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }
    
    public function testApiCallWithDifferentHttpMethods(): void
    {
        // Test different HTTP methods
        $methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
        $uri = 'resources';
        
        foreach ($methods as $method) {
            // Reset mocks
            $this->setUp();
            
            // Expected response
            $expectedResponse = new WSResponse(200, ['success' => true]);
            
            // Configure mocks
            $this->mockLogger->expects($this->once())
                ->method('info');
                
            $this->mockClient->expects($this->once())
                ->method('request')
                ->with($method, $this->url . '/' . $uri, $this->callback(function($options) {
                    return isset($options['headers']['x-api-key']) && 
                           $options['headers']['x-api-key'] === $this->apiKey &&
                           $options['verify_peer'] === false &&
                           $options['verify_host'] === false;
                }))
                ->willReturn($expectedResponse);
            
            // Execute the method
            $result = $this->connector->call($method, $uri);
            
            // Assert results
            $this->assertSame($expectedResponse, $result);
        }
    }
    
    public function testApiCallWithEmptyOptions(): void
    {
        // Test data
        $method = 'GET';
        $uri = 'status';
        $options = [];
        
        // Expected response
        $expectedResponse = new WSResponse(200, ['status' => 'online']);
        
        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');
            
        $this->mockClient->expects($this->once())
            ->method('request')
            ->willReturn($expectedResponse);
        
        // Execute the method
        $result = $this->connector->call($method, $uri, $options);
        
        // Assert results
        $this->assertSame($expectedResponse, $result);
    }
    
    public function testApiCallWithCustomHeaders(): void
    {
        // Test data
        $method = 'GET';
        $uri = 'data';
        $options = ['headers' => ['Accept' => 'application/json']];
        $expectedOptions = [
            'headers' => [
                'Accept' => 'application/json',
                'x-api-key' => $this->apiKey
            ],
            'verify_peer' => false,
            'verify_host' => false
        ];
        
        // Expected response
        $expectedResponse = new WSResponse(200, ['data' => 'value']);
        
        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');
            
        $this->mockClient->expects($this->once())
            ->method('request')
            ->with($method, $this->url . '/' . $uri, $expectedOptions)
            ->willReturn($expectedResponse);
        
        // Execute the method
        $result = $this->connector->call($method, $uri, $options);
        
        // Assert results
        $this->assertSame($expectedResponse, $result);
    }
}
