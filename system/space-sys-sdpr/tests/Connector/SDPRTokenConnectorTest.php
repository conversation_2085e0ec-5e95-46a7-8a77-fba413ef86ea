<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\SDPRTokenConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class SDPRTokenConnectorTest extends TestCase
{
    private $mockClient;
    private $mockLogger;
    private $connector;
    private $apiKey;
    private $urlToken;

    protected function setUp(): void
    {
        echo "Setting up objects for testing !! \n";
        // Create mocks
        $this->mockClient = $this->createMock(CustomHttpClient::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);
        
        // Set test values
        $this->apiKey = 'test-api-key';
        $this->urlToken = 'https://token.example.com/';
        
        // Create the connector to test
        /** @var CustomHttpClient&\PHPUnit\Framework\MockObject\MockObject */
        $this->connector = new SDPRTokenConnector($this->mockClient, $this->apiKey, $this->urlToken);
        
        // Inject mock logger
        $reflection = new \ReflectionClass($this->connector);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->connector, $this->mockLogger);
    }

    protected function tearDown(): void
    {
        echo "Tearing down objects !! \n";
        unset($this->mockClient);
        unset($this->mockLogger);
        unset($this->connector);
    }

    public function testSuccessfulApiCall(): void
    {
        // Test data
        $method = 'GET';
        $uri = 'token/validate';
        $options = ['query' => ['scope' => 'read']];
        $expectedUrl = $this->urlToken . $uri;
        $expectedOptions = [
            'query' => ['scope' => 'read'],
            'headers' => ['api-key' => $this->apiKey],
            'verify_peer' => false,
            'verify_host' => false
        ];
        
        // Expected response
        $expectedResponse = new WSResponse(200, ['valid' => true, 'expires_in' => 3600]);
        
        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('SdprTokenConnector::call ' . $expectedUrl));
            
        $this->mockClient->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $expectedOptions)
            ->willReturn($expectedResponse);
        
        // Execute the method
        $result = $this->connector->call($method, $uri, $options);
        
        // Assert results
        $this->assertSame($expectedResponse, $result);
    }
    
    public function testApiCallWithException(): void
    {
        // Test data
        $method = 'POST';
        $uri = 'token/refresh';
        $options = ['json' => ['refresh_token' => 'abc123']];
        $exceptionMessage = 'Token service unavailable';
        $exceptionCode = 503;
        
        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');
            
        $this->mockLogger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Error occured while calling ' . $uri));
            
        $this->mockClient->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
        
        // Execute the method
        $result = $this->connector->call($method, $uri, $options);
        
        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }
    
    public function testApiCallWithZeroExceptionCode(): void
    {
        // Test data
        $method = 'GET';
        $uri = 'token/info';
        $options = [];
        $exceptionMessage = 'Unknown token error';
        $exceptionCode = 0;
        
        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');
            
        $this->mockLogger->expects($this->once())
            ->method('error');
            
        $this->mockClient->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
        
        // Execute the method
        $result = $this->connector->call($method, $uri, $options);
        
        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }
    
    public function testApiCallWithDifferentHttpMethods(): void
    {
        // Test different HTTP methods
        $methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
        $uri = 'token';
        
        foreach ($methods as $method) {
            // Reset mocks
            $this->setUp();
            
            // Expected response
            $expectedResponse = new WSResponse(200, ['success' => true]);
            
            // Configure mocks
            $this->mockLogger->expects($this->once())
                ->method('info');
                
            $this->mockClient->expects($this->once())
                ->method('request')
                ->with($method, $this->urlToken . $uri, $this->callback(function($options) {
                    return isset($options['headers']['api-key']) && 
                           $options['headers']['api-key'] === $this->apiKey &&
                           $options['verify_peer'] === false &&
                           $options['verify_host'] === false;
                }))
                ->willReturn($expectedResponse);
            
            // Execute the method
            $result = $this->connector->call($method, $uri);
            
            // Assert results
            $this->assertSame($expectedResponse, $result);
        }
    }
    
    public function testApiCallWithEmptyOptions(): void
    {
        // Test data
        $method = 'GET';
        $uri = 'token/status';
        $options = [];
        
        // Expected response
        $expectedResponse = new WSResponse(200, ['status' => 'active']);
        
        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');
            
        $this->mockClient->expects($this->once())
            ->method('request')
            ->willReturn($expectedResponse);
        
        // Execute the method
        $result = $this->connector->call($method, $uri, $options);
        
        // Assert results
        $this->assertSame($expectedResponse, $result);
    }
    
    public function testApiCallWithCustomHeaders(): void
    {
        // Test data
        $method = 'GET';
        $uri = 'token/validate';
        $options = ['headers' => ['Accept' => 'application/json']];
        $expectedOptions = [
            'headers' => [
                'Accept' => 'application/json',
                'api-key' => $this->apiKey
            ],
            'verify_peer' => false,
            'verify_host' => false
        ];
        
        // Expected response
        $expectedResponse = new WSResponse(200, ['valid' => true]);
        
        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');
            
        $this->mockClient->expects($this->once())
            ->method('request')
            ->with($method, $this->urlToken . $uri, $expectedOptions)
            ->willReturn($expectedResponse);
        
        // Execute the method
        $result = $this->connector->call($method, $uri, $options);
        
        // Assert results
        $this->assertSame($expectedResponse, $result);
    }
}