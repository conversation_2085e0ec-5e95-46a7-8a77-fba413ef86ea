<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\NaftaSDPRApiConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class NaftaSDPRApiConnectorTest extends TestCase
{
    private $connector;
    private $mockClient;
    private $mockLogger;
    private $naftaApiKey;
    private $naftaUrl;

    protected function setUp(): void
    {
        $this->mockClient = $this->createMock(CustomHttpClient::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);
        $this->naftaApiKey = 'test-nafta-api-key';
        $this->naftaUrl = 'https://nafta-api.example.com';

        $this->connector = new NaftaSDPRApiConnector(
            $this->mockClient,
            $this->naftaApiKey,
            $this->naftaUrl
        );

        // Set logger using reflection
        $reflection = new \ReflectionClass($this->connector);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->connector, $this->mockLogger);
    }

    /**
     * Test successful API call
     */
    public function testSuccessfulApiCall(): void
    {
        $method = 'GET';
        $uri = 'test/endpoint';
        $options = ['query' => ['param' => 'value']];
        $expectedUrl = $this->naftaUrl . '/' . $uri;
        $expectedOptions = [
            'query' => ['param' => 'value'],
            'headers' => ['x-api-key' => $this->naftaApiKey],
            'verify_peer' => false,
            'verify_host' => false
        ];

        // Expected response
        $expectedResponse = new WSResponse(200, ['status' => 'success', 'data' => ['id' => 1]]);

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('NaftaSDPRApiConnector::call ' . $expectedUrl));

        $this->mockClient->expects($this->once())
            ->method('request')
            ->with($method, $expectedUrl, $expectedOptions)
            ->willReturn($expectedResponse);

        // Execute the method
        $result = $this->connector->call($method, $uri, $options);

        // Assert results
        $this->assertSame($expectedResponse, $result);
    }

    /**
     * Test API call with exception
     */
    public function testApiCallWithException(): void
    {
        $method = 'POST';
        $uri = 'test/endpoint';
        $options = [];
        $exceptionMessage = 'Connection timeout';
        $exceptionCode = 500;

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');

        $this->mockLogger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Error occured while calling ' . $this->naftaUrl . '/' . $uri));

        $this->mockClient->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));

        // Execute the method
        $result = $this->connector->call($method, $uri, $options);

        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }

    /**
     * Test API call with zero exception code
     */
    public function testApiCallWithZeroExceptionCode(): void
    {
        $method = 'PUT';
        $uri = 'test/endpoint';
        $options = [];
        $exceptionMessage = 'Unknown error';
        $exceptionCode = 0;

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');

        $this->mockLogger->expects($this->once())
            ->method('error');

        $this->mockClient->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));

        // Execute the method
        $result = $this->connector->call($method, $uri, $options);

        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }

    /**
     * Test API call with different HTTP methods
     */
    public function testApiCallWithDifferentHttpMethods(): void
    {
        $methods = ['GET', 'POST', 'PUT', 'DELETE'];
        $uri = 'test/endpoint';

        foreach ($methods as $method) {
            // Reset mocks
            $this->setUp();

            // Expected response
            $expectedResponse = new WSResponse(200, ['success' => true]);

            // Configure mocks
            $this->mockLogger->expects($this->once())
                ->method('info');

            $this->mockClient->expects($this->once())
                ->method('request')
                ->with($method, $this->naftaUrl . '/' . $uri, $this->callback(function($options) {
                    return isset($options['headers']['x-api-key']) &&
                           $options['headers']['x-api-key'] === $this->naftaApiKey &&
                           $options['verify_peer'] === false &&
                           $options['verify_host'] === false;
                }))
                ->willReturn($expectedResponse);

            // Execute the method
            $result = $this->connector->call($method, $uri);

            // Assert results
            $this->assertSame($expectedResponse, $result);
        }
    }

    /**
     * Test API call with empty options
     */
    public function testApiCallWithEmptyOptions(): void
    {
        $method = 'GET';
        $uri = 'test/endpoint';
        $options = [];

        // Expected response
        $expectedResponse = new WSResponse(200, ['data' => 'test']);

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');

        $this->mockClient->expects($this->once())
            ->method('request')
            ->willReturn($expectedResponse);

        // Execute the method
        $result = $this->connector->call($method, $uri, $options);

        // Assert results
        $this->assertSame($expectedResponse, $result);
    }

    /**
     * Test API call with custom headers
     */
    public function testApiCallWithCustomHeaders(): void
    {
        $method = 'POST';
        $uri = 'test/endpoint';
        $options = [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer token123'
            ],
            'body' => json_encode(['test' => 'data'])
        ];
        $expectedOptions = [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer token123',
                'x-api-key' => $this->naftaApiKey
            ],
            'body' => json_encode(['test' => 'data']),
            'verify_peer' => false,
            'verify_host' => false
        ];

        // Expected response
        $expectedResponse = new WSResponse(201, ['created' => true]);

        // Configure mocks
        $this->mockLogger->expects($this->once())
            ->method('info');

        $this->mockClient->expects($this->once())
            ->method('request')
            ->with($method, $this->naftaUrl . '/' . $uri, $expectedOptions)
            ->willReturn($expectedResponse);

        // Execute the method
        $result = $this->connector->call($method, $uri, $options);

        // Assert results
        $this->assertSame($expectedResponse, $result);
    }

    protected function tearDown(): void
    {
        unset($this->mockClient);
        unset($this->mockLogger);
        unset($this->connector);
    }
}
