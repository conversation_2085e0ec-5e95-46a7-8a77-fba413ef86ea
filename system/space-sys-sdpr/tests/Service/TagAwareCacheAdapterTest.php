<?php
namespace App\Tests\Service;

use App\Service\TagAwareCacheAdapter;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Cache\Adapter\TagAwareAdapter;
use Symfony\Component\Cache\CacheItem;

class TagAwareCacheAdapterTest extends TestCase
{
    
    public function testConstructor(): void
    {
        $mockCache = $this->getMockBuilder(TagAwareAdapter::class)
            ->disableOriginalConstructor()
            ->getMock();
        $adapter = new TagAwareCacheAdapter($mockCache);
        $this->assertInstanceOf(TagAwareCacheAdapter::class, $adapter);
    }

    public function testInvalidateTags(): void
    {
        $mockCache = $this->getMockBuilder(TagAwareAdapter::class)
            ->disableOriginalConstructor()
            ->getMock();
            
        $mockCache->expects($this->once())
            ->method('invalidateTags')
            ->with(['tag1', 'tag2'])
            ->willReturn(true);

        $adapter = new TagAwareCacheAdapter($mockCache);
        $this->assertTrue($adapter->invalidateTags(['tag1', 'tag2']));
    }

    public function testGetWhenCacheMiss(): void
    {
        $key = 'test_key';
        $value = 'computed_value';

        $cacheItem = new CacheItem();
        $cacheItem->set($value); // Set the value manually
        // Simulate a cache miss
        $reflection = new \ReflectionClass($cacheItem);
        $hitProp = $reflection->getProperty('isHit');
        $hitProp->setAccessible(true);
        $hitProp->setValue($cacheItem, false);

        $mockCache = $this->getMockBuilder(TagAwareAdapter::class)
            ->disableOriginalConstructor()
            ->getMock();
            
        $mockCache->method('getItem')
            ->with($key)
            ->willReturn($cacheItem);
            
        $mockCache->expects($this->once())
            ->method('save')
            ->willReturn(true);

        $adapter = new TagAwareCacheAdapter($mockCache);
        $result = $adapter->get($key, fn() => $value);
        
        $this->assertEquals($value, $result);
    }
    /**
     * @runInSeparateProcess
     */
    public function testGetWhenCacheHit(): void
    {
        $key = 'cached_key';
        $cachedValue = 'from_cache';

        // ✅ Create a real CacheItem instance
        // $cacheItem = new \Symfony\Component\Cache\CacheItem();
        $cacheItem = new CacheItem();
        $cacheItem->set($cachedValue);

        // ✅ Simulate a cache hit using reflection
        $ref = new \ReflectionClass($cacheItem);
        $prop = $ref->getProperty('isHit');
        $prop->setAccessible(true);
        $prop->setValue($cacheItem, true);

        // Mock the cache adapter
        $mockCache = $this->getMockBuilder(\Symfony\Component\Cache\Adapter\TagAwareAdapter::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockCache->method('getItem')
            ->with($key)
            ->willReturn($cacheItem);

        $mockCache->expects($this->never())
            ->method('save');

        $adapter = new \App\Service\TagAwareCacheAdapter($mockCache);
        $result = $adapter->get($key, fn() => 'should_not_run');

        $this->assertEquals($cachedValue, $result);
    }

    /**
     * @runInSeparateProcess
     */
    public function testDeleteWhenItemIsHit(): void
    {
        $key = 'key_to_delete';
        // $cacheItem = new \Symfony\Component\Cache\CacheItem();
        $cacheItem = new CacheItem();

        // Set the "isHit" property to true via reflection
        $reflection = new \ReflectionClass($cacheItem);
        $property = $reflection->getProperty('isHit');
        $property->setAccessible(true);
        $property->setValue($cacheItem, true);

        $mockCache = $this->getMockBuilder(\Symfony\Component\Cache\Adapter\TagAwareAdapter::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockCache->method('getItem')
            ->with($key)
            ->willReturn($cacheItem);

        $mockCache->expects($this->once())
            ->method('deleteItem')
            ->with($key)
            ->willReturn(true);

        $adapter = new \App\Service\TagAwareCacheAdapter($mockCache);
        $this->assertTrue($adapter->delete($key));
    }
    /**
     * @runInSeparateProcess
     */
    public function testDeleteWhenItemNotHit(): void
    {
        $key = 'nonexistent_key';

        $cacheItem = new CacheItem();

        // Set isHit to false using reflection
        $reflection = new \ReflectionClass($cacheItem);
        $property = $reflection->getProperty('isHit');
        $property->setAccessible(true);
        $property->setValue($cacheItem, false);

        $mockCache = $this->getMockBuilder(\Symfony\Component\Cache\Adapter\TagAwareAdapter::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockCache->method('getItem')
            ->with($key)
            ->willReturn($cacheItem);

        $mockCache->expects($this->never())
            ->method('deleteItem');

        $adapter = new \App\Service\TagAwareCacheAdapter($mockCache);
        $this->assertTrue($adapter->delete($key));
    }
}
