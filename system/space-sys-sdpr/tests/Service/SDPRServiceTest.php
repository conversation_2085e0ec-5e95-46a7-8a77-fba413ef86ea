<?php

namespace App\Tests\Service;

use App\Connector\SDPRApiConnector;
use App\Connector\SDPRTokenConnector;
use App\Connector\NaftaSDPRApiConnector;
use App\Connector\NaftaSDPRTokenConnector;
use App\Helper\FileHelper;
use App\Helper\WSResponse;
use App\Service\SDPRService;
use App\Service\TagAwareCacheAdapter;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Cache\ItemInterface;

class SDPRServiceTest extends TestCase
{
    private SDPRService $sdprService;
    private $mockTokenConnector;
    private $mockSdprConnector;
    private $mockNaftaSdprConnector;
    private $mockNaftaTokenConnector;
    private $mockFileHelper;
    private $mockTagAwareCacheAdapter;
    private $mockLogger;
    private $mockCacheItem;

    private string $clientId = 'test-client-id';
    private string $secret = 'test-secret';
    private string $clientRequestId = 'test-request-id';
    private string $certifDir = '/path/to/certs';
    private string $certName = 'test-cert';
    private string $naftaCertName = 'test-nafta-cert';
    private string $naftaClientId = 'test-nafta-client-id';
    private string $naftaSecret = 'test-nafta-secret';
    private string $naftaClientRequestId = 'test-nafta-request-id';

    protected function setUp(): void
    {
        $this->mockTokenConnector = $this->createMock(SDPRTokenConnector::class);
        $this->mockSdprConnector = $this->createMock(SDPRApiConnector::class);
        $this->mockNaftaSdprConnector = $this->createMock(NaftaSDPRApiConnector::class);
        $this->mockNaftaTokenConnector = $this->createMock(NaftaSDPRTokenConnector::class);
        $this->mockFileHelper = $this->createMock(FileHelper::class);
        $this->mockTagAwareCacheAdapter = $this->createMock(TagAwareCacheAdapter::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);
        $this->mockCacheItem = $this->createMock(ItemInterface::class);

        $this->sdprService = new SDPRService(
            $this->clientId,
            $this->secret,
            $this->clientRequestId,
            $this->certifDir,
            $this->certName,
            $this->naftaCertName,
            $this->naftaClientId,
            $this->naftaSecret,
            $this->naftaClientRequestId,
            $this->mockTokenConnector,
            $this->mockSdprConnector,
            $this->mockNaftaSdprConnector,
            $this->mockNaftaTokenConnector,
            $this->mockFileHelper,
            $this->mockTagAwareCacheAdapter
        );

        // Set logger using reflection to avoid modifying the original class
        $reflection = new \ReflectionClass($this->sdprService);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->sdprService, $this->mockLogger);
    }

    /**
     * Test getSdprToken with successful token retrieval
     */
    public function testGetSdprTokenSuccess(): void
    {
        $userId = 'test-user-123';
        $accessToken = 'test-access-token';
        $expiresIn = 3600;
        $certPath = $this->certifDir . '/test-cert.pem';

        // Configure mocks
        $this->mockFileHelper->expects($this->once())
            ->method('setExtension')
            ->with($this->certName, '.pem')
            ->willReturn('test-cert.pem');

        $mockResponse = new WSResponse(Response::HTTP_OK, [
            'access_token' => $accessToken,
            'expires_in' => $expiresIn
        ]);

        $this->mockTokenConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_POST,
                '/token',
                $this->callback(function($options) use ($certPath) {
                    return
                        isset($options['headers']['Content-Type']) &&
                        $options['headers']['Content-Type'] === 'application/x-www-form-urlencoded' &&
                        isset($options['body']['grant_type']) &&
                        $options['body']['grant_type'] === 'client_credentials' &&
                        isset($options['body']['client_id']) &&
                        $options['body']['client_id'] === $this->clientId &&
                        isset($options['body']['client_secret']) &&
                        $options['body']['client_secret'] === $this->secret &&
                        isset($options['local_cert']) &&
                        $options['local_cert'] === $certPath;
                })
            )
            ->willReturn($mockResponse);

        $this->mockCacheItem->expects($this->once())
            ->method('expiresAfter')
            ->with($expiresIn - 30);

        $this->mockTagAwareCacheAdapter->expects($this->once())
            ->method('get')
            ->with("SDPR-TOKEN_" . $userId)
            ->willReturnCallback(function($key, $callback) {
                return $callback($this->mockCacheItem);
            });

        // Execute the method
        $result = $this->sdprService->getSdprToken($userId);

        // Assert result
        $this->assertEquals($accessToken, $result);
    }

    /**
     * Test getUserVehicles with successful response
     */
    public function testGetUserVehiclesSuccess(): void
    {
        $userId = 'test-user-123';
        $accessToken = 'test-access-token';
        $certPath = $this->certifDir . '/test-cert.pem';
        $vehiclesData = [
            'vehicles' => [
                ['id' => 'v1', 'make' => 'Toyota', 'model' => 'Corolla'],
                ['id' => 'v2', 'make' => 'Honda', 'model' => 'Civic']
            ]
        ];

        // Configure mocks for getSdprToken
        $this->mockTagAwareCacheAdapter->expects($this->once())
            ->method('get')
            ->with("SDPR-TOKEN_" . $userId)
            ->willReturn($accessToken);

        $this->mockFileHelper->expects($this->once())
            ->method('setExtension')
            ->with($this->certName, '.pem')
            ->willReturn('test-cert.pem');

        $mockResponse = new WSResponse(Response::HTTP_OK, $vehiclesData);

        $this->mockSdprConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                $userId . '/vehicles',
                $this->callback(function($options) use ($accessToken, $certPath) {
                    return
                        isset($options['headers']['Authorization']) &&
                        $options['headers']['Authorization'] === 'Bearer ' . $accessToken &&
                        isset($options['headers']['x-originator-type']) &&
                        $options['headers']['x-originator-type'] === 'server' &&
                        isset($options['headers']['content-type']) &&
                        $options['headers']['content-type'] === 'application/json' &&
                        isset($options['headers']['clientrequestid']) &&
                        $options['headers']['clientrequestid'] === $this->clientRequestId &&
                        isset($options['query']['stage']) &&
                        $options['query']['stage'] === 'ALL' &&
                        isset($options['query']['sdp']) &&
                        $options['query']['sdp'] === 'ALL' &&
                        isset($options['local_cert']) &&
                        $options['local_cert'] === $certPath;
                })
            )
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->sdprService->getUserVehicles($userId);

        // Assert result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals(['vehicles' => $vehiclesData['vehicles']], $result->getData());
    }

    /**
     * Test getUserVehicles with empty vehicles response
     */
    public function testGetUserVehiclesEmptyResponse(): void
    {
        $userId = 'test-user-123';
        $accessToken = 'test-access-token';

        // Configure mocks for getSdprToken
        $this->mockTagAwareCacheAdapter->expects($this->once())
            ->method('get')
            ->willReturn($accessToken);

        $this->mockFileHelper->expects($this->once())
            ->method('setExtension')
            ->willReturn('test-cert.pem');

        $mockResponse = new WSResponse(Response::HTTP_OK, ['vehicles' => []]);

        $this->mockSdprConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->sdprService->getUserVehicles($userId);

        // Assert result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals(['vehicles' => []], $result->getData());
    }

    /**
     * Test getUserVehicles with error response
     */
    public function testGetUserVehiclesErrorResponse(): void
    {
        $userId = 'test-user-123';
        $accessToken = 'test-access-token';
        $errorCode = Response::HTTP_BAD_REQUEST;
        $errorData = 'Invalid request';

        // Configure mocks for getSdprToken
        $this->mockTagAwareCacheAdapter->expects($this->once())
            ->method('get')
            ->willReturn($accessToken);

        $this->mockFileHelper->expects($this->once())
            ->method('setExtension')
            ->willReturn('test-cert.pem');

        $mockResponse = new WSResponse($errorCode, $errorData);

        $this->mockSdprConnector->expects($this->once())
            ->method('call')
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->sdprService->getUserVehicles($userId);

        // Assert result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($errorCode, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }

    /**
     * Test getUserVehicles with exception
     */
    public function testGetUserVehiclesException(): void
    {
        $userId = 'test-user-123';
        $exceptionMessage = 'Connection error';
        $exceptionCode = 500;

        // Configure mocks for getSdprToken
        $this->mockTagAwareCacheAdapter->expects($this->once())
            ->method('get')
            ->willReturn('test-access-token');

        $this->mockFileHelper->expects($this->once())
            ->method('setExtension')
            ->willReturn('test-cert.pem');

        $this->mockSdprConnector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));

        $this->mockLogger->expects($this->once())
            ->method('error')
            ->with('Error SDPR Get Vehicles Exception ' . $exceptionMessage);

        // Execute the method
        $result = $this->sdprService->getUserVehicles($userId);

        // Assert result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }

    /**
     * Test getUserVehicles with exception having zero code
     */
    public function testGetUserVehiclesExceptionWithZeroCode(): void
    {
        $userId = 'test-user-123';
        $exceptionMessage = 'Unknown error';
        $exceptionCode = 0;

        // Configure mocks for getSdprToken
        $this->mockTagAwareCacheAdapter->expects($this->once())
            ->method('get')
            ->willReturn('test-access-token');

        $this->mockFileHelper->expects($this->once())
            ->method('setExtension')
            ->willReturn('test-cert.pem');

        $this->mockSdprConnector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));

        $this->mockLogger->expects($this->once())
            ->method('error')
            ->with('Error SDPR Get Vehicles Exception ' . $exceptionMessage);

        // Execute the method
        $result = $this->sdprService->getUserVehicles($userId);

        // Assert result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }

    /**
     * Test getCertifFile method
     */
    public function testGetCertifFile(): void
    {
        $certifName = 'test-cert';
        $extension = '.pem';
        $expectedFileName = 'test-cert.pem';
        $expectedPath = $this->certifDir . '/' . $expectedFileName;

        // Configure mocks
        $this->mockFileHelper->expects($this->once())
            ->method('setExtension')
            ->with($certifName, $extension)
            ->willReturn($expectedFileName);

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->sdprService);
        $method = $reflection->getMethod('getCertifFile');
        $method->setAccessible(true);

        // Execute the method
        $result = $method->invoke($this->sdprService, $certifName, $extension);

        // Assert result
        $this->assertEquals($expectedPath, $result);
    }

    /**
     * Test getNaftaSdprToken with successful token retrieval
     */
    public function testGetNaftaSdprTokenSuccess(): void
    {
        $userId = 'test-user-123';
        $accessToken = 'test-nafta-access-token';
        $expiresIn = 3600;
        $certPath = $this->certifDir . '/test-nafta-cert.pem';

        // Configure mocks
        $this->mockFileHelper->expects($this->once())
            ->method('setExtension')
            ->with($this->naftaCertName, '.pem')
            ->willReturn('test-nafta-cert.pem');

        $mockResponse = new WSResponse(Response::HTTP_OK, [
            'access_token' => $accessToken,
            'expires_in' => $expiresIn
        ]);

        $this->mockNaftaTokenConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_POST,
                '/token',
                $this->callback(function($options) use ($certPath) {
                    return
                        isset($options['headers']['Content-Type']) &&
                        $options['headers']['Content-Type'] === 'application/x-www-form-urlencoded' &&
                        isset($options['body']['grant_type']) &&
                        $options['body']['grant_type'] === 'client_credentials' &&
                        isset($options['body']['client_id']) &&
                        $options['body']['client_id'] === $this->naftaClientId &&
                        isset($options['body']['client_secret']) &&
                        $options['body']['client_secret'] === $this->naftaSecret &&
                        isset($options['local_cert']) &&
                        $options['local_cert'] === $certPath;
                })
            )
            ->willReturn($mockResponse);

        $this->mockCacheItem->expects($this->once())
            ->method('expiresAfter')
            ->with($expiresIn - 30);

        $this->mockTagAwareCacheAdapter->expects($this->once())
            ->method('get')
            ->with("NAFTA-SDPR-TOKEN_" . $userId)
            ->willReturnCallback(function($key, $callback) {
                return $callback($this->mockCacheItem);
            });

        // Execute the method
        $result = $this->sdprService->getNaftaSdprToken($userId);

        // Assert result
        $this->assertEquals($accessToken, $result);
    }

    /**
     * Test getNaftaUserVehicles with successful response
     */
    public function testGetNaftaUserVehiclesSuccess(): void
    {
        $userId = 'test-user-123';
        $accessToken = 'test-nafta-access-token';
        $certPath = $this->certifDir . '/test-nafta-cert.pem';
        $vehiclesData = [
            'vehicles' => [
                ['id' => 'v1', 'make' => 'Toyota', 'model' => 'Corolla'],
                ['id' => 'v2', 'make' => 'Honda', 'model' => 'Civic']
            ]
        ];

        // Configure mocks for getNaftaSdprToken
        $this->mockTagAwareCacheAdapter->expects($this->once())
            ->method('get')
            ->with("NAFTA-SDPR-TOKEN_" . $userId)
            ->willReturn($accessToken);

        $this->mockFileHelper->expects($this->once())
            ->method('setExtension')
            ->with($this->naftaCertName, '.pem')
            ->willReturn('test-nafta-cert.pem');

        $mockResponse = new WSResponse(Response::HTTP_OK, $vehiclesData);

        $this->mockNaftaSdprConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                $userId . '/vehicles',
                $this->callback(function($options) use ($accessToken, $certPath) {
                    return
                        isset($options['headers']['Authorization']) &&
                        $options['headers']['Authorization'] === 'Bearer ' . $accessToken &&
                        isset($options['headers']['x-originator-type']) &&
                        $options['headers']['x-originator-type'] === 'server' &&
                        isset($options['headers']['content-type']) &&
                        $options['headers']['content-type'] === 'application/json' &&
                        isset($options['headers']['clientrequestid']) &&
                        $options['headers']['clientrequestid'] === $this->naftaClientRequestId &&
                        isset($options['query']['stage']) &&
                        $options['query']['stage'] === 'ALL' &&
                        isset($options['query']['sdp']) &&
                        $options['query']['sdp'] === 'ALL' &&
                        isset($options['local_cert']) &&
                        $options['local_cert'] === $certPath;
                })
            )
            ->willReturn($mockResponse);

        // Execute the method
        $result = $this->sdprService->getNaftaUserVehicles($userId);

        // Assert result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals(['vehicles' => $vehiclesData['vehicles']], $result->getData());
    }

    protected function tearDown(): void
    {
        unset($this->mockTokenConnector);
        unset($this->mockSdprConnector);
        unset($this->mockNaftaSdprConnector);
        unset($this->mockNaftaTokenConnector);
        unset($this->mockFileHelper);
        unset($this->mockTagAwareCacheAdapter);
        unset($this->mockLogger);
        unset($this->mockCacheItem);
        unset($this->sdprService);
    }
}