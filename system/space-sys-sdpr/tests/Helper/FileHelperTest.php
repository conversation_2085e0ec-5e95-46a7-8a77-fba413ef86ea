<?php

namespace App\Tests\Helper;

use App\Helper\FileHelper;
use PHPUnit\Framework\TestCase;

class FileHelperTest extends TestCase
{
    private FileHelper $fileHelper;

    protected function setUp(): void
    {
        $this->fileHelper = new FileHelper();
    }

    /**
     * Test when filename already has the correct extension
     */
    public function testSetExtensionWhenExtensionAlreadyExists(): void
    {
        $fileName = 'certificate.cer';
        $extension = '.cer';
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals($fileName, $result);
        $this->assertEquals('certificate.cer', $result);
    }

    /**
     * Test when filename doesn't have the extension
     */
    public function testSetExtensionWhenExtensionDoesNotExist(): void
    {
        $fileName = 'certificate';
        $extension = '.cer';
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals($fileName . $extension, $result);
        $this->assertEquals('certificate.cer', $result);
    }

    /**
     * Test with different extension types
     */
    public function testSetExtensionWithDifferentExtensionTypes(): void
    {
        // Test with .pdf extension
        $this->assertEquals('document.pdf', $this->fileHelper->setExtension('document', '.pdf'));
        $this->assertEquals('document.pdf', $this->fileHelper->setExtension('document.pdf', '.pdf'));
        
        // Test with .txt extension
        $this->assertEquals('notes.txt', $this->fileHelper->setExtension('notes', '.txt'));
        $this->assertEquals('notes.txt', $this->fileHelper->setExtension('notes.txt', '.txt'));
        
        // Test with .jpg extension
        $this->assertEquals('image.jpg', $this->fileHelper->setExtension('image', '.jpg'));
        $this->assertEquals('image.jpg', $this->fileHelper->setExtension('image.jpg', '.jpg'));
    }

    /**
     * Test with extension that doesn't start with a dot
     */
    public function testSetExtensionWithExtensionWithoutDot(): void
    {
        $fileName = 'document';
        $extension = 'pdf'; // No leading dot
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals('documentpdf', $result);
    }

    /**
     * Test with empty filename
     */
    public function testSetExtensionWithEmptyFileName(): void
    {
        $fileName = '';
        $extension = '.cer';
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals($extension, $result);
    }

    /**
     * Test with empty extension
     */
    public function testSetExtensionWithEmptyExtension(): void
    {
        $fileName = 'certificate';
        $extension = '';
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals($fileName, $result);
    }

    /**
     * Test with filename containing dots in the middle
     */
    public function testSetExtensionWithFileNameContainingDots(): void
    {
        $fileName = 'certificate.v1.2';
        $extension = '.cer';
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals('certificate.v1.2.cer', $result);
    }

    /**
     * Test with filename already having a different extension
     */
    public function testSetExtensionWithFileNameHavingDifferentExtension(): void
    {
        $fileName = 'certificate.txt';
        $extension = '.cer';
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals('certificate.txt.cer', $result);
    }

    /**
     * Test with case-sensitive extensions
     */
    public function testSetExtensionWithCaseSensitiveExtensions(): void
    {
        // Test with lowercase extension
        $this->assertEquals('document.pdf', $this->fileHelper->setExtension('document', '.pdf'));
        
        // Test with uppercase extension in filename -- Bug was caught.
        // The current implementation doesn't do case-sensitive comparison
        // So it adds .pdf even when .PDF already exists
        $this->assertEquals('document.PDF.pdf', $this->fileHelper->setExtension('document.PDF', '.pdf'));
        
        // Test with uppercase extension parameter
        $this->assertEquals('document.PDF', $this->fileHelper->setExtension('document', '.PDF'));
    }

    /**
     * Test with special characters in filename
     */
    public function testSetExtensionWithSpecialCharactersInFileName(): void
    {
        $fileName = 'certificate-123_$#@!';
        $extension = '.cer';
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals('certificate-123_$#@!.cer', $result);
    }

    /**
     * Test with very long filename
     */
    public function testSetExtensionWithVeryLongFileName(): void
    {
        $fileName = str_repeat('a', 1000);
        $extension = '.cer';
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals($fileName . $extension, $result);
        $this->assertEquals(1004, strlen($result));
    }

    /**
     * Test with extension containing multiple dots
     */
    public function testSetExtensionWithMultipleDotsInExtension(): void
    {
        $fileName = 'archive';
        $extension = '.tar.gz';
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals('archive.tar.gz', $result);
    }

    /**
     * Test with filename ending with the extension substring but not the full extension
     */
    public function testSetExtensionWithPartialExtensionMatch(): void
    {
        $fileName = 'mycer';
        $extension = '.cer';
        
        $result = $this->fileHelper->setExtension($fileName, $extension);
        
        $this->assertEquals('mycer.cer', $result);
    }

    protected function tearDown(): void
    {
        unset($this->fileHelper);
    }
}
