<?php

namespace App\Tests\Helper;

use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class SuccessResponseTest extends TestCase
{
    /**
     * Test constructor with array data and default code
     */
    public function testConstructWithArrayData(): void
    {
        $data = ['id' => 1, 'name' => 'Test User'];
        $response = new SuccessResponse($data);
        
        $this->assertEquals($data, $response->getData());
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
    }
    
    /**
     * Test constructor with array data and custom code
     */
    public function testConstructWithArrayDataAndCustomCode(): void
    {
        $data = ['id' => 1, 'name' => 'Test User'];
        $code = Response::HTTP_CREATED;
        $response = new SuccessResponse($data, $code);
        
        $this->assertEquals($data, $response->getData());
        $this->assertEquals($code, $response->getCode());
    }
    
    /**
     * Test constructor with string data (JSON)
     */
    public function testConstructWithJsonString(): void
    {
        $jsonString = '{"id": 1, "name": "Test User"}';
        $response = new SuccessResponse($jsonString);
        
        $result = $response->toArray();
        
        // When toArray is called, the JSON string should be decoded
        $this->assertEquals(['id' => 1, 'name' => 'Test User'], $result['content']['success']);
    }
    
    /**
     * Test constructor with null code
     */
    public function testConstructWithNullCode(): void
    {
        $data = ['status' => 'active'];
        $response = new SuccessResponse($data, null);
        
        $this->assertEquals($data, $response->getData());
        $this->assertNull($response->getCode());
    }
    
    /**
     * Test setCode method
     */
    public function testSetCode(): void
    {
        $response = new SuccessResponse(['id' => 1]);
        $newCode = Response::HTTP_ACCEPTED;
        
        $result = $response->setCode($newCode);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        $this->assertEquals($newCode, $response->getCode());
    }
    
    /**
     * Test setData method
     */
    public function testSetData(): void
    {
        $response = new SuccessResponse(['id' => 1]);
        $newData = ['id' => 2, 'status' => 'updated'];
        
        $result = $response->setData($newData);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        $this->assertEquals($newData, $response->getData());
    }
    
    /**
     * Test toArray method with array data
     */
    public function testToArrayWithArrayData(): void
    {
        $data = ['id' => 1, 'name' => 'Test User'];
        $code = Response::HTTP_OK;
        $response = new SuccessResponse($data, $code);
        
        $result = $response->toArray();
        
        $this->assertEquals([
            'content' => ['success' => $data],
            'code' => $code
        ], $result);
    }
    
    /**
     * Test toArray method with JSON string data
     */
    public function testToArrayWithJsonStringData(): void
    {
        $jsonString = '{"id": 1, "name": "Test User"}';
        $code = Response::HTTP_OK;
        $response = new SuccessResponse($jsonString, $code);
        
        $result = $response->toArray();
        
        $this->assertEquals([
            'content' => ['success' => ['id' => 1, 'name' => 'Test User']],
            'code' => $code
        ], $result);
    }
    
    /**
     * Test toArray method with invalid JSON string
     */
    public function testToArrayWithInvalidJsonString(): void
    {
        $invalidJson = '{id: 1, name: Test User}'; // Missing quotes
        $response = new SuccessResponse($invalidJson);
        
        $result = $response->toArray();
        
        // When JSON is invalid, it should be treated as a regular string
        $this->assertEquals([
            'content' => ['success' => null], // json_decode returns null for invalid JSON
            'code' => Response::HTTP_OK
        ], $result);
    }
    
    /**
     * Test with empty array data
     */
    public function testWithEmptyArrayData(): void
    {
        $data = [];
        $response = new SuccessResponse($data);
        
        $result = $response->toArray();
        
        $this->assertEquals([
            'content' => ['success' => []],
            'code' => Response::HTTP_OK
        ], $result);
    }
    
    /**
     * Test with null data
     */
    public function testWithNullData(): void
    {
        $data = null;
        $response = new SuccessResponse($data);
        
        $result = $response->toArray();
        
        $this->assertEquals([
            'content' => ['success' => null],
            'code' => Response::HTTP_OK
        ], $result);
    }
    
    /**
     * Test with boolean data
     */
    public function testWithBooleanData(): void
    {
        $data = true;
        $response = new SuccessResponse($data);
        
        $result = $response->toArray();
        
        $this->assertEquals([
            'content' => ['success' => true],
            'code' => Response::HTTP_OK
        ], $result);
    }
    
    /**
     * Test with integer data
     */
    public function testWithIntegerData(): void
    {
        $data = 42;
        $response = new SuccessResponse($data);
        
        $result = $response->toArray();
        
        $this->assertEquals([
            'content' => ['success' => 42],
            'code' => Response::HTTP_OK
        ], $result);
    }
    
    /**
     * Test with complex nested array data
     */
    public function testWithComplexNestedArrayData(): void
    {
        $data = [
            'user' => [
                'id' => 1,
                'profile' => [
                    'name' => 'John Doe',
                    'email' => '<EMAIL>'
                ]
            ],
            'permissions' => ['read', 'write'],
            'active' => true
        ];
        $response = new SuccessResponse($data);
        
        $result = $response->toArray();
        
        $this->assertEquals([
            'content' => ['success' => $data],
            'code' => Response::HTTP_OK
        ], $result);
    }
    
    /**
     * Test chained methods
     */
    public function testChainedMethods(): void
    {
        $initialData = ['status' => 'pending'];
        $newData = ['status' => 'completed', 'timestamp' => time()];
        $newCode = Response::HTTP_CREATED;
        
        $response = new SuccessResponse($initialData);
        $response->setData($newData)
                 ->setCode($newCode);
        
        $result = $response->toArray();
        
        $this->assertEquals([
            'content' => ['success' => $newData],
            'code' => $newCode
        ], $result);
    }
    
    /**
     * Test that the class implements ResponseArrayFormat
     */
    public function testImplementsResponseArrayFormat(): void
    {
        $response = new SuccessResponse([]);
        
        $this->assertInstanceOf('App\Helper\ResponseArrayFormat', $response);
    }
}