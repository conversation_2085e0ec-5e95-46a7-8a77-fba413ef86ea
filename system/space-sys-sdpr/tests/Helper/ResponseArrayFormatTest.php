<?php

namespace App\Tests\Helper;

use App\Helper\ResponseArrayFormat;
use PHPUnit\Framework\TestCase;

class ResponseArrayFormatTest extends TestCase
{
    /**
     * Test that a class implementing ResponseArrayFormat has the toArray method
     */
    public function testInterfaceRequiresToArrayMethod(): void
    {
        // Create a mock of the interface
        $mock = $this->createMock(ResponseArrayFormat::class);
        
        // Verify the mock has the toArray method
        $this->assertTrue(method_exists($mock, 'toArray'));
        
        // Set up the mock to return a sample array
        $expectedArray = ['key' => 'value'];
        $mock->method('toArray')->willReturn($expectedArray);
        
        // Call the method and verify it returns the expected array
        $result = $mock->toArray();
        $this->assertIsArray($result);
        $this->assertEquals($expectedArray, $result);
    }
    
    /**
     * Test with a concrete implementation of the interface
     */
    public function testConcreteImplementation(): void
    {
        // Create a concrete implementation of the interface
        $implementation = new class implements ResponseArrayFormat {
            public function toArray(): array
            {
                return ['status' => 'success', 'data' => ['id' => 1, 'name' => 'Test']];
            }
        };
        
        // Call the method and verify it returns the expected array
        $result = $implementation->toArray();
        $this->assertIsArray($result);
        $this->assertArrayHasKey('status', $result);
        $this->assertArrayHasKey('data', $result);
        $this->assertEquals('success', $result['status']);
        $this->assertIsArray($result['data']);
        $this->assertEquals(1, $result['data']['id']);
        $this->assertEquals('Test', $result['data']['name']);
    }
    
    /**
     * Test with multiple implementations of the interface
     */
    public function testMultipleImplementations(): void
    {
        // Create first implementation
        $implementation1 = new class implements ResponseArrayFormat {
            public function toArray(): array
            {
                return ['type' => 'success', 'code' => 200];
            }
        };
        
        // Create second implementation
        $implementation2 = new class implements ResponseArrayFormat {
            public function toArray(): array
            {
                return ['type' => 'error', 'code' => 400, 'message' => 'Bad request'];
            }
        };
        
        // Test first implementation
        $result1 = $implementation1->toArray();
        $this->assertIsArray($result1);
        $this->assertEquals('success', $result1['type']);
        $this->assertEquals(200, $result1['code']);
        
        // Test second implementation
        $result2 = $implementation2->toArray();
        $this->assertIsArray($result2);
        $this->assertEquals('error', $result2['type']);
        $this->assertEquals(400, $result2['code']);
        $this->assertEquals('Bad request', $result2['message']);
    }
    
    /**
     * Test with an implementation returning an empty array
     */
    public function testEmptyArrayImplementation(): void
    {
        // Create implementation that returns an empty array
        $implementation = new class implements ResponseArrayFormat {
            public function toArray(): array
            {
                return [];
            }
        };
        
        // Call the method and verify it returns an empty array
        $result = $implementation->toArray();
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }
    
    /**
     * Test with an implementation returning a nested array
     */
    public function testNestedArrayImplementation(): void
    {
        // Create implementation that returns a deeply nested array
        $implementation = new class implements ResponseArrayFormat {
            public function toArray(): array
            {
                return [
                    'level1' => [
                        'level2' => [
                            'level3' => [
                                'data' => 'Deep value'
                            ]
                        ]
                    ]
                ];
            }
        };
        
        // Call the method and verify the nested structure
        $result = $implementation->toArray();
        $this->assertIsArray($result);
        $this->assertArrayHasKey('level1', $result);
        $this->assertIsArray($result['level1']);
        $this->assertArrayHasKey('level2', $result['level1']);
        $this->assertIsArray($result['level1']['level2']);
        $this->assertArrayHasKey('level3', $result['level1']['level2']);
        $this->assertIsArray($result['level1']['level2']['level3']);
        $this->assertEquals('Deep value', $result['level1']['level2']['level3']['data']);
    }
    
    /**
     * Test with an implementation that includes various data types
     */
    public function testMixedDataTypesImplementation(): void
    {
        // Create implementation that returns various data types
        $implementation = new class implements ResponseArrayFormat {
            public function toArray(): array
            {
                return [
                    'string' => 'text value',
                    'integer' => 42,
                    'float' => 3.14,
                    'boolean' => true,
                    'null' => null,
                    'array' => [1, 2, 3],
                    'object' => (object)['key' => 'value']
                ];
            }
        };
        
        // Call the method and verify all data types
        $result = $implementation->toArray();
        $this->assertIsArray($result);
        $this->assertIsString($result['string']);
        $this->assertIsInt($result['integer']);
        $this->assertIsFloat($result['float']);
        $this->assertIsBool($result['boolean']);
        $this->assertNull($result['null']);
        $this->assertIsArray($result['array']);
        $this->assertIsObject($result['object']);
    }
}