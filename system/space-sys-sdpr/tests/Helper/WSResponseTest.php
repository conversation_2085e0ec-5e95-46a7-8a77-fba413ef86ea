<?php

namespace App\Tests\Helper;

use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class WSResponseTest extends TestCase
{
    /**
     * Test constructor with integer code and string data
     */
    public function testConstructWithIntegerCodeAndStringData(): void
    {
        $code = Response::HTTP_OK;
        $data = 'Success message';
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
    
    /**
     * Test constructor with integer code and array data
     */
    public function testConstructWithIntegerCodeAndArrayData(): void
    {
        $code = Response::HTTP_CREATED;
        $data = ['id' => 1, 'name' => 'Test User'];
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
    
    /**
     * Test constructor with error code and error message
     */
    public function testConstructWithErrorCodeAndErrorMessage(): void
    {
        $code = Response::HTTP_BAD_REQUEST;
        $data = 'Invalid input';
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
    
    /**
     * Test setCode method
     */
    public function testSetCode(): void
    {
        $initialCode = Response::HTTP_OK;
        $newCode = Response::HTTP_CREATED;
        $data = 'Test data';
        
        $response = new WSResponse($initialCode, $data);
        $result = $response->setCode($newCode);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        $this->assertEquals($newCode, $response->getCode());
    }
    
    /**
     * Test setData method with string
     */
    public function testSetDataWithString(): void
    {
        $code = Response::HTTP_OK;
        $initialData = 'Initial data';
        $newData = 'Updated data';
        
        $response = new WSResponse($code, $initialData);
        $result = $response->setData($newData);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        $this->assertEquals($newData, $response->getData());
    }
    
    /**
     * Test setData method with array
     */
    public function testSetDataWithArray(): void
    {
        $code = Response::HTTP_OK;
        $initialData = ['status' => 'pending'];
        $newData = ['status' => 'completed', 'timestamp' => time()];
        
        $response = new WSResponse($code, $initialData);
        $result = $response->setData($newData);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        $this->assertEquals($newData, $response->getData());
    }
    
    /**
     * Test setData method with object
     */
    public function testSetDataWithObject(): void
    {
        $code = Response::HTTP_OK;
        $initialData = 'Initial data';
        
        $objectData = new \stdClass();
        $objectData->id = 1;
        $objectData->name = 'Test Object';
        
        $response = new WSResponse($code, $initialData);
        $result = $response->setData($objectData);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        $this->assertEquals($objectData, $response->getData());
    }
    
    /**
     * Test setData method with null
     */
    public function testSetDataWithNull(): void
    {
        $code = Response::HTTP_OK;
        $initialData = 'Initial data';
        $newData = null;
        
        $response = new WSResponse($code, $initialData);
        $result = $response->setData($newData);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        $this->assertNull($response->getData());
    }
    
    /**
     * Test setData method with boolean
     */
    public function testSetDataWithBoolean(): void
    {
        $code = Response::HTTP_OK;
        $initialData = 'Initial data';
        $newData = true;
        
        $response = new WSResponse($code, $initialData);
        $result = $response->setData($newData);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        $this->assertTrue($response->getData());
    }
    
    /**
     * Test setData method with integer
     */
    public function testSetDataWithInteger(): void
    {
        $code = Response::HTTP_OK;
        $initialData = 'Initial data';
        $newData = 42;
        
        $response = new WSResponse($code, $initialData);
        $result = $response->setData($newData);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        $this->assertEquals(42, $response->getData());
    }
    
    /**
     * Test with complex nested array data
     */
    public function testWithComplexNestedArrayData(): void
    {
        $code = Response::HTTP_OK;
        $data = [
            'user' => [
                'id' => 1,
                'profile' => [
                    'name' => 'John Doe',
                    'email' => '<EMAIL>'
                ]
            ],
            'permissions' => ['read', 'write'],
            'active' => true
        ];
        
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
    
    /**
     * Test chained methods
     */
    public function testChainedMethods(): void
    {
        $initialCode = Response::HTTP_OK;
        $initialData = 'Initial data';
        $newCode = Response::HTTP_CREATED;
        $newData = ['status' => 'created', 'id' => 123];
        
        $response = new WSResponse($initialCode, $initialData);
        $response->setCode($newCode)
                 ->setData($newData);
        
        $this->assertEquals($newCode, $response->getCode());
        $this->assertEquals($newData, $response->getData());
    }
    
    /**
     * Test with zero code
     */
    public function testWithZeroCode(): void
    {
        $code = 0;
        $data = 'Zero code response';
        
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
    
    /**
     * Test with negative code
     */
    public function testWithNegativeCode(): void
    {
        $code = -1;
        $data = 'Negative code response';
        
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
    
    /**
     * Test with very large code
     */
    public function testWithVeryLargeCode(): void
    {
        $code = PHP_INT_MAX;
        $data = 'Very large code response';
        
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
    
    /**
     * Test with empty string data
     */
    public function testWithEmptyStringData(): void
    {
        $code = Response::HTTP_OK;
        $data = '';
        
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
    
    /**
     * Test with empty array data
     */
    public function testWithEmptyArrayData(): void
    {
        $code = Response::HTTP_OK;
        $data = [];
        
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
}