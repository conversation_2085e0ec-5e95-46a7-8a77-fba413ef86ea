<?php

namespace App\Tests\Helper;

use App\Helper\ErrorResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class ErrorResponseTest extends TestCase
{
    public function testConstructWithStringError(): void
    {
        $errorMessage = 'This is an error message';
        $response = new ErrorResponse($errorMessage);
        
        $result = $response->toArray();
        
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertEquals($errorMessage, $result['content']['error']['message']);
        $this->assertArrayNotHasKey('errors', $result['content']['error']);
    }
    
    public function testConstructWithArrayError(): void
    {
        $errors = ['field1' => 'Error 1', 'field2' => 'Error 2'];
        $response = new ErrorResponse($errors);
        
        $result = $response->toArray();
        
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertEquals(json_encode($errors), $result['content']['error']['message']);
        $this->assertArrayNotHasKey('errors', $result['content']['error']);
    }
    
    public function testConstructWithObjectError(): void
    {
        $errorObject = new \stdClass();
        $errorObject->field1 = 'Error 1';
        $errorObject->field2 = 'Error 2';
        
        $response = new ErrorResponse($errorObject);
        
        $result = $response->toArray();
        
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertEquals(json_encode($errorObject), $result['content']['error']['message']);
        $this->assertArrayNotHasKey('errors', $result['content']['error']);
    }
    
    public function testConstructWithCustomCode(): void
    {
        $errorMessage = 'Not Found';
        $customCode = Response::HTTP_NOT_FOUND;
        $response = new ErrorResponse($errorMessage, $customCode);
        
        $result = $response->toArray();
        
        $this->assertEquals($customCode, $result['code']);
        $this->assertEquals($errorMessage, $result['content']['error']['message']);
    }
    
    public function testSetCode(): void
    {
        $response = new ErrorResponse('Error');
        $customCode = Response::HTTP_UNAUTHORIZED;
        
        $result = $response->setCode($customCode);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        
        $array = $response->toArray();
        $this->assertEquals($customCode, $array['code']);
    }
    
    public function testSetMessage(): void
    {
        $response = new ErrorResponse('Original Error');
        $newMessage = 'Updated Error Message';
        
        $result = $response->setMessage($newMessage);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        
        $array = $response->toArray();
        $this->assertEquals($newMessage, $array['content']['error']['message']);
    }
    
    public function testSetErrors(): void
    {
        $response = new ErrorResponse('Error Message');
        $errors = ['validation' => ['field1' => 'Invalid input']];
        
        $result = $response->setErrors($errors);
        
        // Test fluent interface
        $this->assertSame($response, $result);
        
        $array = $response->toArray();
        $this->assertEquals($errors, $array['content']['error']['errors']);
    }
    
    public function testToArrayWithMessageAndErrors(): void
    {
        $message = 'Validation failed';
        $errors = ['field1' => 'Required', 'field2' => 'Invalid format'];
        
        $response = new ErrorResponse($message);
        $response->setErrors($errors);
        
        $result = $response->toArray();
        
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertEquals($message, $result['content']['error']['message']);
        $this->assertEquals($errors, $result['content']['error']['errors']);
    }
    
    public function testToArrayWithoutMessage(): void
    {
        $response = new ErrorResponse('Initial message');
        $response->setMessage(null);
        $response->setErrors(['error1' => 'Error details']);
        
        $result = $response->toArray();
        
        $this->assertArrayNotHasKey('message', $result['content']['error']);
        $this->assertArrayHasKey('errors', $result['content']['error']);
    }
    
    public function testToArrayWithoutErrors(): void
    {
        $response = new ErrorResponse('Error message');
        // Not setting errors
        
        $result = $response->toArray();
        
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertArrayNotHasKey('errors', $result['content']['error']);
    }
    
    public function testCodeLessThan400(): void
    {
        // If code is less than 400, it should default to HTTP_BAD_REQUEST (400)
        $response = new ErrorResponse('Error message', 200);
        
        $result = $response->toArray();
        
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
    }
    
    public function testNullCode(): void
    {
        // If code is null, it should default to HTTP_BAD_REQUEST (400)
        $response = new ErrorResponse('Error message', null);
        
        $result = $response->toArray();
        
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
    }
    
    public function testChainedMethods(): void
    {
        $response = new ErrorResponse('Initial error');
        
        $response->setCode(Response::HTTP_FORBIDDEN)
                 ->setMessage('Access denied')
                 ->setErrors(['permission' => 'Insufficient privileges']);
        
        $result = $response->toArray();
        
        $this->assertEquals(Response::HTTP_FORBIDDEN, $result['code']);
        $this->assertEquals('Access denied', $result['content']['error']['message']);
        $this->assertEquals(['permission' => 'Insufficient privileges'], $result['content']['error']['errors']);
    }
    
    public function testEmptyErrorMessage(): void
    {
        $response = new ErrorResponse('');
        
        $result = $response->toArray();
        
        $this->assertEquals('', $result['content']['error']['message']);
    }
    
    public function testComplexNestedErrors(): void
    {
        $complexErrors = [
            'user' => [
                'name' => ['First name is required', 'Last name is required'],
                'email' => 'Invalid email format'
            ],
            'payment' => [
                'card' => 'Invalid card number',
                'expiry' => 'Card has expired'
            ]
        ];
        
        $response = new ErrorResponse('Validation failed');
        $response->setErrors($complexErrors);
        
        $result = $response->toArray();
        
        $this->assertEquals($complexErrors, $result['content']['error']['errors']);
    }
}