# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    spdr.client_id: "%env(OAUTH2_CLIENT_ID)%"
    spdr.client_secret: "%env(OAUTH2_CLIENT_SECRET)%"
    spdr.cert_dir: '%kernel.project_dir%/config/cert'
    spdr.cert_name: "%env(MS_SDPR_CERT_NAME)%"
    nafta.sdpr.client_id: "%env(NAFTA_CLIENT_ID)%"
    nafta.sdpr.client_secret: "%env(NAFTA_CLIENT_SECRET)%"
    nafta.sdpr.client_requestId: "%env(NAFTA_CLIENT_REQUEST_ID)%"
    nafta.sdpr.cert_name: "%env(MS_NAFTA_SDPR_CERT_NAME)%"

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $urlToken: "%env(MS_SDPR_TOKEN_URL)%"
            $url: "%env(MS_SDPR_URL)%"
            $apiKey: "%env(MS_SDPR_API_KEY)%"
            $clientId: "%spdr.client_id%"
            $secret: "%spdr.client_secret%"
            $certifDir: "%spdr.cert_dir%"
            $certName: "%spdr.cert_name%"
            $client_requestId: "%env(CLIENT_REQUEST_ID)%"
            $naftaClientId: "%nafta.sdpr.client_id%"
            $naftaSecret: "%nafta.sdpr.client_secret%"
            $naftaClient_requestId: "%nafta.sdpr.client_requestId%"
            $naftaApiKey: "%env(MS_NAFTA_SDPR_API_KEY)%"
            $naftaUrl: "%env(MS_NAFTA_SDPR_URL)%"
            $naftaUrlToken: "%env(MS_NAFTA_SDPR_TOKEN_URL)%"
            $naftaCertName: "%nafta.sdpr.cert_name%"

    # makes classes in src/ available to be used to services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    Symfony\Component\Cache\Adapter\TagAwareAdapter:
        arguments: ['@cache.app', '@cache.app']