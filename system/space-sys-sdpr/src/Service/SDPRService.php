<?php

namespace App\Service;

use App\Connector\SDPRApiConnector;
use App\Connector\SDPRTokenConnector;
use App\Helper\FileHelper;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Cache\ItemInterface;
use App\Connector\NaftaSDPRApiConnector;
use App\Connector\NaftaSDPRTokenConnector;

class SDPRService
{
    use LoggerTrait;
    private string $clientId;
    private string $secret;
    private string $client_requestId;
    private $certifDir;
    private $certName;
    private string $naftaCertName;
    private string $naftaClientId;
    private string $naftaSecret;
    private string $naftaClient_requestId;
    private SDPRApiConnector $sdprConnector;
    private SDPRTokenConnector $tokenConnector;
    private TagAwareCacheAdapter $tagAwareCacheAdapter;
    private FileHelper $fileHelper;
    private NaftaSDPRApiConnector $naftaSdprConnector;
    private NaftaSDPRTokenConnector $naftaTokenConnector;

    public function __construct(
        string $clientId,
        string $secret,
        string $client_requestId,
        string $certifDir,
        string $certName,
        string $naftaCertName,
        string $naftaClientId,
        string $naftaSecret,
        string $naftaClient_requestId,
        SDPRTokenConnector $tokenConnector,
        SDPRApiConnector $sdprConnector,
        NaftaSDPRApiConnector $naftaSdprConnector,
        NaftaSDPRTokenConnector $naftaTokenConnector,
        FileHelper $fileHelper,
        TagAwareCacheAdapter $tagAwareCacheAdapter
    ) {
        $this->clientId = $clientId;
        $this->secret = $secret;
        $this->client_requestId = $client_requestId;
        $this->certifDir = $certifDir;
        $this->certName = $certName;
        $this->naftaCertName = $naftaCertName;
        $this->fileHelper = $fileHelper;
        $this->tokenConnector = $tokenConnector;
        $this->sdprConnector = $sdprConnector;
        $this->naftaSdprConnector = $naftaSdprConnector;
        $this->naftaTokenConnector = $naftaTokenConnector;
        $this->tagAwareCacheAdapter = $tagAwareCacheAdapter;
        $this->naftaClientId = $naftaClientId;
        $this->naftaSecret = $naftaSecret;
        $this->naftaClient_requestId = $naftaClient_requestId;
    }

    public function getSdprToken($userId): mixed
    {
        return $this->tagAwareCacheAdapter->get("SDPR-TOKEN_".$userId, function (ItemInterface $item) {
            $options = [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ],
                'body' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->secret
                ],
                'local_cert' => $this->getCertifFile($this->certName, '.pem'),
            ];

        
            $response = $this->tokenConnector->call(Request::METHOD_POST, "/token", $options);

            if (Response::HTTP_OK == $response->getCode()) {
                $responseData = $response->getData();
                if (isset($responseData['access_token'])) {
                    $sdprToken = $responseData['access_token'];
                    $cacheTtl = (int) $responseData['expires_in'] - 30;
                    $item->expiresAfter($cacheTtl);
                }
            }
            return $sdprToken;
        });
    }

    public function getNaftaSdprToken($userId): mixed
    {
        return $this->tagAwareCacheAdapter->get("NAFTA-SDPR-TOKEN_".$userId, function (ItemInterface $item) {
            $options = [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ],
                'body' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->naftaClientId,
                    'client_secret' => $this->naftaSecret
                ],
                'local_cert' => $this->getCertifFile($this->naftaCertName, '.pem'),
            ];
        
            $response = $this->naftaTokenConnector->call(Request::METHOD_POST, "/token", $options);

            if (Response::HTTP_OK == $response->getCode()) {
                $responseData = $response->getData();
                if (isset($responseData['access_token'])) {
                    $sdprToken = $responseData['access_token'];
                    $cacheTtl = (int) $responseData['expires_in'] - 30;
                    $item->expiresAfter($cacheTtl);
                }
            }
            return $sdprToken;
        });
    }

    public function getUserVehicles($userId): WSResponse
    {
  
        try {
            $query = [
                'stage' => 'ALL',
                'sdp' => 'ALL',
            ];
            $headers = [
                'Authorization' => 'Bearer ' . $this->getSdprToken($userId),
                'x-originator-type' => 'server',
                'content-type' => 'application/json',
                'clientrequestid' => $this->client_requestId
            ];

            $wsResponse = $this->sdprConnector->call(Request::METHOD_GET, $userId . '/vehicles',
                [
                    'headers' => $headers,
                    'query' => $query,
                    'local_cert' => $this->getCertifFile($this->certName, '.pem'),
                ]
            );


            if (Response::HTTP_OK == $wsResponse->getCode()) {
                $response = $wsResponse->getData();
                if (!empty($response['vehicles'])) {
                    return new WSResponse($wsResponse->getCode(), ['vehicles' => $response['vehicles']]);
                }
                return new WSResponse(Response::HTTP_UNAUTHORIZED, $wsResponse->getData());
            }

            return new WSResponse($wsResponse->getCode(), $wsResponse->getData());

        } catch (\Exception $e) {
            $this->logger->error('Error SDPR Get Vehicles Exception ' . $e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function getNaftaUserVehicles($userId): WSResponse
    {
  
        try {
            $query = [
                'stage' => 'ALL',
                'sdp' => 'ALL',
            ];
            $headers = [
                'Authorization' => 'Bearer ' . $this->getNaftaSdprToken($userId),
                'x-originator-type' => 'server',
                'content-type' => 'application/json',
                'clientrequestid' => $this->naftaClient_requestId
            ];

            $wsResponse = $this->naftaSdprConnector->call(Request::METHOD_GET, $userId . '/vehicles',
                [
                    'headers' => $headers,
                    'query' => $query,
                    'local_cert' => $this->getCertifFile($this->naftaCertName, '.pem'),
                ]
            );


            if (Response::HTTP_OK == $wsResponse->getCode()) {
                $response = $wsResponse->getData();
                if (!empty($response['vehicles'])) {
                    return new WSResponse($wsResponse->getCode(), ['vehicles' => $response['vehicles']]);
                }
                return new WSResponse(Response::HTTP_UNAUTHORIZED, $wsResponse->getData());
            }

            return new WSResponse($wsResponse->getCode(), $wsResponse->getData());

        } catch (\Exception $e) {
            $this->logger->error('Error SDPR Get Vehicles Exception ' . $e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    private function getCertifFile(string $certifName, string $extension): string
    {
        return $this->certifDir . '/' . $this->fileHelper->setExtension($certifName, $extension);
    }
}
