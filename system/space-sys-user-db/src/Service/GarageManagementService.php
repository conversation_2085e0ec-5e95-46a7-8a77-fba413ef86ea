<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Component\Serializer\SerializerInterface;

use App\Helper\FileHelper;
use App\Helper\BrandProvider;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Model\AddVehicleModel;
use App\Model\UpdateVehicleModel;
use App\Connector\UserDataConnector;

class GarageManagementService
{
    use LoggerTrait;

    private string $authHttpLogin;
    private string $authHttpPassword;
    private string $certDir;
    private string $certName;
    private array $basicAuth;
    private UserDataConnector $userDataConnector;
    private TagAwareCacheAdapter $tagAwareCacheAdapter;
    private FileHelper $fileHelper;
    private SerializerInterface $serializer;

    public function __construct(
        string $authHttpLogin,
        string $authHttpPassword,
        string $certDir,
        string $certName,
        UserDataConnector $userDataConnector,
        TagAwareCacheAdapter $tagAwareCacheAdapter,
        FileHelper $fileHelper,
        SerializerInterface $serializer
    ) {
        $this->authHttpLogin = $authHttpLogin;
        $this->authHttpPassword = $authHttpPassword;
        $this->certDir = $certDir;
        $this->certName = $certName;
        $this->userDataConnector = $userDataConnector;
        $this->tagAwareCacheAdapter = $tagAwareCacheAdapter;
        $this->fileHelper = $fileHelper;
        $this->serializer = $serializer;
        $this->basicAuth = [$this->authHttpLogin, $this->authHttpPassword];
    }

    private function getCertifFile(string $certName, string $extension): string
    {
        $certPath = $this->certDir . '/' . $this->fileHelper->setExtension($certName, $extension);
        if (!file_exists($certPath)) {
            throw new \RuntimeException(sprintf('Certificate file not found: %s', $certPath));
        }
        return $certPath;
    }

    public function addVehicle(string $customerId, AddVehicleModel $data)
    {
        $this->logger->info('Starting vehicle addition', [
            'customerId' => $customerId,
            'vin' => $data->getVin(),
            'brand' => $data->getBrand(),
            'country' => $data->getCountry()
        ]);
        try {
            $requestBody = json_decode($this->serializer->serialize($data, 'json', [
                'groups' => ['vehicle:write'],
                'skip_null_values' => true
            ]), true);
            $requestBody['brandCode'] = BrandProvider::getBrandId($requestBody['brand']);
            $options = [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'verify_peer' => false,
                'local_cert' => $this->getCertifFile($this->certName, '.cer'),
                'local_pk' => $this->getCertifFile($this->certName, '.key'),
                'auth_basic' => $this->basicAuth,
                'json' => $requestBody
            ];
            $uri = 'api/v1/customer/' . $customerId . '/garage';

            $wsResponse = $this->userDataConnector->call(Request::METHOD_POST, $uri, $options);
            return $wsResponse;
        }
        catch (\Exception $e) {
            $this->logger->error('Error occurred during vehicle addition', [
                'customerId' => $customerId,
                'vin' => $data->getVin(),
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function getAllVehicles(string $customerId): WSResponse
    {
        $this->logger->info('Starting to fetch all vehicles', [
            'customerId' => $customerId
        ]);
        try {
            $options =  [
                'headers' => [
                    'Content-Type' => 'application/json'
                ],
                'verify_peer' => false,
                'local_cert' => $this->getCertifFile($this->certName, '.cer'),
                'local_pk' => $this->getCertifFile($this->certName, '.key'),
                'auth_basic' => $this->basicAuth,
            ];
            $uri = 'api/v1/customer/' . $customerId . '/garage';

            $wsResponse = $this->userDataConnector->call(Request::METHOD_GET, $uri, $options);
            return $wsResponse;
        }
        catch (\Exception $e) {
            $this->logger->error('Error occurred while fetching vehicles', [
                'customerId' => $customerId,
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function updateVehicle(string $customerId, UpdateVehicleModel $data): WSResponse
    {
        $this->logger->info('Starting vehicle update', [
            'customerId' => $customerId,
            'vin' => $data->getVin(),
            'brand' => $data->getBrand(),
            'country' => $data->getCountry()
        ]);
        try {
            $requestBody = json_decode($this->serializer->serialize($data, 'json', [
                'groups' => ['vehicle:write'],
                'skip_null_values' => true
            ]), true);
            $requestBody['brandCode'] = BrandProvider::getBrandId($requestBody['brand']);
            $options = [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'verify_peer' => false,
                'local_cert' => $this->getCertifFile($this->certName, '.cer'),
                'local_pk' => $this->getCertifFile($this->certName, '.key'),
                'auth_basic' => $this->basicAuth,
                'json' => $requestBody
            ];
            $uri = 'api/v1/customer/' . $customerId . '/garage';

            $wsResponse = $this->userDataConnector->call(Request::METHOD_PUT, $uri, $options);
            return $wsResponse;
        }
        catch (\Exception $e) {
            $this->logger->error('Error occurred during vehicle update', [
                'customerId' => $customerId,
                'vin' => $data->getVin(),
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function deleteVehicle(string $customerId, string $vin): WSResponse
    {
        $this->logger->info('Starting vehicle deletion', [
            'customerId' => $customerId,
            'vin' => $vin
        ]);
        try {
            $options =  [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'verify_peer' => false,
                'local_cert' => $this->getCertifFile($this->certName, '.cer'),
                'local_pk' => $this->getCertifFile($this->certName, '.key'),
                'auth_basic' => $this->basicAuth,
            ];
            $uri = 'api/v1/customer/' . $customerId . '/garage/' . $vin;

            $wsResponse = $this->userDataConnector->call(Request::METHOD_DELETE, $uri, $options);
            return $wsResponse;
        }
        catch (\Exception $e) {
            $this->logger->error('Error occurred during vehicle deletion', [
                'customerId' => $customerId,
                'vin' => $vin,
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }


}