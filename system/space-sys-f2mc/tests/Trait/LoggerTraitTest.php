<?php

namespace App\Tests\Trait;

use App\Trait\LoggerTrait;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\Service\Attribute\Required;
use ReflectionClass;
use ReflectionProperty;

class LoggerTraitTest extends TestCase
{
    /**
     * Test class that uses the LoggerTrait
     */
    private $testClass;

    protected function setUp(): void
    {
        // Create an anonymous class that uses the LoggerTrait
        $this->testClass = new class {
            use LoggerTrait;
        };
    }

    protected function tearDown(): void
    {
        unset($this->testClass);
    }

    /**
     * Test that the logger property is initially null
     */
    public function testLoggerInitiallyNull(): void
    {
        // Use reflection to access the private property
        $reflection = new ReflectionClass($this->testClass);
        $property = $reflection->getProperty('logger');
        $property->setAccessible(true);
        
        $this->assertNull($property->getValue($this->testClass));
    }

    /**
     * Test that setLogger method sets the logger property
     */
    public function testSetLogger(): void
    {
        // Create a mock logger
        $mockLogger = $this->createMock(LoggerInterface::class);
        
        // Call the setLogger method
        $this->testClass->setLogger($mockLogger);
        
        // Use reflection to access the private property
        $reflection = new ReflectionClass($this->testClass);
        $property = $reflection->getProperty('logger');
        $property->setAccessible(true);
        
        // Verify the logger was set
        $this->assertSame($mockLogger, $property->getValue($this->testClass));
    }

    /**
     * Test that the Required attribute is present on the setLogger method
     */
    public function testRequiredAttributePresent(): void
    {
        $reflection = new ReflectionClass($this->testClass);
        $method = $reflection->getMethod('setLogger');
        $attributes = $method->getAttributes(Required::class);
        
        $this->assertCount(1, $attributes, 'The Required attribute should be present on the setLogger method');
    }

    /**
     * Test that the logger property has the correct type
     */
    public function testLoggerPropertyType(): void
    {
        $reflection = new ReflectionClass($this->testClass);
        $property = $reflection->getProperty('logger');
        
        // Get the property type
        $type = $property->getType();
        
        $this->assertTrue($type->allowsNull());
        $this->assertEquals('Psr\Log\LoggerInterface', $type->getName());
    }

    /**
     * Test setting multiple loggers (should replace the previous one)
     */
    public function testSettingMultipleLoggers(): void
    {
        // Create two mock loggers
        $mockLogger1 = $this->createMock(LoggerInterface::class);
        $mockLogger2 = $this->createMock(LoggerInterface::class);
        
        // Set the first logger
        $this->testClass->setLogger($mockLogger1);
        
        // Use reflection to access the private property
        $reflection = new ReflectionClass($this->testClass);
        $property = $reflection->getProperty('logger');
        $property->setAccessible(true);
        
        // Verify the first logger was set
        $this->assertSame($mockLogger1, $property->getValue($this->testClass));
        
        // Set the second logger
        $this->testClass->setLogger($mockLogger2);
        
        // Verify the second logger replaced the first one
        $this->assertSame($mockLogger2, $property->getValue($this->testClass));
    }
}