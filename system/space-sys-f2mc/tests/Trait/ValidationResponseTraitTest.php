<?php

namespace App\Tests\Trait;

use App\Helper\ErrorResponse;
use App\Trait\ValidationResponseTrait;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;

class ValidationResponseTraitTest extends TestCase
{
    /**
     * Test class that uses the ValidationResponseTrait
     */
    private $testClass;

    protected function setUp(): void
    {
        // Create an anonymous class that uses the ValidationResponseTrait
        $this->testClass = new class {
            use ValidationResponseTrait;

            // Make protected methods public for testing
            public function getValidationMessagesPublic($errors)
            {
                return self::getValidationMessages($errors);
            }

            public function getValidationErrorResponsePublic($messages)
            {
                return self::getValidationErrorResponse($messages);
            }
        };
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->testClass);
    }

    /**
     * Test getValidationMessages with empty errors list
     */
    public function testGetValidationMessagesWithEmptyErrors(): void
    {
        $emptyViolations = new ConstraintViolationList();

        $result = $this->testClass->getValidationMessagesPublic($emptyViolations);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Test getValidationMessages with single error
     */
    public function testGetValidationMessagesWithSingleError(): void
    {
        $violation = new ConstraintViolation(
            'Email is not valid',
            null,
            [],
            null,
            'email',
            null
        );

        $violations = new ConstraintViolationList([$violation]);

        $result = $this->testClass->getValidationMessagesPublic($violations);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertArrayHasKey('email', $result);
        $this->assertEquals('Email is not valid', $result['email']);
    }

    /**
     * Test getValidationMessages with multiple errors
     */
    public function testGetValidationMessagesWithMultipleErrors(): void
    {
        $emailViolation = new ConstraintViolation(
            'Email is not valid',
            null,
            [],
            null,
            'email',
            null
        );

        $nameViolation = new ConstraintViolation(
            'Name cannot be empty',
            null,
            [],
            null,
            'name',
            null
        );

        $violations = new ConstraintViolationList([$emailViolation, $nameViolation]);

        $result = $this->testClass->getValidationMessagesPublic($violations);

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertArrayHasKey('email', $result);
        $this->assertArrayHasKey('name', $result);
        $this->assertEquals('Email is not valid', $result['email']);
        $this->assertEquals('Name cannot be empty', $result['name']);
    }

    /**
     * Test getValidationMessages with nested property paths
     */
    public function testGetValidationMessagesWithNestedPropertyPaths(): void
    {
        $violation = new ConstraintViolation(
            'City is required',
            null,
            [],
            null,
            'address[city]',
            null
        );

        $violations = new ConstraintViolationList([$violation]);

        $result = $this->testClass->getValidationMessagesPublic($violations);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        // The ValidationResponseTrait removes '[' and ']' from the property path
        $this->assertArrayHasKey('addresscity', $result);
        $this->assertEquals('City is required', $result['addresscity']);
    }

    /**
     * Test getValidationErrorResponse
     */
    public function testGetValidationErrorResponse(): void
    {
        $messages = [
            'email' => 'Email is not valid',
            'name' => 'Name cannot be empty'
        ];

        $result = $this->testClass->getValidationErrorResponsePublic($messages);

        $this->assertInstanceOf(ErrorResponse::class, $result);

        // Use toArray() to access the response data since ErrorResponse doesn't have getter methods
        $responseArray = $result->toArray();
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $responseArray['code']);
        $this->assertEquals('validation_failed', $responseArray['content']['error']['message']);
        $this->assertEquals($messages, $responseArray['content']['error']['errors']);
    }

    /**
     * Test getValidationErrorResponse with empty messages
     */
    public function testGetValidationErrorResponseWithEmptyMessages(): void
    {
        $messages = [];

        $result = $this->testClass->getValidationErrorResponsePublic($messages);

        $this->assertInstanceOf(ErrorResponse::class, $result);

        // Use toArray() to access the response data since ErrorResponse doesn't have getter methods
        $responseArray = $result->toArray();
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $responseArray['code']);
        $this->assertEquals('validation_failed', $responseArray['content']['error']['message']);

        // When errors array is empty, the 'errors' key might not be present in the response
        // Check if the key exists, and if it does, verify it's an empty array
        if (isset($responseArray['content']['error']['errors'])) {
            $this->assertEquals($messages, $responseArray['content']['error']['errors']);
        } else {
            // If 'errors' key is not present, that's also acceptable for an empty array
            $this->assertEmpty($messages);
        }
    }

    /**
     * Test full validation flow from violations to error response
     */
    public function testFullValidationFlow(): void
    {
        // Create violations
        $emailViolation = new ConstraintViolation(
            'Email is not valid',
            null,
            [],
            null,
            'email',
            null
        );

        $nameViolation = new ConstraintViolation(
            'Name cannot be empty',
            null,
            [],
            null,
            'name',
            null
        );

        $violations = new ConstraintViolationList([$emailViolation, $nameViolation]);

        // Get validation messages
        $messages = $this->testClass->getValidationMessagesPublic($violations);

        // Get error response
        $response = $this->testClass->getValidationErrorResponsePublic($messages);

        // Verify the flow
        $this->assertInstanceOf(ErrorResponse::class, $response);

        // Use toArray() to access the response data since ErrorResponse doesn't have getter methods
        $responseArray = $response->toArray();
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $responseArray['code']);
        $this->assertEquals('validation_failed', $responseArray['content']['error']['message']);
        $this->assertCount(2, $responseArray['content']['error']['errors']);
        $this->assertEquals('Email is not valid', $responseArray['content']['error']['errors']['email']);
        $this->assertEquals('Name cannot be empty', $responseArray['content']['error']['errors']['name']);
    }
}