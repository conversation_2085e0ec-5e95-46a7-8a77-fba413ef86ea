<?php

namespace App\Tests\Manager;

use App\DataMapper\UserDataMapper;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\UserManager;
use App\Model\UserDataModel;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class UserManagerTest extends TestCase
{
    private $userService;
    private $userManager;
    private $serializer;
    private $userDataMapper;
    private $logger;
    private $userDataModel;

    protected function setUp(): void
    {
        $this->userService = $this->createMock(UserService::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->userDataMapper = $this->createMock(UserDataMapper::class);
        $this->userDataModel = $this->createMock(UserDataModel::class);

        $this->userManager = new UserManager(
            $this->userService,
            $this->serializer,
            $this->userDataMapper
        );

        $this->logger = $this->createMock(LoggerInterface::class);
        // Logger methods return void, so we don't need to set a return value
        $this->logger->method('info');
        $this->logger->method('error');

        $this->userManager->setLogger($this->logger);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        
        unset($this->userManager);
        unset($this->userService);
        unset($this->serializer);
        unset($this->userDataMapper);
        unset($this->logger);
        unset($this->userDataModel);
    }

    // =============== REGISTER ===============

    public function testRegisterSuccessfulResponse()
    {
        $params = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => 12345,
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'vin' => 'ABC123456789DEF'
        ];

        $responseData = [
            'data' => [
                'accessToken' => 'access-token-123',
                'expiresIn' => 3600,
                'idToken' => 'id-token-123',
                'refreshToken' => 'refresh-token-123',
                'tokenType' => 'Bearer',
                'profile' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>'
                ]
            ]
        ];

        $mappedData = [
            'accessToken' => 'access-token-123',
            'expiresIn' => 3600,
            'idToken' => 'id-token-123',
            'refreshToken' => 'refresh-token-123',
            'tokenType' => 'Bearer',
            'profile' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'email' => '<EMAIL>'
            ]
        ];

        $this->userService->expects($this->once())
            ->method('register')
            ->with($params)
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $this->userDataMapper->expects($this->once())
            ->method('map')
            ->with($responseData['data'])
            ->willReturn($this->userDataModel);

        $this->userDataModel->expects($this->once())
            ->method('toArray')
            ->willReturn($mappedData);

        $result = $this->userManager->register($params);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($mappedData, $result->getData());
    }

    public function testRegisterErrorResponse()
    {
        $params = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>'
        ];

        $errorResponse = [
            'error' => [
                'message' => 'Validation failed'
            ]
        ];

        $this->userService->expects($this->once())
            ->method('register')
            ->with($params)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponse));

        $result = $this->userManager->register($params);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Validation failed', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testRegisterExceptionHandling()
    {
        $params = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>'
        ];

        $this->userService->expects($this->once())
            ->method('register')
            ->with($params)
            ->willThrowException(new \Exception('Service unavailable', 503));

        $result = $this->userManager->register($params);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service unavailable', $result->toArray()['content']['error']['message']);
        $this->assertEquals(503, $result->toArray()['code']);
    }

    // =============== REFRESH TOKEN ===============

    public function testRefreshTokenSuccessfulResponse()
    {
        $params = [
            'accessToken' => 'old-access-token',
            'refreshToken' => 'old-refresh-token'
        ];

        $responseData = [
            'accessToken' => 'new-access-token',
            'expiresIn' => 3600,
            'idToken' => 'new-id-token',
            'refreshToken' => 'new-refresh-token',
            'tokenType' => 'Bearer'
        ];

        $this->userService->expects($this->once())
            ->method('refreshToken')
            ->with($params)
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->userManager->refreshToken($params);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData, $result->getData());
    }

    public function testRefreshTokenErrorResponse()
    {
        $params = [
            'accessToken' => 'invalid-token',
            'refreshToken' => 'invalid-refresh-token'
        ];

        $errorResponse = [
            'message' => 'Invalid token'
        ];

        $this->userService->expects($this->once())
            ->method('refreshToken')
            ->with($params)
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponse));

        $result = $this->userManager->refreshToken($params);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Invalid token', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->toArray()['code']);
    }

    public function testRefreshTokenExceptionHandling()
    {
        $params = [
            'accessToken' => 'access-token',
            'refreshToken' => 'refresh-token'
        ];

        $this->userService->expects($this->once())
            ->method('refreshToken')
            ->with($params)
            ->willThrowException(new \Exception('Service unavailable', 503));

        $result = $this->userManager->refreshToken($params);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service unavailable', $result->toArray()['content']['error']['message']);
        $this->assertEquals(503, $result->toArray()['code']);
    }

    // =============== GET ISHOWROOM ELIGIBILITY ===============

    public function testGetIshowroomEligibilitySuccessfulResponse()
    {
        $token = 'valid-token';
        $email = '<EMAIL>';

        $responseData = [
            'data' => [
                'eligible' => true,
                'credits' => 100
            ]
        ];

        $this->userService->expects($this->once())
            ->method('getIshowroomEligibility')
            ->with($token, $email)
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->userManager->getIshowroomEligibility($token, $email);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData['data'], $result->getData());
    }

    public function testGetIshowroomEligibilityErrorResponse()
    {
        $token = 'invalid-token';
        $email = '<EMAIL>';

        $errorResponse = [
            'message' => 'Invalid token'
        ];

        $this->userService->expects($this->once())
            ->method('getIshowroomEligibility')
            ->with($token, $email)
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponse));

        $result = $this->userManager->getIshowroomEligibility($token, $email);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Invalid token', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->toArray()['code']);
    }

    public function testGetIshowroomEligibilityExceptionHandling()
    {
        $token = 'valid-token';
        $email = '<EMAIL>';

        $this->userService->expects($this->once())
            ->method('getIshowroomEligibility')
            ->with($token, $email)
            ->willThrowException(new \Exception('Service unavailable', 503));

        $result = $this->userManager->getIshowroomEligibility($token, $email);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service unavailable', $result->toArray()['content']['error']['message']);
        $this->assertEquals(503, $result->toArray()['code']);
    }

    // =============== APPLY ISHOWROOM CREDIT ===============

    public function testApplyIshowroomCreditSuccessfulResponse()
    {
        $token = 'valid-token';
        $id = 'credit-id-123';

        $responseData = [
            'data' => [
                'applied' => true,
                'amount' => 100
            ]
        ];

        $this->userService->expects($this->once())
            ->method('applyIshowroomCredit')
            ->with($token, $id)
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->userManager->applyIshowroomCredit($token, $id);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData['data'], $result->getData());
    }

    public function testApplyIshowroomCreditWithCreatedStatusResponse()
    {
        $token = 'valid-token';
        $id = 'credit-id-123';

        $responseData = [
            'data' => [
                'applied' => true,
                'amount' => 100
            ]
        ];

        $this->userService->expects($this->once())
            ->method('applyIshowroomCredit')
            ->with($token, $id)
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->userManager->applyIshowroomCredit($token, $id);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData['data'], $result->getData());
    }

    public function testApplyIshowroomCreditErrorResponse()
    {
        $token = 'invalid-token';
        $id = 'credit-id-123';

        $errorResponse = [
            'message' => 'Invalid token'
        ];

        $this->userService->expects($this->once())
            ->method('applyIshowroomCredit')
            ->with($token, $id)
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponse));

        $result = $this->userManager->applyIshowroomCredit($token, $id);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Invalid token', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->toArray()['code']);
    }

    public function testApplyIshowroomCreditExceptionHandling()
    {
        $token = 'valid-token';
        $id = 'credit-id-123';

        $this->userService->expects($this->once())
            ->method('applyIshowroomCredit')
            ->with($token, $id)
            ->willThrowException(new \Exception('Service unavailable', 503));

        $result = $this->userManager->applyIshowroomCredit($token, $id);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service unavailable', $result->toArray()['content']['error']['message']);
        $this->assertEquals(503, $result->toArray()['code']);
    }

    // =============== GET ACCOUNT LINK ===============

    public function testGetAccountLinkSuccessfulResponse()
    {
        $providerShortCode = 'provider-123';
        $token = 'valid-token';

        $responseData = [
            'data' => [
                'url' => 'https://example.com/link',
                'expiresIn' => 3600
            ]
        ];

        $this->userService->expects($this->once())
            ->method('getAccountLink')
            ->with($providerShortCode, $token)
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->userManager->getAccountLink($providerShortCode, $token);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData['data'], $result->getData());
    }

    public function testGetAccountLinkErrorResponse()
    {
        $providerShortCode = 'provider-123';
        $token = 'invalid-token';

        $errorResponse = [
            'message' => 'Invalid token'
        ];

        $this->userService->expects($this->once())
            ->method('getAccountLink')
            ->with($providerShortCode, $token)
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponse));

        $result = $this->userManager->getAccountLink($providerShortCode, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Invalid token', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->toArray()['code']);
    }

    public function testGetAccountLinkExceptionHandling()
    {
        $providerShortCode = 'provider-123';
        $token = 'valid-token';

        $this->userService->expects($this->once())
            ->method('getAccountLink')
            ->with($providerShortCode, $token)
            ->willThrowException(new \Exception('Service unavailable', 503));

        $result = $this->userManager->getAccountLink($providerShortCode, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service unavailable', $result->toArray()['content']['error']['message']);
        $this->assertEquals(503, $result->toArray()['code']);
    }
}
