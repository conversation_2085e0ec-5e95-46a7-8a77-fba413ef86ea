<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\ChargingSessionManager;
use App\Service\ChargingSessionService;
use Symfony\Component\HttpFoundation\Response;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ChargingSessionManagerTest extends TestCase
{
    private $ChargingSessionService;
    private $ChargingSessionManager;
    private $validator;

    protected function setUp(): void
    {
        $this->ChargingSessionService = $this->createMock(ChargingSessionService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->validator->expects($this->any())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));
        $this->ChargingSessionManager = new ChargingSessionManager($this->ChargingSessionService, $this->validator);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())->method('info');
        $logger->expects($this->any())->method('error');

        $this->ChargingSessionManager->setLogger($logger);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->ChargingSessionManager);
        unset($this->ChargingSessionService);
        unset($this->validator);
    }

    public function testStartSessionCreatesSuccessfulResponse()
    {
        $params = ['param1' => 'value1'];
        $accessToken = "accessToken";

        $this->ChargingSessionService->expects($this->once())
            ->method('startSession')
            ->with($params, $accessToken)
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ["data" => "idSession"]));

        $result = $this->ChargingSessionManager->startSession($params, $accessToken);


        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['data' => 'idSession'], $result->getData());
    }

    public function testStartSessionReturnsErrorResponseOnFailure()
    {
        $params = ['param1' => 'value1'];
        $accessToken = "accessToken";

        $this->ChargingSessionService->expects($this->once())
            ->method('startSession')
            ->with($params)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Error"]));

        $result = $this->ChargingSessionManager->startSession($params, $accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testStartSessionHandlesException()
    {
        $params = ['param1' => 'value1'];

        $this->ChargingSessionService->expects($this->once())
            ->method('startSession')
            ->with($params, '')
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->ChargingSessionManager->startSession($params, '');

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    // ============ CDR Data ==================

    public function testGetCdrDataCreatesSuccessfulResponse()
    {
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $accessToken = "accessToken";

        $this->ChargingSessionService->expects($this->once())
            ->method('getCdrData')
            ->with($sessionId, $accessToken)
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ["data" => "idSession"]));

        $result = $this->ChargingSessionManager->getCdrData($sessionId, $accessToken);


        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['data' => 'idSession'], $result->getData());
    }

    public function testGetCdrDataReturnsErrorResponseOnFailure()
    {
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $accessToken = "accessToken";

        $this->ChargingSessionService->expects($this->once())
            ->method('getCdrData')
            ->with($sessionId, $accessToken)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Bad Request"]));

        $result = $this->ChargingSessionManager->getCdrData($sessionId, $accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Bad Request', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testGetCdrDataHandlesException()
    {
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';

        $this->ChargingSessionService->expects($this->once())
            ->method('getCdrData')
            ->with($sessionId, '')
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->ChargingSessionManager->getCdrData($sessionId, '');

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testValidateChargingSessionDataReturnsNoViolations()
    {
        $data = [
            'locationId' => 'loc1',
            'evseId' => 'evse1',
            'coordinate' => ['lat' => 12.34, 'lng' => 56.78],
            'connectorId' => 'conn1',
            'response_url' => 'http://example.com',
            'accessToken' => 'accessToken'
        ];

        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, \PHPUnit\Framework\anything())
            ->willReturn($mockViolations);

        $violations = $this->ChargingSessionManager->validateChargingSessionData($data);

        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
    }

    public function testDeleteAccountSuccess()
    {
        $params = ['reasonId' => '123', 'otherReason' => 'Testing'];
        $token = ['valid-token'];

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn(['data' => true]);

        $this->ChargingSessionService->expects($this->once())
            ->method('accountDeletion')
            ->with($params, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->deleteAccount($params, $token);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['data' => true], $result->getData());
    }

    public function testDeleteAccountReturnsErrorResponseOnFailure()
    {
        $params = ['param1' => 'value1'];
        $token = ["accessToken"];

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);
        $mockResponse->method('getData')->willReturn(['message' => 'Bad Request']);

        $this->ChargingSessionService->expects($this->once())
            ->method('accountDeletion')
            ->with($params, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->deleteAccount($params, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Bad Request', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testDeleteAccountHandlesException()
    {
        $params = ['param1' => 'value1'];
        $token = ["accessToken"];

        $this->ChargingSessionService->expects($this->once())
            ->method('accountDeletion')
            ->with($params, $token)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->ChargingSessionManager->deleteAccount($params, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testValidateAccountDeleteDataReturnsNoViolations()
    {
        $data = [
            'reasonId' => 123,
            'otherReason' => 'Testing',
        ];

        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, \PHPUnit\Framework\anything())
            ->willReturn($mockViolations);

        $violations = $this->ChargingSessionManager->validateAccountDeleteData($data);

        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
    }

    public function testgetWalletDetailSuccess()
    {
        $token = ['valid-token'];
        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn(['success' => ['totalCredits' => 0]]);
        $this->ChargingSessionService->expects($this->once())
            ->method('walletDetails')
            ->with($token)
            ->willReturn($mockResponse);
        $result = $this->ChargingSessionManager->getWalletDetail($token);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['success' => ['totalCredits' => 0]], $result->getData());
    }

    public function testgetWalletDetailReturnsErrorResponseOnFailure()
    {
        $token = ["accessToken"];
        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);
        $mockResponse->method('getData')->willReturn("bad request");
        $this->ChargingSessionService->expects($this->once())
            ->method('walletDetails')
            ->with($token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->getWalletDetail($token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('bad request', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testgetWalletDetailHandlesException()
    {
        $token = ["accessToken"];
        $this->ChargingSessionService->expects($this->once())
            ->method('walletDetails')
            ->with($token)
            ->willThrowException(new \Exception('Service error', 500));
        $result = $this->ChargingSessionManager->getWalletDetail($token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testwalletDataValidationReturnsNoViolations()
    {
        $data = [
            'token' => 'validToken'
        ];
        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, \PHPUnit\Framework\anything())
            ->willReturn($mockViolations);
        $violations = $this->ChargingSessionManager->walletDataValidation($data);
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
    }

    // =============== STOP SESSION TESTS ===============

    public function testStopSessionCreatesSuccessfulResponse()
    {
        $params = ['sessionId' => 'session-123', 'reason' => 'USER_REQUESTED'];
        $token = 'accessToken';

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_CREATED);
        $mockResponse->method('getData')->willReturn([
            'id' => 'session-123',
            'status' => 'COMPLETED',
            'stopTime' => '2023-06-01T12:30:00Z'
        ]);

        $this->ChargingSessionService->expects($this->once())
            ->method('stopSession')
            ->with($params, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->stopSession($params, $token);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals([
            'id' => 'session-123',
            'status' => 'COMPLETED',
            'stopTime' => '2023-06-01T12:30:00Z'
        ], $result->getData());
    }

    public function testStopSessionReturnsErrorResponseOnFailure()
    {
        $params = ['sessionId' => 'invalid-session', 'reason' => 'USER_REQUESTED'];
        $token = 'accessToken';

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);
        $mockResponse->method('getData')->willReturn(['message' => 'Invalid session ID']);

        $this->ChargingSessionService->expects($this->once())
            ->method('stopSession')
            ->with($params, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->stopSession($params, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Invalid session ID', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testStopSessionHandlesException()
    {
        $params = ['sessionId' => 'session-123', 'reason' => 'USER_REQUESTED'];
        $token = 'accessToken';

        $this->ChargingSessionService->expects($this->once())
            ->method('stopSession')
            ->with($params, $token)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->ChargingSessionManager->stopSession($params, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testValidateSessionStopDataReturnsNoViolations()
    {
        $data = [
            'sessionId' => 'session-123',
            'response_url' => 'http://example.com/callback',
            'token' => 'validToken'
        ];

        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, \PHPUnit\Framework\anything())
            ->willReturn($mockViolations);

        $violations = $this->ChargingSessionManager->validateSessionStopData($data);

        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
    }

    // =============== GET SESSION DETAILS VALIDATION TESTS ===============

    public function testGetSessionDetailsValidationReturnsNoViolations()
    {
        $data = [
            'token' => 'validToken'
        ];

        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, \PHPUnit\Framework\anything())
            ->willReturn($mockViolations);

        $violations = $this->ChargingSessionManager->getSessionDetailsValidation($data);

        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
    }

    // =============== GET HISTORY TESTS ===============

    public function testGetHistoryCreatesSuccessfulResponse()
    {
        $params = [
            'fromDate' => '2023-01-01',
            'toDate' => '2023-06-30',
            'limit' => 10,
            'offset' => 0
        ];
        $token = 'accessToken';

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn([
            'data' => [
                [
                    'id' => 'session-123',
                    'startTime' => '2023-06-01T10:00:00Z',
                    'stopTime' => '2023-06-01T12:30:00Z',
                    'status' => 'COMPLETED',
                    'location' => 'Charging Station 1'
                ],
                [
                    'id' => 'session-456',
                    'startTime' => '2023-06-02T14:00:00Z',
                    'stopTime' => '2023-06-02T16:45:00Z',
                    'status' => 'COMPLETED',
                    'location' => 'Charging Station 2'
                ]
            ]
        ]);

        $this->ChargingSessionService->expects($this->once())
            ->method('getHistory')
            ->with($params, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->getHistory($params, $token);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals([
            [
                'id' => 'session-123',
                'startTime' => '2023-06-01T10:00:00Z',
                'stopTime' => '2023-06-01T12:30:00Z',
                'status' => 'COMPLETED',
                'location' => 'Charging Station 1'
            ],
            [
                'id' => 'session-456',
                'startTime' => '2023-06-02T14:00:00Z',
                'stopTime' => '2023-06-02T16:45:00Z',
                'status' => 'COMPLETED',
                'location' => 'Charging Station 2'
            ]
        ], $result->getData());
    }

    public function testGetHistoryReturnsEmptyArrayWhenNoData()
    {
        $params = [
            'fromDate' => '2023-01-01',
            'toDate' => '2023-06-30'
        ];
        $token = 'accessToken';

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn([]);

        $this->ChargingSessionService->expects($this->once())
            ->method('getHistory')
            ->with($params, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->getHistory($params, $token);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals([], $result->getData());
    }

    public function testGetHistoryReturnsErrorResponseOnFailure()
    {
        $params = [
            'fromDate' => '2023-01-01',
            'toDate' => '2023-06-30'
        ];
        $token = 'accessToken';

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);
        $mockResponse->method('getData')->willReturn(['message' => 'Invalid date format']);

        $this->ChargingSessionService->expects($this->once())
            ->method('getHistory')
            ->with($params, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->getHistory($params, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Invalid date format', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testGetHistoryHandlesException()
    {
        $params = [
            'fromDate' => '2023-01-01',
            'toDate' => '2023-06-30'
        ];
        $token = 'accessToken';

        $this->ChargingSessionService->expects($this->once())
            ->method('getHistory')
            ->with($params, $token)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->ChargingSessionManager->getHistory($params, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    // =============== CHARGE SESSION DATA TESTS ===============

    public function testChargeSessionDataCreatesSuccessfulResponse()
    {
        $sessionId = 'session-123';
        $token = 'accessToken';

        $responseData = [
            'id' => 'session-123',
            'startTime' => '2023-06-01T10:00:00Z',
            'status' => 'IN_PROGRESS',
            'location' => 'Charging Station 1',
            'evseId' => 'evse-456',
            'connectorId' => 'conn-789',
            'meterValues' => [
                [
                    'timestamp' => '2023-06-01T10:15:00Z',
                    'value' => 5.2
                ],
                [
                    'timestamp' => '2023-06-01T10:30:00Z',
                    'value' => 10.8
                ]
            ]
        ];

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn($responseData);

        $this->ChargingSessionService->expects($this->once())
            ->method('chargeSessionData')
            ->with($sessionId, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->chargeSessionData($sessionId, $token);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData, $result->getData());
    }

    public function testChargeSessionDataReturnsErrorResponseOnFailure()
    {
        $sessionId = 'invalid-session';
        $token = 'accessToken';

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_NOT_FOUND);
        $mockResponse->method('getData')->willReturn(['message' => 'Session not found']);

        $this->ChargingSessionService->expects($this->once())
            ->method('chargeSessionData')
            ->with($sessionId, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->chargeSessionData($sessionId, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Session not found', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->toArray()['code']);
    }

    public function testChargeSessionDataHandlesException()
    {
        $sessionId = 'session-123';
        $token = 'accessToken';

        $this->ChargingSessionService->expects($this->once())
            ->method('chargeSessionData')
            ->with($sessionId, $token)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->ChargingSessionManager->chargeSessionData($sessionId, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }
}
