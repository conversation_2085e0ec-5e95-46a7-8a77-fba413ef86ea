<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\ChargingLocationManager;
use App\Service\ChargingLocationService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ChargingLocationManagerTest extends TestCase
{
    private $chargingLocationService;
    private $chargingLocationManager;
    private $validator;
    private $logger;

    protected function setUp(): void
    {
        $this->chargingLocationService = $this->createMock(ChargingLocationService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);

        $this->chargingLocationManager = new ChargingLocationManager(
            $this->chargingLocationService,
            $this->validator
        );

        $this->logger = $this->createMock(LoggerInterface::class);
        // Logger methods return void, so we don't need to set a return value
        $this->logger->method('info');
        $this->logger->method('error');

        $this->chargingLocationManager->setLogger($this->logger);
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        unset($this->chargingLocationManager);
        unset($this->chargingLocationService);
        unset($this->validator);
        unset($this->logger);
    }

    // =============== GET LOCATIONS ===============

    /**
     * Test getLocations method with successful response
     */
    public function testGetLocationsSuccessfulResponse()
    {
        // Test data
        $params = [
            'filters' => [
                [
                    'field' => 'country',
                    'operator' => 'eq',
                    'value' => 'US'
                ],
                [
                    'field' => 'city',
                    'operator' => 'eq',
                    'value' => 'New York'
                ]
            ]
        ];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2023-01-01 00:00:00';
        $toDate = '2023-12-31 23:59:59';

        // Expected response data
        $responseData = [
            'data' => [
                [
                    'id' => 'loc1',
                    'name' => 'Charging Station 1',
                    'address' => '123 Main St',
                    'city' => 'New York',
                    'country' => 'US',
                    'coordinates' => [
                        'latitude' => 40.7128,
                        'longitude' => -74.0060
                    ]
                ],
                [
                    'id' => 'loc2',
                    'name' => 'Charging Station 2',
                    'address' => '456 Broadway',
                    'city' => 'New York',
                    'country' => 'US',
                    'coordinates' => [
                        'latitude' => 40.7589,
                        'longitude' => -73.9851
                    ]
                ]
            ]
        ];

        // Mock the service to return a successful response
        $this->chargingLocationService->expects($this->once())
            ->method('getLocations')
            ->with(
                $params['filters'],
                $sortBy,
                $orderBy,
                $offset,
                $limit,
                $fromDate,
                $toDate
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        // Call the method
        $result = $this->chargingLocationManager->getLocations(
            $params,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData['data'], $result->getData());
    }

    /**
     * Test getLocations method with error response
     */
    public function testGetLocationsErrorResponse()
    {
        // Test data
        $params = [
            'filters' => [
                [
                    'field' => 'country',
                    'operator' => 'eq',
                    'value' => 'US'
                ]
            ]
        ];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2023-01-01 00:00:00';
        $toDate = '2023-12-31 23:59:59';

        // Error response data
        $errorResponseData = [
            'message' => 'Invalid request parameters'
        ];

        // Mock the service to return an error response
        $this->chargingLocationService->expects($this->once())
            ->method('getLocations')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        // Call the method
        $result = $this->chargingLocationManager->getLocations(
            $params,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals($errorResponseData, $result->toArray()['content']['error']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    /**
     * Test getLocations method with exception
     */
    public function testGetLocationsExceptionHandling()
    {
        // Test data
        $params = [
            'filters' => [
                [
                    'field' => 'country',
                    'operator' => 'eq',
                    'value' => 'US'
                ]
            ]
        ];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2023-01-01 00:00:00';
        $toDate = '2023-12-31 23:59:59';

        // Create a custom exception with a simple string message
        $exception = new \Exception('Service unavailable', 503);

        // Mock the service to throw the exception
        $this->chargingLocationService->expects($this->once())
            ->method('getLocations')
            ->willThrowException($exception);

        // Call the method
        $result = $this->chargingLocationManager->getLocations(
            $params,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result is an ErrorResponse
        $this->assertInstanceOf(ErrorResponse::class, $result);

        // Get the result array
        $resultArray = $result->toArray();

        // Assert the error code
        $this->assertEquals(503, $resultArray['code']);

        // Assert the error content structure
        $this->assertArrayHasKey('content', $resultArray);
        $this->assertArrayHasKey('error', $resultArray['content']);
    }

    /**
     * Test getLocations method with null parameters
     */
    public function testGetLocationsWithNullParameters()
    {
        // Test data with null parameters
        $params = [];
        $sortBy = null;
        $orderBy = null;
        $offset = null;
        $limit = null;
        $fromDate = null;
        $toDate = null;

        // Expected response data
        $responseData = [
            'data' => [
                [
                    'id' => 'loc1',
                    'name' => 'Charging Station 1',
                    'country' => 'US'
                ]
            ]
        ];

        // Mock the service to return a successful response
        $this->chargingLocationService->expects($this->once())
            ->method('getLocations')
            ->with(
                [],
                $sortBy,
                $orderBy,
                $offset,
                $limit,
                $fromDate,
                $toDate
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        // Call the method
        $result = $this->chargingLocationManager->getLocations(
            $params,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData['data'], $result->getData());
    }

    // =============== VALIDATE CHARGING LOCATION DATA ===============

    /**
     * Test validateChargingLocationData method with valid data
     */
    public function testValidateChargingLocationDataWithValidData()
    {
        // Valid data
        $data = [
            'filters' => [
                [
                    'field' => 'country',
                    'operator' => 'eq',
                    'value' => 'US'
                ],
                [
                    'field' => 'city',
                    'operator' => 'eq',
                    'value' => 'New York'
                ]
            ]
        ];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2023-01-01 00:00:00';
        $toDate = '2023-12-31 23:59:59';

        // Mock the validator to return no violations
        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);
        $mockViolations->method('count')->willReturn(0);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($mockViolations);

        // Call the method
        $result = $this->chargingLocationManager->validateChargingLocationData(
            $data,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $result);
        $this->assertEquals(0, $result->count());
    }

    /**
     * Test validateChargingLocationData method with invalid date format
     */
    public function testValidateChargingLocationDataWithInvalidDateFormat()
    {
        // Data with invalid date format
        $data = [
            'filters' => [
                [
                    'field' => 'country',
                    'operator' => 'eq',
                    'value' => 'US'
                ]
            ]
        ];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2023/01/01'; // Invalid format, should be Y-m-d H:i:s
        $toDate = '2023-12-31 23:59:59';

        // Create constraint violations
        $violation = new ConstraintViolation(
            'This value is not a valid datetime.',
            null,
            [],
            null,
            'fromDate',
            $fromDate
        );

        $violationList = new ConstraintViolationList([$violation]);

        // Mock the validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        // Call the method
        $result = $this->chargingLocationManager->validateChargingLocationData(
            $data,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $result);
        $this->assertEquals(1, $result->count());
    }

    /**
     * Test validateChargingLocationData method with invalid offset and limit types
     */
    public function testValidateChargingLocationDataWithInvalidOffsetAndLimitTypes()
    {
        // Data with invalid offset and limit types
        $data = [
            'filters' => [
                [
                    'field' => 'country',
                    'operator' => 'eq',
                    'value' => 'US'
                ]
            ]
        ];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 10; // Valid integer
        $limit = 20; // Valid integer
        $fromDate = '2023-01-01 00:00:00';
        $toDate = '2023-12-31 23:59:59';

        // Create constraint violations for testing
        $violationList = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should be of type integer.',
                null,
                [],
                null,
                'offset',
                'invalid'
            ),
            new ConstraintViolation(
                'This value should be of type integer.',
                null,
                [],
                null,
                'limit',
                'invalid'
            )
        ]);

        // Mock the validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        // Call the method
        $result = $this->chargingLocationManager->validateChargingLocationData(
            $data,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $result);
        $this->assertEquals(2, $result->count());
    }

    /**
     * Test validateChargingLocationData method with invalid filter structure
     */
    public function testValidateChargingLocationDataWithInvalidFilterStructure()
    {
        // Data with invalid filter structure
        $data = [
            'filters' => [
                [
                    'field' => '', // Empty field
                    'operator' => 'eq',
                    'value' => 'US'
                ],
                [
                    'field' => 'city',
                    'operator' => '', // Empty operator
                    'value' => 'New York'
                ],
                [
                    'field' => 'state',
                    'operator' => 'eq',
                    'value' => null // Null value
                ]
            ]
        ];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2023-01-01 00:00:00';
        $toDate = '2023-12-31 23:59:59';

        // Create constraint violations
        $fieldViolation = new ConstraintViolation(
            'This value should not be blank.',
            null,
            [],
            null,
            'filters[0].field',
            ''
        );

        $operatorViolation = new ConstraintViolation(
            'This value should not be blank.',
            null,
            [],
            null,
            'filters[1].operator',
            ''
        );

        $valueViolation = new ConstraintViolation(
            'This value should not be null.',
            null,
            [],
            null,
            'filters[2].value',
            null
        );

        $violationList = new ConstraintViolationList([$fieldViolation, $operatorViolation, $valueViolation]);

        // Mock the validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        // Call the method
        $result = $this->chargingLocationManager->validateChargingLocationData(
            $data,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $result);
        $this->assertEquals(3, $result->count());
    }

    /**
     * Test validateChargingLocationData method with all null parameters
     */
    public function testValidateChargingLocationDataWithAllNullParameters()
    {
        // All null parameters
        $data = null;
        $sortBy = null;
        $orderBy = null;
        $offset = null;
        $limit = null;
        $fromDate = null;
        $toDate = null;

        // Mock the validator to return no violations
        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);
        $mockViolations->method('count')->willReturn(0);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($mockViolations);

        // Call the method
        $result = $this->chargingLocationManager->validateChargingLocationData(
            $data,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $result);
        $this->assertEquals(0, $result->count());
    }
}
