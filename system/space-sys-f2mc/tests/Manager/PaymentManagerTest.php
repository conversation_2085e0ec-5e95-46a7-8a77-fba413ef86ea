<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\PaymentManager;
use App\Service\PaymentService;
use Symfony\Component\HttpFoundation\Response;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class PaymentManagerTest extends TestCase
{
    private $paymentService;
    private $paymentManager;
    private $validator;

    protected function setUp(): void
    {
        $this->paymentService = $this->createMock(PaymentService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->validator->expects($this->any())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));
        $this->paymentManager = new PaymentManager($this->paymentService, $this->validator);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())->method('info');
        $logger->expects($this->any())->method('error');

        $this->paymentManager->setLogger($logger);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->paymentManager);
        unset($this->paymentService);
        unset($this->validator);
    }


    // =============== ADD URL ===============

    /**
     * Test addUrl method with successful response containing data field
     */
    public function testAddUrlSuccessfulResponseWithDataField()
    {
        $accessToken = "accessToken";

        // Mock response data with data field
        $responseData = [
            'data' => [
                'paymentUrl' => 'https://payment.example.com/checkout/123456',
                'expiresAt' => '2023-06-01T14:30:00Z',
                'amount' => 25.50,
                'currency' => 'USD'
            ]
        ];

        $this->paymentService->expects($this->once())
            ->method('addUrl')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->paymentManager->addUrl($accessToken);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData['data'], $result->getData());
    }

    /**
     * Test addUrl method with successful response without data field
     */
    public function testAddUrlSuccessfulResponseWithoutDataField()
    {
        $accessToken = "accessToken";

        // Mock response data without data field
        $responseData = [
            'paymentUrl' => 'https://payment.example.com/checkout/123456',
            'expiresAt' => '2023-06-01T14:30:00Z',
            'amount' => 25.50,
            'currency' => 'USD'
        ];

        $this->paymentService->expects($this->once())
            ->method('addUrl')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->paymentManager->addUrl($accessToken);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test addUrl method with error response containing message field
     */
    public function testAddUrlReturnsErrorResponseWithMessageField()
    {
        $accessToken = "accessToken";

        // Mock error response with message field
        $errorResponseData = [
            'message' => 'Invalid access token'
        ];

        $this->paymentService->expects($this->once())
            ->method('addUrl')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponseData));

        $result = $this->paymentManager->addUrl($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Invalid access token', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->toArray()['code']);
    }

    /**
     * Test addUrl method with error response without message field
     */
    public function testAddUrlReturnsDefaultErrorMessageOnFailure()
    {
        $accessToken = "accessToken";

        // Mock error response without message field
        $errorResponseData = [
            'error' => 'Some error occurred'
        ];

        $this->paymentService->expects($this->once())
            ->method('addUrl')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->paymentManager->addUrl($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Operation failed', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testAddUrlHandlesException()
    {
        $accessToken = "accessToken";

        $this->paymentService->expects($this->once())
            ->method('addUrl')
            ->with($accessToken)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->paymentManager->addUrl($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    // =============== GET PAYMENT HISTORY ===============

    /**
     * Test getPaymentHistory method with successful response
     */
    public function testGetPaymentHistorySuccessfulResponse()
    {
        $accessToken = "accessToken";

        // Mock response data with payment history
        $responseData = [
            'data' => [
                [
                    'id' => 'payment-123',
                    'amount' => 15.75,
                    'currency' => 'USD',
                    'status' => 'COMPLETED',
                    'timestamp' => '2023-06-01T12:30:00Z',
                    'description' => 'Charging session payment'
                ],
                [
                    'id' => 'payment-456',
                    'amount' => 22.50,
                    'currency' => 'USD',
                    'status' => 'COMPLETED',
                    'timestamp' => '2023-06-15T14:45:00Z',
                    'description' => 'Charging session payment'
                ]
            ]
        ];

        $this->paymentService->expects($this->once())
            ->method('getPaymentHistory')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->paymentManager->getPaymentHistory($accessToken);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test getPaymentHistory method with empty payment history
     */
    public function testGetPaymentHistoryWithEmptyHistory()
    {
        $accessToken = "accessToken";

        // Mock response data with empty payment history
        $responseData = [
            'data' => []
        ];

        $this->paymentService->expects($this->once())
            ->method('getPaymentHistory')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->paymentManager->getPaymentHistory($accessToken);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test getPaymentHistory method with error response
     */
    public function testGetPaymentHistoryReturnsErrorResponseOnFailure()
    {
        $accessToken = "accessToken";

        // Mock error response
        $errorResponseData = [
            'message' => 'Invalid access token'
        ];

        $this->paymentService->expects($this->once())
            ->method('getPaymentHistory')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponseData));

        $result = $this->paymentManager->getPaymentHistory($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Invalid access token', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->toArray()['code']);
    }

    /**
     * Test getPaymentHistory method with no error message in response
     */
    public function testGetPaymentHistoryReturnsDefaultErrorMessageOnFailure()
    {
        $accessToken = "accessToken";

        // Mock error response with no message
        $errorResponseData = [
            'error' => 'Some error occurred'
        ];

        $this->paymentService->expects($this->once())
            ->method('getPaymentHistory')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->paymentManager->getPaymentHistory($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Operation failed', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    /**
     * Test getPaymentHistory method with exception
     */
    public function testGetPaymentHistoryHandlesException()
    {
        $accessToken = "accessToken";

        $this->paymentService->expects($this->once())
            ->method('getPaymentHistory')
            ->with($accessToken)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->paymentManager->getPaymentHistory($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }
}
