<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\VehicleManager;
use App\Service\VehicleService;
use App\DataMapper\VehicleDataMapper;
use App\Model\VehicleModel;
use Symfony\Component\HttpFoundation\Response;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class VehicleManagerTest extends TestCase
{
    private $vehicleService;
    private $vehicleManager;
    private $validator;
    private $vehicleMapper;
    private $serializer;
    private $logger;

    protected function setUp(): void
    {
        $this->vehicleService = $this->createMock(VehicleService::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->vehicleMapper = $this->createMock(VehicleDataMapper::class);
        $this->validator = $this->createMock(ValidatorInterface::class);

        $this->vehicleManager = new VehicleManager(
            $this->vehicleService,
            $this->validator,
            $this->vehicleMapper
        );

        $this->logger = $this->createMock(LoggerInterface::class);
        
        // Logger methods return void, so we don't need to set a return value
        $this->logger->method('info');
        $this->logger->method('error');

        $this->vehicleManager->setLogger($this->logger);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        
        unset($this->vehicleManager);
        unset($this->vehicleService);
        unset($this->validator);
        unset($this->vehicleMapper);
        unset($this->serializer);
        unset($this->logger);
    }

    // =============== UPDATE ===============

    public function testUpdateSuccessfulResponse()
    {
        $data = [
            'vehicleId' => 123,
            'params' => ['make' => 'Tesla', 'model' => 'Model 3'],
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('update')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_OK, [
                "data" => ["id" => 123, "make" => "Tesla", "model" => "Model 3"],
                "message" => "Successfully modified vehicle"
            ]));

        $result = $this->vehicleManager->update($data);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['message' => 'Successfully modified vehicle'], $result->getData());
    }

    public function testUpdateReturnsErrorResponseOnFailure()
    {
        $data = [
            'vehicleId' => 123,
            'params' => ['make' => 'Tesla', 'model' => 'Model 3'],
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('update')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Operation failed"]));

        $result = $this->vehicleManager->update($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Operation failed", $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testUpdateHandlesException()
    {
        $data = [
            'vehicleId' => 123,
            'params' => ['make' => 'Tesla', 'model' => 'Model 3'],
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('update')
            ->with($data)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->vehicleManager->update($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testUpdateWithNoMessageInResponse()
    {
        $data = [
            'vehicleId' => 123,
            'params' => ['make' => 'Tesla', 'model' => 'Model 3'],
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('update')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, []));

        $result = $this->vehicleManager->update($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Operation failed", $result->toArray()['content']['error']['message']);
    }

    // =============== DELETE ===============

    public function testDeleteSuccessfulResponse()
    {
        $data = [
            'vehicleId' => 123,
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('delete')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_OK, [
                "data" => ["id" => 123],
                "message" => "The vehicle is successfully deleted"
            ]));

        $result = $this->vehicleManager->delete($data);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['message' => 'The vehicle is successfully deleted'], $result->getData());
    }

    public function testDeleteReturnsErrorResponseOnFailure()
    {
        $data = [
            'vehicleId' => 123,
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('delete')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Operation failed"]));

        $result = $this->vehicleManager->delete($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Operation failed", $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testDeleteHandlesException()
    {
        $data = [
            'vehicleId' => 123,
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('delete')
            ->with($data)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->vehicleManager->delete($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testDeleteWithNoMessageInResponse()
    {
        $data = [
            'vehicleId' => 123,
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('delete')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, []));

        $result = $this->vehicleManager->delete($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Operation failed", $result->toArray()['content']['error']['message']);
    }

    // =============== VALIDATE VEHICLE ===============

    public function testValidateVehicleDataWithValidData()
    {
        $data = [
            'vin' => 'FCABEF567IND00179',
            'make' => 'Tesla',
            'model' => 'Model 3',
            'vehicleId' => '123'
        ];

        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);
        $mockViolations->method('count')->willReturn(0);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, $this->anything())
            ->willReturn($mockViolations);

        $violations = $this->vehicleManager->validateVehicleData($data);

        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
        $this->assertEquals(0, $violations->count());
    }

    public function testValidateVehicleDataWithInvalidVin()
    {
        $data = [
            'vin' => 'TOOSHORT', // VIN should be 17 characters
            'make' => 'Tesla',
            'model' => 'Model 3'
        ];

        $violation = new ConstraintViolation(
            'This value should have exactly 17 characters.',
            null,
            [],
            $data,
            'vin',
            $data['vin']
        );

        $violationList = new ConstraintViolationList([$violation]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, $this->anything())
            ->willReturn($violationList);

        $violations = $this->vehicleManager->validateVehicleData($data);

        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
        $this->assertEquals(1, $violations->count());
    }

    public function testValidateVehicleDataWithInvalidVehicleId()
    {
        $data = [
            'vin' => 'FCABEF567IND00179',
            'make' => 'Tesla',
            'model' => 'Model 3',
            'vehicleId' => 'not-a-number' // Should be numeric
        ];

        $violation = new ConstraintViolation(
            'This value should be number.',
            null,
            [],
            $data,
            'vehicleId',
            $data['vehicleId']
        );

        $violationList = new ConstraintViolationList([$violation]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, $this->anything())
            ->willReturn($violationList);

        $violations = $this->vehicleManager->validateVehicleData($data);

        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
        $this->assertEquals(1, $violations->count());
    }

    // =============== GET LIST ===============

    public function testGetListSuccessfulResponse()
    {
        $accessToken = "accessToken123";

        $expectedResponse = ["data" => [
            [
                "id" => 757,
                "make" => ["id" => 5, "name" => "Jeep"],
                "model" => ["id" => 5, "name" => "Grand Cherokee 4xe"],
                "modelYear" => 2024,
                "plugType" => [["id" => 1, "name" => "J1772"]],
                "vin" => "3F9HBEEG4R90116ER"
            ],
            [
                "id" => 758,
                "make" => ["id" => 6, "name" => "Tesla"],
                "model" => ["id" => 7, "name" => "Model 3"],
                "modelYear" => 2023,
                "plugType" => [["id" => 2, "name" => "Tesla Connector"]],
                "vin" => "5YJSA1E40JF262476"
            ]
        ]];

        $this->vehicleService->expects($this->once())
            ->method('getList')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_OK, $expectedResponse));

        $mappedVehicles = [
            new VehicleModel(),
            new VehicleModel()
        ];

        $this->vehicleMapper->expects($this->once())
            ->method('map')
            ->with($expectedResponse['data'])
            ->willReturn($mappedVehicles);

        $result = $this->vehicleManager->getList($accessToken);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($mappedVehicles, $result->getData());
    }

    public function testGetListReturnsErrorResponseOnFailure()
    {
        $accessToken = "accessToken123";

        $this->vehicleService->expects($this->once())
            ->method('getList')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Request failed"]));

        $result = $this->vehicleManager->getList($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Request failed", $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testGetListHandlesException()
    {
        $accessToken = "accessToken123";

        $this->vehicleService->expects($this->once())
            ->method('getList')
            ->with($accessToken)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->vehicleManager->getList($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testGetListWithNoDataInResponse()
    {
        $accessToken = "accessToken123";

        $this->vehicleService->expects($this->once())
            ->method('getList')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_OK, []));

        $result = $this->vehicleManager->getList($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Request failed", $result->toArray()['content']['error']['message']);
    }

    // =============== ADD / NEW ===============

    public function testAddSuccessfulResponse()
    {
        $data = [
            'params' => [
                'vin' => 'FCABEF567IND00179',
                'make' => 'Tesla',
                'model' => 'Model 3'
            ],
            'accessToken' => "accessToken123"
        ];

        $responseData = [
            'data' => [
                'id' => 123,
                'vin' => 'FCABEF567IND00179',
                'make' => 'Tesla',
                'model' => 'Model 3'
            ]
        ];

        $this->vehicleService->expects($this->once())
            ->method('add')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->vehicleManager->add($data);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData['data'], $result->getData());
    }

    public function testAddWithCreatedStatusResponse()
    {
        $data = [
            'params' => [
                'vin' => 'FCABEF567IND00179',
                'make' => 'Tesla',
                'model' => 'Model 3'
            ],
            'accessToken' => "accessToken123"
        ];

        $responseData = [
            'data' => [
                'id' => 123,
                'vin' => 'FCABEF567IND00179',
                'make' => 'Tesla',
                'model' => 'Model 3'
            ]
        ];

        $this->vehicleService->expects($this->once())
            ->method('add')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->vehicleManager->add($data);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($responseData['data'], $result->getData());
    }

    public function testAddReturnsErrorResponseOnFailure()
    {
        $data = [
            'params' => [
                'vin' => 'FCABEF567IND00179',
                'make' => 'Tesla',
                'model' => 'Model 3'
            ],
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('add')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Operation failed"]));

        $result = $this->vehicleManager->add($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Operation failed", $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testAddHandlesException()
    {
        $data = [
            'params' => [
                'vin' => 'FCABEF567IND00179',
                'make' => 'Tesla',
                'model' => 'Model 3'
            ],
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('add')
            ->with($data)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->vehicleManager->add($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testAddWithNoMessageInResponse()
    {
        $data = [
            'params' => [
                'vin' => 'FCABEF567IND00179',
                'make' => 'Tesla',
                'model' => 'Model 3'
            ],
            'accessToken' => "accessToken123"
        ];

        $this->vehicleService->expects($this->once())
            ->method('add')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, []));

        $result = $this->vehicleManager->add($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Operation failed", $result->toArray()['content']['error']['message']);
    }

}
