<?php

namespace App\Tests\Model;

use App\Model\ProfileModel;
use App\Model\UserDataModel;
use App\Model\VehicleModel;
use PHPUnit\Framework\TestCase;

class UserDataModelTest extends TestCase
{
    private UserDataModel $userDataModel;

    protected function setUp(): void
    {
        $this->userDataModel = new UserDataModel();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->userDataModel);
    }

    public function testRequiredPropertiesGettersAndSetters(): void
    {
        // Test accessToken (required)
        $this->userDataModel->setAccessToken('test-access-token');
        $this->assertEquals('test-access-token', $this->userDataModel->getAccessToken());

        // Test refreshToken (required)
        $this->userDataModel->setRefreshToken('test-refresh-token');
        $this->assertEquals('test-refresh-token', $this->userDataModel->getRefreshToken());
    }

    public function testNullablePropertiesGettersAndSetters(): void
    {
        // Test expiresIn (nullable)
        $this->userDataModel->setExpiresIn(3600);
        $this->assertEquals(3600, $this->userDataModel->getExpiresIn());

        // Test idToken (nullable)
        $this->userDataModel->setIdToken('test-id-token');
        $this->assertEquals('test-id-token', $this->userDataModel->getIdToken());

        // Test tokenType (nullable)
        $this->userDataModel->setTokenType('Bearer');
        $this->assertEquals('Bearer', $this->userDataModel->getTokenType());

        // Test consent (nullable, default null)
        $this->assertNull($this->userDataModel->getConsent());
        $this->userDataModel->setConsent('granted');
        $this->assertEquals('granted', $this->userDataModel->getConsent());
        $this->userDataModel->setConsent(null);
        $this->assertNull($this->userDataModel->getConsent());
    }

    public function testComplexPropertiesGettersAndSetters(): void
    {
        $this->userDataModel->setProfile(null);
        $this->assertNull($this->userDataModel->getProfile());

        $profileModel = new ProfileModel();
        $profileModel->setFirstName('John');
        $profileModel->setLastName('Doe');

        $this->userDataModel->setProfile($profileModel);
        $this->assertSame($profileModel, $this->userDataModel->getProfile());
        $this->assertEquals('John', $this->userDataModel->getProfile()->getFirstName());

        $this->userDataModel->setProfile(null);
        $this->assertNull($this->userDataModel->getProfile());

        $this->assertEquals([], $this->userDataModel->getIsShowRoomRecords());

        $showroomRecords = [
            ['id' => 1, 'status' => 'active'],
            ['id' => 2, 'status' => 'pending']
        ];
        $this->userDataModel->setIsShowRoomRecords($showroomRecords);
        $this->assertEquals($showroomRecords, $this->userDataModel->getIsShowRoomRecords());

        $this->userDataModel->setIsShowRoomRecords(null);
        $this->assertNull($this->userDataModel->getIsShowRoomRecords());

        $this->assertNull($this->userDataModel->getVehicle());

        $vehicleModel = $this->createMock(VehicleModel::class);
        $this->userDataModel->setVehicle($vehicleModel);
        $this->assertSame($vehicleModel, $this->userDataModel->getVehicle());

        $this->userDataModel->setVehicle(null);
        $this->assertNull($this->userDataModel->getVehicle());
    }

    public function testToArrayWithAllProperties(): void
    {
        // Set all properties
        $this->userDataModel->setAccessToken('test-access-token');
        $this->userDataModel->setExpiresIn(3600);
        $this->userDataModel->setIdToken('test-id-token');
        $this->userDataModel->setRefreshToken('test-refresh-token');
        $this->userDataModel->setTokenType('Bearer');
        $this->userDataModel->setConsent('granted');

        $profileModel = $this->createMock(ProfileModel::class);
        $profileModel->method('toArray')->willReturn(['firstName' => 'John', 'lastName' => 'Doe']);
        $this->userDataModel->setProfile($profileModel);

        $showroomRecords = [
            ['id' => 1, 'status' => 'active'],
            ['id' => 2, 'status' => 'pending']
        ];
        $this->userDataModel->setIsShowRoomRecords($showroomRecords);

        $vehicleModel = $this->createMock(VehicleModel::class);
        $vehicleModel->method('toArray')->willReturn(['vin' => 'ABC123', 'make' => 'Tesla']);
        $this->userDataModel->setVehicle($vehicleModel);

        // Get the actual array from toArray method
        $actualArray = $this->userDataModel->toArray();

        // Check individual keys instead of comparing the whole array
        $this->assertEquals('test-access-token', $actualArray['accessToken']);
        $this->assertEquals(3600, $actualArray['expiresIn']);
        $this->assertEquals('test-id-token', $actualArray['idToken']);
        $this->assertEquals('test-refresh-token', $actualArray['refreshToken']);
        $this->assertEquals('Bearer', $actualArray['tokenType']);
        $this->assertEquals('granted', $actualArray['consent']);
        $this->assertEquals(['firstName' => 'John', 'lastName' => 'Doe'], $actualArray['profile']);
        $this->assertEquals($showroomRecords, $actualArray['isShowRoomRecords']);

        // Check that vehicle is present but don't compare its exact value
        $this->assertArrayHasKey('vehicle', $actualArray);
    }

    public function testToArrayWithMinimalProperties(): void
    {
        // Set only required properties
        $this->userDataModel->setAccessToken('test-access-token');
        $this->userDataModel->setRefreshToken('test-refresh-token');

        // Initialize nullable properties to avoid uninitialized property errors
        $this->userDataModel->setExpiresIn(0);
        $this->userDataModel->setIdToken('');
        $this->userDataModel->setTokenType('');
        $this->userDataModel->setProfile(null);
        $this->userDataModel->setVehicle(null);

        // Test toArray method
        $result = $this->userDataModel->toArray();

        // Check that only required properties are included
        $this->assertArrayHasKey('accessToken', $result);
        $this->assertArrayHasKey('refreshToken', $result);

        // These properties should be included because they're initialized with non-null values
        $this->assertArrayHasKey('expiresIn', $result);
        $this->assertArrayHasKey('idToken', $result);
        $this->assertArrayHasKey('tokenType', $result);

        // These properties should not be included because they're null
        $this->assertArrayNotHasKey('consent', $result);
        $this->assertArrayNotHasKey('profile', $result);
        $this->assertArrayNotHasKey('vehicle', $result);
    }

    public function testToArrayWithNullComplexProperties(): void
    {
        // Set required properties
        $this->userDataModel->setAccessToken('test-access-token');
        $this->userDataModel->setExpiresIn(3600);
        $this->userDataModel->setIdToken('test-id-token');
        $this->userDataModel->setRefreshToken('test-refresh-token');
        $this->userDataModel->setTokenType('Bearer');

        // Explicitly set complex properties to null
        $this->userDataModel->setProfile(null);
        $this->userDataModel->setIsShowRoomRecords(null);
        $this->userDataModel->setVehicle(null);

        // Get array representation
        $result = $this->userDataModel->toArray();

        // Check that null complex properties are not included
        $this->assertArrayNotHasKey('profile', $result);
        $this->assertArrayNotHasKey('vehicle', $result);

        // isShowRoomRecords is explicitly set to null, so it should be filtered out
        $this->assertArrayNotHasKey('isShowRoomRecords', $result);
    }

    public function testToArrayFiltersNullValues(): void
    {
        // Set some properties to non-null and others to null or empty values
        $this->userDataModel->setAccessToken('test-access-token');
        $this->userDataModel->setRefreshToken('test-refresh-token');
        $this->userDataModel->setExpiresIn(0);
        $this->userDataModel->setIdToken('');    
        $this->userDataModel->setTokenType('Bearer');
        $this->userDataModel->setConsent(null);   
        $this->userDataModel->setProfile(null);   
        $this->userDataModel->setVehicle(null); 

        // Get array representation
        $result = $this->userDataModel->toArray();

        // Check that required values are present
        $this->assertArrayHasKey('accessToken', $result);
        $this->assertArrayHasKey('refreshToken', $result);
        $this->assertArrayHasKey('tokenType', $result);

        // These should be present because they have non-null values (0 and empty string)
        $this->assertArrayHasKey('expiresIn', $result);
        $this->assertArrayHasKey('idToken', $result);

        // These should be filtered out because they're null
        $this->assertArrayNotHasKey('consent', $result);
        $this->assertArrayNotHasKey('profile', $result);
        $this->assertArrayNotHasKey('vehicle', $result);
    }

    public function testEmptyShowroomRecordsHandling(): void
    {
        // Set required properties
        $this->userDataModel->setAccessToken('test-access-token');
        $this->userDataModel->setRefreshToken('test-refresh-token');

        // Initialize other required properties to avoid uninitialized property errors
        $this->userDataModel->setExpiresIn(0);
        $this->userDataModel->setIdToken('');
        $this->userDataModel->setTokenType('');
        $this->userDataModel->setProfile(null);
        $this->userDataModel->setVehicle(null);

        // Test with empty array
        $this->userDataModel->setIsShowRoomRecords([]);
        $result = $this->userDataModel->toArray();

        // Empty array should be included in the result
        $this->assertArrayHasKey('isShowRoomRecords', $result);
        $this->assertIsArray($result['isShowRoomRecords']);
        $this->assertEmpty($result['isShowRoomRecords']);
    }
}