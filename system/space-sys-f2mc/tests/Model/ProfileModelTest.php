<?php

namespace App\Tests\Model;

use App\Model\ProfileModel;
use PHPUnit\Framework\TestCase;

class ProfileModelTest extends TestCase
{
    private ProfileModel $profileModel;

    protected function setUp(): void
    {
        $this->profileModel = new ProfileModel();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->profileModel);
    }

    public function testGettersAndSetters(): void
    {
        // Test firstName
        $this->profileModel->setFirstName('John');
        $this->assertEquals('John', $this->profileModel->getFirstName());

        // Test lastName
        $this->profileModel->setLastName('Doe');
        $this->assertEquals('Doe', $this->profileModel->getLastName());

        // Test email
        $this->profileModel->setEmail('<EMAIL>');
        $this->assertEquals('<EMAIL>', $this->profileModel->getEmail());

        // Test zipCode
        $this->profileModel->setZipCode('12345');
        $this->assertEquals('12345', $this->profileModel->getZipCode());

        // Test country
        $this->profileModel->setCountry('United States');
        $this->assertEquals('United States', $this->profileModel->getCountry());

        // Test countryCode
        $this->profileModel->setCountryCode('US');
        $this->assertEquals('US', $this->profileModel->getCountryCode());

        // Test state
        $this->profileModel->setState('California');
        $this->assertEquals('California', $this->profileModel->getState());

        // Test city
        $this->profileModel->setCity('San Francisco');
        $this->assertEquals('San Francisco', $this->profileModel->getCity());

        // Test isVinAdded
        $this->profileModel->setIsVinAdded(true);
        $this->assertTrue($this->profileModel->isVinAdded());

        // Test isNewUser
        $this->profileModel->setIsNewUser(false);
        $this->assertFalse($this->profileModel->isNewUser());

        // Test id
        $this->profileModel->setId('user123');
        $this->assertEquals('user123', $this->profileModel->getId());
    }

    public function testToArray(): void
    {
        // Set all properties
        $this->profileModel->setFirstName('John');
        $this->profileModel->setLastName('Doe');
        $this->profileModel->setEmail('<EMAIL>');
        $this->profileModel->setZipCode('12345');
        $this->profileModel->setCountry('United States');
        $this->profileModel->setCountryCode('US');
        $this->profileModel->setState('California');
        $this->profileModel->setCity('San Francisco');
        $this->profileModel->setIsVinAdded(true);
        $this->profileModel->setIsNewUser(false);
        $this->profileModel->setId('user123');

        // Expected array representation
        $expectedArray = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => '12345',
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'isVinAdded' => true,
            'isNewUser' => false,
            'id' => 'user123',
        ];

        // Test toArray method
        $this->assertEquals($expectedArray, $this->profileModel->toArray());
    }

    public function testEmptyProfileToArray(): void
    {
        // Create a new ProfileModel without setting any properties
        $emptyProfile = new ProfileModel();

        try {
            $array = $emptyProfile->toArray();
            $this->fail('Expected an error when calling toArray() on an empty ProfileModel');
        } catch (\Error $e) {
            // Match the actual PHP error message for uninitialized typed properties
            $this->assertStringContainsString('must not be accessed before initialization', $e->getMessage());
        }
    }

    public function testBooleanProperties(): void
    {
        // Test boolean properties with different values
        $this->profileModel->setIsVinAdded(true);
        $this->assertTrue($this->profileModel->isVinAdded());
        
        $this->profileModel->setIsVinAdded(false);
        $this->assertFalse($this->profileModel->isVinAdded());
        
        $this->profileModel->setIsNewUser(true);
        $this->assertTrue($this->profileModel->isNewUser());
        
        $this->profileModel->setIsNewUser(false);
        $this->assertFalse($this->profileModel->isNewUser());
    }
}