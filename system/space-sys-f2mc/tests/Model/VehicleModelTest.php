<?php

namespace App\Tests\Model;

use App\Model\VehicleModel;
use PHPUnit\Framework\TestCase;

class VehicleModelTest extends TestCase
{
    private VehicleModel $vehicleModel;

    protected function setUp(): void
    {
        $this->vehicleModel = new VehicleModel();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->vehicleModel);
    }

    public function testBasicGettersAndSetters(): void
    {
        // Test id
        $this->vehicleModel->setId(123);
        $this->assertEquals(123, $this->vehicleModel->getId());

        // Test modelYear
        $this->vehicleModel->setModelYear(2023);
        $this->assertEquals(2023, $this->vehicleModel->getModelYear());

        // Test vin
        $this->vehicleModel->setVin('5YJSA1E40JF262476');
        $this->assertEquals('5YJSA1E40JF262476', $this->vehicleModel->getVin());
    }

    public function testArrayProperties(): void
    {
        // Test make property
        $make = ['id' => 5, 'name' => 'Tesla'];
        $this->vehicleModel->setMake($make);
        $this->assertEquals($make, $this->vehicleModel->getMake());
        $this->assertIsArray($this->vehicleModel->getMake());
        $this->assertArrayHasKey('id', $this->vehicleModel->getMake());
        $this->assertArrayHasKey('name', $this->vehicleModel->getMake());

        // Test model property
        $model = ['id' => 3, 'name' => 'Model 3'];
        $this->vehicleModel->setModel($model);
        $this->assertEquals($model, $this->vehicleModel->getModel());
        $this->assertIsArray($this->vehicleModel->getModel());
        $this->assertArrayHasKey('id', $this->vehicleModel->getModel());
        $this->assertArrayHasKey('name', $this->vehicleModel->getModel());

        // Test plugType property (array of arrays)
        $plugType = [
            ['id' => 1, 'name' => 'Type 2'],
            ['id' => 2, 'name' => 'CCS']
        ];
        $this->vehicleModel->setPlugType($plugType);
        $this->assertEquals($plugType, $this->vehicleModel->getPlugType());
        $this->assertIsArray($this->vehicleModel->getPlugType());
        $this->assertCount(2, $this->vehicleModel->getPlugType());
        $this->assertArrayHasKey('id', $this->vehicleModel->getPlugType()[0]);
        $this->assertArrayHasKey('name', $this->vehicleModel->getPlugType()[0]);
    }

    public function testEmptyArrayProperties(): void
    {
        // Test with empty arrays
        $this->vehicleModel->setMake([]);
        $this->assertEquals([], $this->vehicleModel->getMake());
        $this->assertIsArray($this->vehicleModel->getMake());
        $this->assertEmpty($this->vehicleModel->getMake());

        $this->vehicleModel->setModel([]);
        $this->assertEquals([], $this->vehicleModel->getModel());
        $this->assertIsArray($this->vehicleModel->getModel());
        $this->assertEmpty($this->vehicleModel->getModel());

        $this->vehicleModel->setPlugType([]);
        $this->assertEquals([], $this->vehicleModel->getPlugType());
        $this->assertIsArray($this->vehicleModel->getPlugType());
        $this->assertEmpty($this->vehicleModel->getPlugType());
    }

    public function testComplexArrayProperties(): void
    {
        // Test with nested complex arrays
        $make = [
            'id' => 5,
            'name' => 'Tesla',
            'country' => 'USA',
            'details' => [
                'founded' => 2003,
                'headquarters' => 'Austin, Texas'
            ]
        ];
        $this->vehicleModel->setMake($make);
        $this->assertEquals($make, $this->vehicleModel->getMake());
        $this->assertIsArray($this->vehicleModel->getMake()['details']);

        // Test with array containing mixed types
        $model = [
            'id' => 3,
            'name' => 'Model 3',
            'variants' => ['Standard Range', 'Long Range', 'Performance'],
            'specifications' => [
                'range' => 358,
                'topSpeed' => 162,
                'available' => true
            ]
        ];
        $this->vehicleModel->setModel($model);
        $this->assertEquals($model, $this->vehicleModel->getModel());
        $this->assertIsArray($this->vehicleModel->getModel()['variants']);
        $this->assertIsArray($this->vehicleModel->getModel()['specifications']);
    }

    public function testEdgeCasesForIntegerProperties(): void
    {
        // Test with minimum integer value
        $this->vehicleModel->setId(0);
        $this->assertEquals(0, $this->vehicleModel->getId());

        // Test with large integer value
        $largeId = PHP_INT_MAX;
        $this->vehicleModel->setId($largeId);
        $this->assertEquals($largeId, $this->vehicleModel->getId());

        // Test with minimum model year
        $this->vehicleModel->setModelYear(1886); // First automobile
        $this->assertEquals(1886, $this->vehicleModel->getModelYear());

        // Test with future model year
        $this->vehicleModel->setModelYear(2100);
        $this->assertEquals(2100, $this->vehicleModel->getModelYear());
    }

    public function testEdgeCasesForStringProperties(): void
    {
        // Test with empty string
        $this->vehicleModel->setVin('');
        $this->assertEquals('', $this->vehicleModel->getVin());

        // Test with very long VIN (though standard VINs are 17 characters)
        $longVin = str_repeat('A', 100);
        $this->vehicleModel->setVin($longVin);
        $this->assertEquals($longVin, $this->vehicleModel->getVin());

        // Test with special characters
        $specialVin = '5YJ$A1E-4_JF#262476';
        $this->vehicleModel->setVin($specialVin);
        $this->assertEquals($specialVin, $this->vehicleModel->getVin());
    }

    public function testToArray(): void
    {
        // Set all properties
        $this->vehicleModel->setId(123);
        $this->vehicleModel->setMake(['id' => 5, 'name' => 'Tesla']);
        $this->vehicleModel->setModel(['id' => 3, 'name' => 'Model 3']);
        $this->vehicleModel->setModelYear(2023);
        $this->vehicleModel->setPlugType([['id' => 1, 'name' => 'Type 2']]);
        $this->vehicleModel->setVin('5YJSA1E40JF262476');

        // Expected array representation
        $expectedArray = [
            'id' => 123,
            'model' => ['id' => 3, 'name' => 'Model 3'],
            'make' => ['id' => 5, 'name' => 'Tesla'],
            'modelYear' => 2023,
            'plugType' => [['id' => 1, 'name' => 'Type 2']],
            'vin' => '5YJSA1E40JF262476'
        ];

        // Test toArray method
        $this->assertEquals($expectedArray, $this->vehicleModel->toArray());
    }

    public function testToArrayWithEmptyArrays(): void
    {
        // Set properties with empty arrays
        $this->vehicleModel->setId(123);
        $this->vehicleModel->setMake([]);
        $this->vehicleModel->setModel([]);
        $this->vehicleModel->setModelYear(2023);
        $this->vehicleModel->setPlugType([]);
        $this->vehicleModel->setVin('5YJSA1E40JF262476');

        // Expected array representation
        $expectedArray = [
            'id' => 123,
            'model' => [],
            'make' => [],
            'modelYear' => 2023,
            'plugType' => [],
            'vin' => '5YJSA1E40JF262476'
        ];

        // Test toArray method
        $this->assertEquals($expectedArray, $this->vehicleModel->toArray());
    }

    public function testEmptyVehicleToArray(): void
    {
        // Create a new VehicleModel without setting any properties
        $emptyVehicle = new VehicleModel();

        try {
            $array = $emptyVehicle->toArray();
            $this->fail('Expected an error when calling toArray() on an empty VehicleModel');
        } catch (\Error $e) {
            // Match the actual PHP error message for uninitialized typed properties
            $this->assertStringContainsString('must not be accessed before initialization', $e->getMessage());
        }
    }

    public function testPartiallyInitializedVehicleToArray(): void
    {
        // Set only some properties
        $this->vehicleModel->setId(123);
        $this->vehicleModel->setVin('5YJSA1E40JF262476');

        try {
            $array = $this->vehicleModel->toArray();
            $this->fail('Expected an error when calling toArray() on a partially initialized VehicleModel');
        } catch (\Error $e) {
            // Match the actual PHP error message for uninitialized typed properties
            $this->assertStringContainsString('must not be accessed before initialization', $e->getMessage());
        }
    }
}
