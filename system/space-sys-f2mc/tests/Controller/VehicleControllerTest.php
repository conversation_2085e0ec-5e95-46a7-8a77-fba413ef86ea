<?php

namespace App\Tests\Controller;

use App\Controller\VehicleController;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\VehicleManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;

class VehicleControllerTest extends WebTestCase
{
    private $vehicleController;
    private $vehicleManager;
    private $container;
    private $serializer;

    protected function setUp(): void
    {
        $this->vehicleManager = $this->createMock(VehicleManager::class);
        $this->container = $this->createMock(ContainerInterface::class);
        $this->serializer = $this->createMock(SerializerInterface::class);

        // Configure container mock
        $this->container->method('has')->willReturn(true);
        $this->container->method('get')
            ->willReturnCallback(function($service) {
                if ($service === 'serializer') {
                    return $this->serializer;
                }
                return null;
            });

        $this->serializer->method('serialize')
            ->willReturnCallback(function ($data) {
                return json_encode($data);
            });

        $this->vehicleController = new VehicleController($this->vehicleManager);
        $this->vehicleController->setContainer($this->container);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->vehicleController);
        unset($this->vehicleManager);
        unset($this->container);
        unset($this->serializer);
    }

    // =========== UPDATE ===========

    public function testUpdateReturnsSuccessfulResponse()
    {
        $server = ['CONTENT_TYPE' => 'application/json', 'HTTP_ACCESSTOKEN' => 'valid_token'];
        $data = json_encode(['make' => 'Tesla', 'model' => 'Model 3']);
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId . '/default', Request::METHOD_PUT, [], [], [], $server, $data);

        $emptyViolations = $this->createMock(ConstraintViolationListInterface::class);
        $emptyViolations->method('count')->willReturn(0);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($emptyViolations);

        $expectedResponse = [
            "message" => "Successfully modified vehicle"
        ];

        $this->vehicleManager->method('update')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->vehicleController->update($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"message":"Successfully modified vehicle"}}', $response->getContent());
    }

    public function testUpdateReturnsValidationError()
    {
        $server = ['CONTENT_TYPE' => 'application/json', 'HTTP_ACCESSTOKEN' => 'valid_token'];
        $data = json_encode(['invalid_param' => 'value']);
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId . '/default', Request::METHOD_PUT, [], [], [], $server, $data);

        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            [],
            null,
            'make',
            null
        );

        $violations = new ConstraintViolationList([$constraint]);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($violations);

        $response = $this->vehicleController->update($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('make', $response->getContent());
    }

    public function testUpdateReturnsErrorResponse()
    {
        $server = ['CONTENT_TYPE' => 'application/json', 'HTTP_ACCESSTOKEN' => 'invalid_token'];
        $data = json_encode(['make' => 'Tesla', 'model' => 'Model 3']);
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId . '/default', Request::METHOD_PUT, [], [], [], $server, $data);

        $emptyViolations = $this->createMock(ConstraintViolationListInterface::class);
        $emptyViolations->method('count')->willReturn(0);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($emptyViolations);

        $this->vehicleManager->method('update')
            ->willReturn(new ErrorResponse('Unauthorized', Response::HTTP_UNAUTHORIZED));

        $response = $this->vehicleController->update($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertStringContainsString('Unauthorized', $response->getContent());
    }

    public function testUpdateWithMissingAccessToken()
    {
        $server = ['CONTENT_TYPE' => 'application/json']; // No access token
        $data = json_encode(['make' => 'Tesla', 'model' => 'Model 3']);
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId . '/default', Request::METHOD_PUT, [], [], [], $server, $data);

        $emptyViolations = $this->createMock(ConstraintViolationListInterface::class);
        $emptyViolations->method('count')->willReturn(0);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($emptyViolations);

        $this->vehicleManager->method('update')
            ->willReturn(new ErrorResponse('Missing access token', Response::HTTP_BAD_REQUEST));

        $response = $this->vehicleController->update($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
    }

    // ========= DELETE =========

    public function testDeleteReturnsSuccessfulResponse()
    {
        $server = ['CONTENT_TYPE' => 'application/json', 'HTTP_ACCESSTOKEN' => 'valid_token'];
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId, Request::METHOD_DELETE, [], [], [], $server, []);

        $emptyViolations = $this->createMock(ConstraintViolationListInterface::class);
        $emptyViolations->method('count')->willReturn(0);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($emptyViolations);

        $expectedResponse = [
            "message" => "The vehicle is successfully deleted"
        ];

        $this->vehicleManager->method('delete')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->vehicleController->delete($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"message":"The vehicle is successfully deleted"}}', $response->getContent());
    }

    public function testDeleteReturnsValidationError()
    {
        $server = ['CONTENT_TYPE' => 'application/json', 'HTTP_ACCESSTOKEN' => 'valid_token'];
        $vehicleId = 'abc'; // Invalid vehicle ID (not numeric)
        $request = Request::create('/user/vehicles/' . $vehicleId, Request::METHOD_DELETE, [], [], [], $server, []);

        $constraint = new ConstraintViolation(
            'This value should be numeric.',
            null,
            [],
            null,
            'vehicleId',
            null
        );

        $violations = new ConstraintViolationList([$constraint]);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($violations);

        $response = $this->vehicleController->delete($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('vehicleId', $response->getContent());
    }

    public function testDeleteReturnsErrorResponse()
    {
        $server = ['CONTENT_TYPE' => 'application/json', 'HTTP_ACCESSTOKEN' => 'invalid_token'];
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId, Request::METHOD_DELETE, [], [], [], $server, []);

        $emptyViolations = $this->createMock(ConstraintViolationListInterface::class);
        $emptyViolations->method('count')->willReturn(0);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($emptyViolations);

        $this->vehicleManager->method('delete')
            ->willReturn(new ErrorResponse('Unauthorized', Response::HTTP_UNAUTHORIZED));

        $response = $this->vehicleController->delete($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertStringContainsString('Unauthorized', $response->getContent());
    }

    public function testDeleteWithMissingAccessToken()
    {
        $server = ['CONTENT_TYPE' => 'application/json']; // No access token
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId, Request::METHOD_DELETE, [], [], [], $server, []);

        $emptyViolations = $this->createMock(ConstraintViolationListInterface::class);
        $emptyViolations->method('count')->willReturn(0);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($emptyViolations);

        $this->vehicleManager->method('delete')
            ->willReturn(new ErrorResponse('Missing access token', Response::HTTP_BAD_REQUEST));

        $response = $this->vehicleController->delete($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
    }

    // ========= GET LIST =========

    public function testGetListReturnsSuccessfulResponse()
    {
        // Create a request with the HTTP_ACCESSTOKEN header
        $server = ['HTTP_ACCESSTOKEN' => 'valid_token'];
        $request = Request::create('/user/vehicles', Request::METHOD_GET, [], [], [], $server);

        $expectedResponse = [
            [
                "id" => 757,
                "make" => ["id" => 5, "name" => "Jeep"],
                "model" => ["id" => 5, "name" => "Grand Cherokee 4xe"],
                "modelYear" => 2024,
                "plugType" => [["id" => 1, "name" => "J1772"]],
                "vin" => "3F9HBEEG4R90116ER"
            ],
            [
                "id" => 758,
                "make" => ["id" => 6, "name" => "Tesla"],
                "model" => ["id" => 7, "name" => "Model 3"],
                "modelYear" => 2023,
                "plugType" => [["id" => 2, "name" => "Tesla Connector"]],
                "vin" => "5YJSA1E40JF262476"
            ]
        ];

        $this->vehicleManager->method('getList')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->vehicleController->getList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertStringContainsString('Jeep', $response->getContent());
        $this->assertStringContainsString('Tesla', $response->getContent());
    }

    public function testGetListReturnsErrorResponse()
    {
        // Create a request with the HTTP_ACCESSTOKEN header
        $server = ['HTTP_ACCESSTOKEN' => 'invalid_token'];
        $request = Request::create('/user/vehicles', Request::METHOD_GET, [], [], [], $server);

        $this->vehicleManager->method('getList')
            ->willReturn(new ErrorResponse('Unauthorized', Response::HTTP_UNAUTHORIZED));

        $response = $this->vehicleController->getList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertStringContainsString('Unauthorized', $response->getContent());
    }

    public function testGetListWithMissingAccessToken()
    {
        // Create a request without the HTTP_ACCESSTOKEN header
        $request = Request::create('/user/vehicles', Request::METHOD_GET);

        $response = $this->vehicleController->getList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Unauthorized', $response->getContent());
    }

    public function testGetListWithEmptyResponse()
    {
        // Create a request with the HTTP_ACCESSTOKEN header
        $server = ['HTTP_ACCESSTOKEN' => 'valid_token'];
        $request = Request::create('/user/vehicles', Request::METHOD_GET, [], [], [], $server);

        $this->vehicleManager->method('getList')
            ->willReturn(new SuccessResponse([], Response::HTTP_OK));

        $response = $this->vehicleController->getList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":[]}', $response->getContent());
    }

    // =========== ADD / NEW ===========

    public function testAddReturnsSuccessfulResponse()
    {
        $server = ['CONTENT_TYPE' => 'application/json', 'HTTP_ACCESSTOKEN' => 'valid_token'];
        $data = json_encode([
            'vin' => 'FCABEF567IND00179',
            'make' => 'Tesla',
            'model' => 'Model 3'
        ]);
        $request = Request::create('/user/vehicles', Request::METHOD_POST, [], [], [], $server, $data);

        $emptyViolations = $this->createMock(ConstraintViolationListInterface::class);
        $emptyViolations->method('count')->willReturn(0);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($emptyViolations);

        $expectedResponse = [
            'id' => 123,
            'vin' => 'FCABEF567IND00179',
            'make' => 'Tesla',
            'model' => 'Model 3'
        ];

        $this->vehicleManager->method('add')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->vehicleController->add($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertStringContainsString('FCABEF567IND00179', $response->getContent());
    }

    public function testAddReturnsValidationError()
    {
        $server = ['CONTENT_TYPE' => 'application/json', 'HTTP_ACCESSTOKEN' => 'valid_token'];
        $data = json_encode(['invalid_param' => 'value']);
        $request = Request::create('/user/vehicles', Request::METHOD_POST, [], [], [], $server, $data);

        $constraint = new ConstraintViolation(
            'VIN is required.',
            null,
            [],
            null,
            'vin',
            null
        );

        $violations = new ConstraintViolationList([$constraint]);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($violations);

        $response = $this->vehicleController->add($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('vin', $response->getContent());
    }

    public function testAddReturnsErrorResponse()
    {
        $server = ['CONTENT_TYPE' => 'application/json', 'HTTP_ACCESSTOKEN' => 'invalid_token'];
        $data = json_encode([
            'vin' => 'FCABEF567IND00179',
            'make' => 'Tesla',
            'model' => 'Model 3'
        ]);
        $request = Request::create('/user/vehicles', Request::METHOD_POST, [], [], [], $server, $data);

        $emptyViolations = $this->createMock(ConstraintViolationListInterface::class);
        $emptyViolations->method('count')->willReturn(0);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($emptyViolations);

        $this->vehicleManager->method('add')
            ->willReturn(new ErrorResponse('Unauthorized', Response::HTTP_UNAUTHORIZED));

        $response = $this->vehicleController->add($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertStringContainsString('Unauthorized', $response->getContent());
    }

    public function testAddWithMissingAccessToken()
    {
        $server = ['CONTENT_TYPE' => 'application/json']; // No access token
        $data = json_encode([
            'vin' => 'FCABEF567IND00179',
            'make' => 'Tesla',
            'model' => 'Model 3'
        ]);
        $request = Request::create('/user/vehicles', Request::METHOD_POST, [], [], [], $server, $data);

        $emptyViolations = $this->createMock(ConstraintViolationListInterface::class);
        $emptyViolations->method('count')->willReturn(0);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($emptyViolations);

        $this->vehicleManager->method('add')
            ->willReturn(new ErrorResponse('Missing access token', Response::HTTP_BAD_REQUEST));

        $response = $this->vehicleController->add($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
    }

    public function testAddWithInvalidVin()
    {
        $server = ['CONTENT_TYPE' => 'application/json', 'HTTP_ACCESSTOKEN' => 'valid_token'];
        $data = json_encode([
            'vin' => 'INVALID', // Too short for a VIN
            'make' => 'Tesla',
            'model' => 'Model 3'
        ]);
        $request = Request::create('/user/vehicles', Request::METHOD_POST, [], [], [], $server, $data);

        $constraint = new ConstraintViolation(
            'VIN must be 17 characters.',
            null,
            [],
            null,
            'vin',
            null
        );

        $violations = new ConstraintViolationList([$constraint]);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($violations);

        $response = $this->vehicleController->add($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('vin', $response->getContent());
    }
}
