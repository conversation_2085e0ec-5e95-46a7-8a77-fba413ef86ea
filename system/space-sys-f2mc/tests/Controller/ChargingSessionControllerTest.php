<?php

use App\Controller\ChargingSessionController;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Manager\ChargingSessionManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ChargingSessionControllerTest extends WebTestCase
{
    private $chargingSessionController;
    private $ChargingSessionManager;

    protected function setUp(): void
    {
        $validator = $this->createMock(ValidatorInterface::class);
        $this->ChargingSessionManager = $this->createMock(ChargingSessionManager::class);
        $this->chargingSessionController = new ChargingSessionController($validator);
        $this->chargingSessionController->setContainer(static::getContainer());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        
        unset($this->chargingSessionController);
        unset($this->ChargingSessionManager);
    }

    // ============== Start Session Tests =================

    public function testStartReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $request = Request::create('/charging-sessions/start', Request::METHOD_POST, [], [], [], $headers, $data);
        $this->ChargingSessionManager->method('validateChargingSessionData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->ChargingSessionManager->method('startSession')
            ->willReturn(new ErrorResponse('Invalid value', Response::HTTP_BAD_REQUEST));

        $response = $this->chargingSessionController->startSession($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Invalid value', $response->getContent());
    }

    public function testStartReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['valid_param' => 'value']);
        $request = Request::create('/charging-sessions/start', Request::METHOD_POST, [], [], [], $headers, $data);

        $this->ChargingSessionManager->method('validateChargingSessionData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            'data' => "idSession"
        ];

        $this->ChargingSessionManager->method('startSession')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->chargingSessionController->startSession($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"data":"idSession"}}', $response->getContent());
    }

    public function testStartReturnsValidationError()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $request = Request::create('/charging-sessions/start', Request::METHOD_POST, [], [], [], $headers, $data);

        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            ['data'],
            null,
            'data',
            null
        );

        $this->ChargingSessionManager->method('validateChargingSessionData')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->chargingSessionController->startSession($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
    }

    // ============== Stop Session Tests =================

    public function testStopReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['sessionId' => 'invalid-id']);
        $request = Request::create('/charging-sessions/stop', Request::METHOD_POST, [], [], [], $headers, $data);
        
        $this->ChargingSessionManager->method('validateSessionStopData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->ChargingSessionManager->method('stopSession')
            ->willReturn(new ErrorResponse('Session not found', Response::HTTP_NOT_FOUND));

        $response = $this->chargingSessionController->stopSession($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());
        $this->assertStringContainsString('Session not found', $response->getContent());
    }

    public function testStopReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json', 'accessToken' => 'valid-token'];
        $data = json_encode(['sessionId' => 'valid-id', 'response_url' => 'http://example.com/callback']);
        $request = Request::create('/charging-sessions/stop', Request::METHOD_POST, [], [], [], $headers, $data);

        $this->ChargingSessionManager->method('validateSessionStopData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            'data' => true
        ];

        $this->ChargingSessionManager->method('stopSession')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->chargingSessionController->stopSession($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"data":true}}', $response->getContent());
    }

    public function testStopReturnsValidationError()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $request = Request::create('/charging-sessions/stop', Request::METHOD_POST, [], [], [], $headers, $data);

        $constraint = new ConstraintViolation(
            'Session ID is required.',
            null,
            [],
            null,
            'sessionId',
            null
        );

        $this->ChargingSessionManager->method('validateSessionStopData')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->chargingSessionController->stopSession($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('Session ID is required', $response->getContent());
    }

    // ============== CDR Data =================

    public function testGetCdrDataReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $request = Request::create("/charging/session/$sessionId/cdr", Request::METHOD_GET, [], [], [], $headers, $data);

        $this->ChargingSessionManager->method('getCdrData')
            ->willReturn(new ErrorResponse('Invalid value', Response::HTTP_BAD_REQUEST));

        $response = $this->chargingSessionController->getCdrData($sessionId, $request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Invalid value', $response->getContent());
    }

    public function testGetCdrDataReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json', 'accessToken' => 'valid-token'];
        $data = json_encode(['valid_param' => 'value']);
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $request = Request::create("/charging/session/$sessionId/cdr", Request::METHOD_GET, [], [], [], $headers, $data);

        $expectedResponse = [
            'data' => "idSession"
        ];

        $this->ChargingSessionManager->method('getCdrData')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->chargingSessionController->getCdrData($sessionId, $request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"data":"idSession"}}', $response->getContent());
    }

    public function testGetCdrDataReturnsValidationError()
    {
        $headers = ['Content-Type' => 'application/json'];
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $request = Request::create("/charging/session/$sessionId/cdr", Request::METHOD_GET, [], [], [], $headers);

        // Create a validator mock that will validate the token
        $validator = $this->getMockBuilder(ValidatorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $constraint = new ConstraintViolation(
            'Access token is required.',
            null,
            [],
            null,
            'token',
            null
        );

        $violationList = new ConstraintViolationList([$constraint]);
        $validator->method('validate')->willReturn($violationList);

        // Create a new controller with our mocked validator
        $controller = new ChargingSessionController($validator);
        $controller->setContainer(static::getContainer());

        $response = $controller->getCdrData($sessionId, $request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('Access token is required', $response->getContent());
    }

    // ============== Session History =================

    public function testHistoryReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json', 'accessToken' => 'valid-token'];
        $request = Request::create(
            '/charging-sessions/history',
            Request::METHOD_GET,
            ['cpo' => 'provider1', 'from' => '2023-01-01', 'to' => '2023-12-31', 'status' => 'completed'],
            [],
            [],
            $headers
        );

        // Create a mock for ConstraintViolationListInterface that returns count() = 0
        $violationList = $this->createMock(ConstraintViolationListInterface::class);
        $violationList->method('count')->willReturn(0);

        // Mock the validator to return no violations
        $validator = $this->getMockBuilder(ValidatorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
        $validator->method('validate')->willReturn($violationList);

        // Create a new controller with our mocked validator
        $controller = new ChargingSessionController($validator);
        $controller->setContainer(static::getContainer());

        $expectedResponse = [
            'data' => [
                [
                    'id' => 'session1',
                    'startTime' => '2023-01-15T10:00:00Z',
                    'endTime' => '2023-01-15T11:30:00Z',
                    'status' => 'completed'
                ]
            ],
            'total' => 1
        ];

        $this->ChargingSessionManager->method('getHistory')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $controller->history($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"data":[{"id":"session1","startTime":"2023-01-15T10:00:00Z","endTime":"2023-01-15T11:30:00Z","status":"completed"}],"total":1}}', $response->getContent());
    }

    public function testHistoryReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json', 'accessToken' => 'invalid-token'];
        $request = Request::create(
            '/charging-sessions/history',
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Mock the validator to pass validation
        $violationList = $this->createMock(ConstraintViolationListInterface::class);
        $violationList->method('count')->willReturn(0);
        
        $validator = $this->getMockBuilder(ValidatorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
        $validator->method('validate')->willReturn($violationList);
        
        // Create a new controller with our mocked validator
        $controller = new ChargingSessionController($validator);
        $controller->setContainer(static::getContainer());

        // Use the correct method name 'getHistory' instead of 'getSessionHistory'
        $this->ChargingSessionManager->method('getHistory')
            ->willReturn(new ErrorResponse('Unauthorized', Response::HTTP_UNAUTHORIZED));

        $response = $controller->history($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertStringContainsString('Unauthorized', $response->getContent());
    }

    public function testHistoryReturnsValidationError()
    {
        $request = Request::create(
            '/charging-sessions/history',
            Request::METHOD_GET
        );

        // Create a validator mock that will validate the token
        $validator = $this->getMockBuilder(ValidatorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $constraint = new ConstraintViolation(
            'Access token is required.',
            null,
            [],
            null,
            'token',
            null
        );

        $violationList = new ConstraintViolationList([$constraint]);
        $validator->method('validate')->willReturn($violationList);

        // Create a new controller with our mocked validator
        $controller = new ChargingSessionController($validator);
        $controller->setContainer(static::getContainer());

        $response = $controller->history($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('Access token is required', $response->getContent());
    }

    // ============== Charge Session Data =================

    public function testChargeSessionDataReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json', 'accessToken' => 'valid-token'];
        $sessionId = 'session-123';
        $request = Request::create(
            "/charging/session/$sessionId",
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        $this->ChargingSessionManager->method('getSessionDetailsValidation')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            'data' => [
                'id' => $sessionId,
                'startTime' => '2023-01-15T10:00:00Z',
                'status' => 'active',
                'energy' => 15.5,
                'duration' => 3600
            ]
        ];

        $this->ChargingSessionManager->method('chargeSessionData')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->chargingSessionController->chargeSessionData($sessionId, $request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"data":{"id":"session-123","startTime":"2023-01-15T10:00:00Z","status":"active","energy":15.5,"duration":3600}}}', $response->getContent());
    }

    public function testChargeSessionDataReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json', 'accessToken' => 'valid-token'];
        $sessionId = 'invalid-session';
        $request = Request::create(
            "/charging/session/$sessionId",
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        $this->ChargingSessionManager->method('getSessionDetailsValidation')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->ChargingSessionManager->method('chargeSessionData')
            ->willReturn(new ErrorResponse('Session not found', Response::HTTP_NOT_FOUND));

        $response = $this->chargingSessionController->chargeSessionData($sessionId, $request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());
        $this->assertStringContainsString('Session not found', $response->getContent());
    }

    public function testChargeSessionDataReturnsValidationError()
    {
        $headers = ['Content-Type' => 'application/json'];
        $sessionId = 'session-123';
        $request = Request::create(
            "/charging/session/$sessionId",
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        $constraint = new ConstraintViolation(
            'Access token is required.',
            null,
            [],
            null,
            'token',
            null
        );

        $this->ChargingSessionManager->method('getSessionDetailsValidation')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->chargingSessionController->chargeSessionData($sessionId, $request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('Access token is required', $response->getContent());
    }

    // ============== Account Delete =================

    public function testAccountDeleteSuccessfully()
    {
        $request = Request::create(
            '/user/unsubscribe', 
            'POST',              
            [],                  
            [],                  
            [],                  
            ['accessToken' => 'valid-token'], 
            json_encode([        
                'reasonId' => '123',
                'otherReason' => 'Testing delete account'
            ])
        );
    
        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);

        $responseArrayFormat->method('toArray')->willReturn([
            'content' => ['data' => true],
            'code' => 200
        ]);
        $this->ChargingSessionManager
            ->method('deleteAccount')
            ->willReturn($responseArrayFormat);

        $response = $this->chargingSessionController->accountDelete($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(
            ['data' => true],
            json_decode($response->getContent(), true)
        );
    }

    public function testAccountDeleteValidationError()
    {
        $request = Request::create(
            '/user/unsubscribe', 
            'POST',              
            [],                  
            [],                  
            [],                  
            ['accessToken' => 'valid-token'], 
            json_encode([        
                'reasonId' => '' 
            ])
        );

        $violation = new ConstraintViolation(
            'This field is required.', 
            null,                      
            [],                        
            null,                      
            'reasonId',                
            ''                         
        );

        $violations = new ConstraintViolationList([$violation]);

        $this->ChargingSessionManager
            ->method('validateAccountDeleteData')
            ->willReturn($violations);
        $response = $this->chargingSessionController->accountDelete($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertEquals(
            [
                'error' => [
                    'message' => 'validation_failed',
                    'errors' => [
                        'reasonId' => 'This field is required.'
                    ]
                ]
            ],
            json_decode($response->getContent(), true)
        );
    }

    public function testWalletDetailSuccess(): void
    {
        // Arrange
        $request = Request::create(
            '/user/wallet',
            'GET',
            [],
            [],
            [],
            ['HTTP_accessToken' => 'valid-token']
        );

        // Mock ConstraintViolationListInterface
        $violationListMock = $this->createMock(ConstraintViolationListInterface::class);

        $this->ChargingSessionManager
            ->method('walletDataValidation')
            ->willReturn($violationListMock);

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn([
            'content' => ['totalCredits' => 0],
            'code' => 200
        ]);
        $this->ChargingSessionManager
            ->method('getWalletDetail')
            ->willReturn($responseArrayFormat);
        $response = $this->chargingSessionController->walletDetail($request, $this->ChargingSessionManager);
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(
            ['totalCredits' => 0],
            json_decode($response->getContent(), true)
        );
    }

    public function testWalletDetailError(): void
    {
        // Arrange
        $request = Request::create(
            '/user/wallet',
            'GET',
            [],
            [],
            [],
            ['HTTP_accessToken' => 'valid-token']
        );

        // Mock ConstraintViolationListInterface
        $violationListMock = $this->createMock(ConstraintViolationListInterface::class);

        $this->ChargingSessionManager
            ->method('walletDataValidation')
            ->willReturn($violationListMock);

        $this->ChargingSessionManager
            ->method('getWalletDetail')
            ->willReturn(new ErrorResponse('Invalid value', Response::HTTP_BAD_REQUEST));
        $response = $this->chargingSessionController->walletDetail($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertEquals(["error" => ["message" => "Invalid value"]], json_decode($response->getContent(), true));
    }

    public function testWalletDetailValidationError(): void
    {
        // Arrange
        $request = Request::create(
            '/user/wallet',
            'GET',
            [],
            [],
            [],
            ['HTTP_accessToken' => 'invalid-token']
        );

        // Mock ConstraintViolation
        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            [],
            null,
            'accessToken',
            null
        );
        $violationList = new ConstraintViolationList([$constraint]);

        // Mock ChargingSessionManager to return validation errors
        $this->ChargingSessionManager
            ->method('walletDataValidation')
            ->willReturn($violationList);

        $response = $this->chargingSessionController->walletDetail($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertEquals(
            ["error" => [
                "message" => "validation_failed",
                "errors" => [
                "accessToken" => "This value should not be blank."]
            ]],
            json_decode($response->getContent(), true)
        );
    }
}
