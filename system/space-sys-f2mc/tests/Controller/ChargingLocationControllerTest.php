<?php
namespace App\Tests\Controller;

use App\Controller\ChargingLocationController;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\ChargingLocationManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ChargingLocationControllerTest extends WebTestCase
{
    private $chargingLocationController;
    private $chargingLocationManager;

    protected function setUp(): void
    {
        $validator = $this->createMock(ValidatorInterface::class);
        $this->chargingLocationManager = $this->createMock(ChargingLocationManager::class);
        $this->chargingLocationController = new ChargingLocationController($validator);
        $this->chargingLocationController->setContainer(static::getContainer());
    }

    protected function tearDown(): void
    {
        unset($this->chargingLocationController);
        unset($this->chargingLocationManager);
    }

    public function testGetLocationsReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['filters' => [['field' => 'status', 'operator' => 'eq', 'value' => 'active']]]);
        $request = Request::create(
            '/v1/charging/locations?sortBy=ASC&orderBy=name&offset=0&limit=10&fromDate=2023-01-01&toDate=2023-12-31',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            $data
        );

        $this->chargingLocationManager->method('validateChargingLocationData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            'data' => [
                [
                    'id' => 1,
                    'name' => 'Charging Station 1',
                    'address' => '123 Main St',
                    'city' => 'New York',
                    'state' => 'NY',
                    'zipCode' => '10001',
                    'status' => 'active'
                ]
            ],
            'total' => 1
        ];

        $this->chargingLocationManager->method('getLocations')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(json_encode(['success' => $expectedResponse]), $response->getContent());
    }

    public function testGetLocationsReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['filters' => [['field' => 'status', 'operator' => 'eq', 'value' => 'active']]]);
        $request = Request::create(
            '/v1/charging/locations?sortBy=ASC&orderBy=name&offset=0&limit=10',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            $data
        );

        $this->chargingLocationManager->method('validateChargingLocationData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $errorMessage = 'Failed to retrieve locations';
        $this->chargingLocationManager->method('getLocations')
            ->willReturn(new ErrorResponse($errorMessage, Response::HTTP_BAD_REQUEST));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertEquals(json_encode(['error' => ['message' => $errorMessage]]), $response->getContent());
    }

    public function testGetLocationsReturnsValidationError()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['filters' => [['field' => 'status', 'operator' => 'invalid', 'value' => 'active']]]);
        $request = Request::create(
            '/v1/charging/locations?sortBy=INVALID&orderBy=name',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            $data
        );

        $constraint = new ConstraintViolation(
            'This value is not valid.',
            null,
            [],
            null,
            'sortBy',
            'INVALID'
        );

        $this->chargingLocationManager->method('validateChargingLocationData')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('This value is not valid.', $response->getContent());
    }

    public function testGetLocationsWithEmptyParameters()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['filters' => []]);
        $request = Request::create(
            '/v1/charging/locations',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            $data
        );

        $this->chargingLocationManager->method('validateChargingLocationData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            'data' => [],
            'total' => 0
        ];

        $this->chargingLocationManager->method('getLocations')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(json_encode(['success' => $expectedResponse]), $response->getContent());
    }

    public function testGetLocationsWithMultipleValidationErrors()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['filters' => [['field' => '', 'operator' => '', 'value' => '']]]);
        $request = Request::create(
            '/v1/charging/locations?sortBy=INVALID',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            $data
        );
        $request->query->set('limit', null);

        $this->chargingLocationManager->expects($this->once())
            ->method('validateChargingLocationData')
            ->with(
                $this->anything(),
                'INVALID',
                $this->anything(),
                $this->anything(),
                $this->anything(),
                $this->anything(),
                $this->anything()
            )
            ->willReturn(new ConstraintViolationList([
                new ConstraintViolation('Sort direction is invalid.', null, [], null, 'sortBy', 'INVALID'),
                new ConstraintViolation('Limit must be a number.', null, [], null, 'limit', 'abc'),
                new ConstraintViolation('Field cannot be empty.', null, [], null, 'filters[0].field', '')
            ]));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('Sort direction is invalid', $response->getContent());
        $this->assertStringContainsString('Limit must be a number', $response->getContent());
        $this->assertStringContainsString('Field cannot be empty', $response->getContent());
    }

    public function testGetLocationsWithServerError()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['filters' => [['field' => 'status', 'operator' => 'eq', 'value' => 'active']]]);
        $request = Request::create(
            '/v1/charging/locations',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            $data
        );

        $this->chargingLocationManager->method('validateChargingLocationData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->chargingLocationManager->method('getLocations')
            ->willReturn(new ErrorResponse('Internal server error', Response::HTTP_INTERNAL_SERVER_ERROR));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        $this->assertStringContainsString('Internal server error', $response->getContent());
    }

    public function testGetLocationsWithMalformedJson()
    {
        $this->expectException(\Symfony\Component\HttpFoundation\Exception\JsonException::class);

        $headers = ['Content-Type' => 'application/json'];
        $request = Request::create(
            '/v1/charging/locations',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            '{malformed json'
        );

        $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);
    }

    public function testGetLocationsWithPaginationAndSorting()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['filters' => [['field' => 'city', 'operator' => 'eq', 'value' => 'New York']]]);
        $request = Request::create(
            '/v1/charging/locations?sortBy=DESC&orderBy=name&offset=10&limit=5',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            $data
        );

        $this->chargingLocationManager->method('validateChargingLocationData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            'data' => [
                [
                    'id' => 15,
                    'name' => 'Charging Station Z',
                    'city' => 'New York'
                ],
                [
                    'id' => 14,
                    'name' => 'Charging Station Y',
                    'city' => 'New York'
                ]
            ],
            'total' => 25
        ];

        $this->chargingLocationManager->method('getLocations')
            ->with(
                $this->anything(),
                'DESC',
                'name',
                '10',
                '5',
                null,
                null
            )
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(json_encode(['success' => $expectedResponse]), $response->getContent());
    }

    public function testGetLocationsWithDateFiltering()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['filters' => []]);
        $request = Request::create(
            '/v1/charging/locations?fromDate=2023-01-01&toDate=2023-12-31',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            $data
        );

        $this->chargingLocationManager->method('validateChargingLocationData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            'data' => [
                [
                    'id' => 1,
                    'name' => 'Charging Station 1',
                    'createdAt' => '2023-06-15T10:30:00Z'
                ]
            ],
            'total' => 1
        ];

        $this->chargingLocationManager->method('getLocations')
            ->with(
                $this->anything(),
                null,
                null,
                null,
                null,
                '2023-01-01',
                '2023-12-31'
            )
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->chargingLocationController->getLocations($request, $this->chargingLocationManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(json_encode(['success' => $expectedResponse]), $response->getContent());
    }
}

