<?php

namespace App\Tests\Controller;

use App\Controller\PaymentController;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Manager\PaymentManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class PaymentControllerTest extends WebTestCase
{
    private $paymentController;
    private $paymentManager;
    private $validator;

    protected function setUp(): void
    {
        parent::setUp();
        $this->paymentManager = $this->createMock(PaymentManager::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->paymentController = new PaymentController($this->paymentManager);
        
        // Create a container with the validator service
        $container = static::getContainer();
        $this->paymentController->setContainer($container);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        
        unset($this->paymentController);
        unset($this->paymentManager);
        unset($this->validator);
    }  

    // ============== Add URL Tests =================

    public function testAddUrlReturnsSuccessfulResponse()
    {
        $server = ['HTTP_ACCESSTOKEN' => 'valid-token'];
        $request = new Request([], [], [], [], [], $server);

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn([
            'content' => ['url' => 'https://payment.example.com/add-method/123'],
            'code' => Response::HTTP_OK
        ]);

        $this->paymentManager->expects($this->once())
            ->method('addUrl')
            ->with('valid-token')
            ->willReturn($responseArrayFormat);

        $response = $this->paymentController->addUrl($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(
            json_encode(['url' => 'https://payment.example.com/add-method/123']),
            $response->getContent()
        );
    }
    
    public function testAddUrlReturnsErrorResponse()
    {
        $headers = ['HTTP_ACCESSTOKEN' => 'invalid-token'];
        $request = new Request([], [], [], [], [], $headers);

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn([
            'content' => ['error' => ['message' => 'Unauthorized access']],
            'code' => Response::HTTP_UNAUTHORIZED
        ]);

        $this->paymentManager->expects($this->once())
            ->method('addUrl')
            ->with('invalid-token')
            ->willReturn($responseArrayFormat);

        $response = $this->paymentController->addUrl($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertEquals('{"error":{"message":"Unauthorized access"}}', $response->getContent());
    }

    public function testAddUrlWithMissingToken()
    {
        $headers = ['Content-Type' => 'application/json'];
        $request = new Request([], [], [], [], [], $headers);

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn([
            'content' => ['error' => ['message' => 'Access token is required']],
            'code' => Response::HTTP_BAD_REQUEST
        ]);

        $this->paymentManager->expects($this->once())
            ->method('addUrl')
            ->with('')
            ->willReturn($responseArrayFormat);

        $response = $this->paymentController->addUrl($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertEquals('{"error":{"message":"Access token is required"}}', $response->getContent());
    }

    public function testAddUrlWithServerError()
    {
        $headers = ['HTTP_ACCESSTOKEN' => 'valid-token'];
        $request = new Request([], [], [], [], [], $headers);

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn([
            'content' => ['error' => ['message' => 'Internal server error']],
            'code' => Response::HTTP_INTERNAL_SERVER_ERROR
        ]);

        $this->paymentManager->expects($this->once())
            ->method('addUrl')
            ->with('valid-token')
            ->willReturn($responseArrayFormat);

        $response = $this->paymentController->addUrl($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        $this->assertEquals('{"error":{"message":"Internal server error"}}', $response->getContent());
    }

    // ============== Payment History Tests =================

    public function testGetPaymentHistoryReturnsSuccessfulResponse()
    {
        $headers = ['HTTP_ACCESSTOKEN' => 'valid-token'];
        $query = ['page' => '1', 'limit' => '10'];
        $request = new Request($query, [], [], [], [], $headers);

        $expectedData = [
            'content' => [
                'success' => [
                    [
                        'id' => 'tx123',
                        'type' => 'payment',
                        'transactionTypeId' => 1,
                        'transactionType' => ['name' => 'Charging Session'],
                        'userId' => 'user123',
                        'amount' => 15.50,
                        'invoiceId' => 'inv123',
                        'transactionDate' => '2023-01-15T10:00:00Z'
                    ]
                ]
            ],
            'code' => Response::HTTP_OK
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($expectedData);

        $this->paymentManager->expects($this->once())
            ->method('getPaymentHistory')
            ->with('valid-token')
            ->willReturn($responseArrayFormat);

        $response = $this->paymentController->getPaymentHistory($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertJson($response->getContent());
        $this->assertStringContainsString('tx123', $response->getContent());
    }

    public function testGetPaymentHistoryReturnsErrorResponse()
    {
        $headers = ['HTTP_ACCESSTOKEN' => 'invalid-token'];
        $request = new Request([], [], [], [], [], $headers);

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn([
            'content' => ['error' => ['message' => 'Unauthorized access']],
            'code' => Response::HTTP_UNAUTHORIZED
        ]);

        $this->paymentManager->expects($this->once())
            ->method('getPaymentHistory')
            ->with('invalid-token')
            ->willReturn($responseArrayFormat);

        $response = $this->paymentController->getPaymentHistory($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertEquals('{"error":{"message":"Unauthorized access"}}', $response->getContent());
    }

    public function testGetPaymentHistoryWithInvalidParameters()
    {
        $server = ['HTTP_ACCESSTOKEN' => 'valid-token'];
        $query = ['page' => 'invalid', 'limit' => 'invalid'];
        $request = new Request($query, [], [], [], [], $server);

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn([
            'content' => ['error' => ['message' => 'Invalid parameters']],
            'code' => Response::HTTP_BAD_REQUEST
        ]);

        $this->paymentManager->expects($this->once())
            ->method('getPaymentHistory')
            ->with('valid-token')
            ->willReturn($responseArrayFormat);

        $response = $this->paymentController->getPaymentHistory($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertEquals('{"error":{"message":"Invalid parameters"}}', $response->getContent());
    }

   
}
