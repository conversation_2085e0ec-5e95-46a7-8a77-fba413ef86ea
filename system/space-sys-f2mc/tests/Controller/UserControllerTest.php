<?php

namespace App\Tests\Controller;

use App\Controller\UserController;
use App\Helper\ResponseArrayFormat;
use App\Manager\UserManager;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class UserControllerTest extends TestCase
{
    private $userController;
    private $userManager;
    private $userService;
    private $validator;
    private $container;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks for dependencies
        $this->userManager = $this->createMock(UserManager::class);
        $this->userService = $this->createMock(UserService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->container = $this->createMock(ContainerInterface::class);

        // Configure container mock
        $this->container->method('has')->willReturn(true);
        $this->container->method('get')
            ->willReturnCallback(function($service) {
                if ($service === 'validator') {
                    return $this->validator;
                }
                if ($service === 'serializer') {
                    // Create a mock serializer that returns JSON
                    $serializer = $this->createMock(\Symfony\Component\Serializer\SerializerInterface::class);
                    $serializer->method('serialize')
                        ->willReturnCallback(function ($data, $format, $context = []) {
                            // Ignore format and context, just return JSON
                            return json_encode($data);
                        });
                    return $serializer;
                }
                return null;
            });

        // Create controller with dependencies
        $this->userController = new UserController(
            $this->userManager,
            $this->userService,
            $this->validator
        );

        // Set container
        $this->userController->setContainer($this->container);
    }

    protected function tearDown(): void
    {
        unset($this->userController);
        unset($this->userManager);
        unset($this->userService);
        unset($this->validator);
        unset($this->container);
    }

    // ============== Registration Tests =================

    public function testRegisterReturnsSuccessfulResponse()
    {
        // Create valid user registration data
        $params = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => 12345,
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'vin' => 'ABC123456789DEF'
        ];

        $request = new Request([], [], [], [], [], [], json_encode($params));

        // Mock the userService to return no validation errors
        $this->userService->expects($this->once())
            ->method('validateUserData')
            ->with($params)
            ->willReturn(new ConstraintViolationList());

        // Create a properly structured successful response
        $responseContent = [
            'accessToken' => 'access-token-123',
            'expiresIn' => 3600,
            'idToken' => 'id-token-123',
            'refreshToken' => 'refresh-token-123',
            'tokenType' => 'Bearer',
            'consent' => 'granted',
            'profile' => [
                'id' => 'user-123',
                'firstName' => 'John',
                'lastName' => 'Doe',
                'email' => '<EMAIL>',
                'isVinAdded' => true,
                'isNewUser' => true,
                'zipCode' => '12345',
                'countryCode' => 'US',
                'country' => 'United States'
            ],
            'ishowroomRecords' => []
        ];

        $responseData = [
            'content' => $responseContent,
            'code' => Response::HTTP_OK
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($responseData);

        // Instead of mocking serialize, we'll create a partial mock of the controller
        $userController = $this->getMockBuilder(UserController::class)
            ->setConstructorArgs([$this->userManager, $this->userService, $this->validator])
            ->onlyMethods(['json'])
            ->getMock();

        $userController->method('json')
            ->willReturn(new JsonResponse($responseContent, Response::HTTP_OK));

        $userController->setContainer($this->container);

        // Mock the userManager to return a successful response
        $this->userManager->expects($this->once())
            ->method('register')
            ->with($params)
            ->willReturn($responseArrayFormat);

        $response = $userController->register($request);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('accessToken', $responseContent);
        $this->assertEquals('access-token-123', $responseContent['accessToken']);
        $this->assertArrayHasKey('profile', $responseContent);
        $this->assertEquals('John', $responseContent['profile']['firstName']);
        $this->assertEquals('Doe', $responseContent['profile']['lastName']);
    }

    public function testRegisterWithMissingRequiredFields()
    {
        // Create incomplete user registration data (missing required fields)
        $params = [
            'firstName' => 'John',
            // Missing lastName
            'email' => '<EMAIL>',
            // Missing zipCode
            'country' => 'United States',
            // Missing other required fields
        ];

        $request = new Request([], [], [], [], [], [], json_encode($params));

        // Create constraint violations for missing fields
        $violations = [
            new ConstraintViolation(
                'Last name is required.',
                null,
                [],
                null,
                'lastName',
                null
            ),
            new ConstraintViolation(
                'Zip code is required.',
                null,
                [],
                null,
                'zipCode',
                null
            )
        ];

        $violationList = new ConstraintViolationList($violations);

        // Mock the userService to return validation errors
        $this->userService->expects($this->once())
            ->method('validateUserData')
            ->with($params)
            ->willReturn($violationList);

        // Use the real controller instead of mocking
        $userController = new UserController($this->userManager, $this->userService, $this->validator);
        $userController->setContainer($this->container);

        $response = $userController->register($request);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('validation_failed', $responseContent['error']['message']);
        $this->assertArrayHasKey('errors', $responseContent['error']);
        $this->assertArrayHasKey('lastName', $responseContent['error']['errors']);
        $this->assertArrayHasKey('zipCode', $responseContent['error']['errors']);
    }

    public function testRegisterWithInvalidEmail()
    {
        // Create user registration data with invalid email
        $params = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => 'invalid-email',
            'zipCode' => 12345,
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'vin' => 'ABC123456789DEF'
        ];

        $request = new Request([], [], [], [], [], [], json_encode($params));

        // Create constraint violation for invalid email
        $violation = new ConstraintViolation(
            'This value is not a valid email address.',
            null,
            [],
            null,
            'email',
            'invalid-email'
        );

        $violationList = new ConstraintViolationList([$violation]);

        // Mock the userService to return validation errors
        $this->userService->expects($this->once())
            ->method('validateUserData')
            ->with($params)
            ->willReturn($violationList);

        // Instantiate the real controller (no method mocking)
        $userController = new UserController($this->userManager, $this->userService, $this->validator);
        $userController->setContainer($this->container);

        // Call the controller
        $response = $userController->register($request);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());

        $responseContent = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('validation_failed', $responseContent['error']['message']);
        $this->assertArrayHasKey('errors', $responseContent['error']);
        $this->assertArrayHasKey('email', $responseContent['error']['errors']);
        $this->assertEquals('This value is not a valid email address.', $responseContent['error']['errors']['email']);
    }

    public function testRegisterWithInvalidVIN()
    {
        // Create user registration data with invalid VIN
        $params = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => 12345,
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'vin' => 'INVALID' // Too short for a VIN
        ];

        $request = new Request([], [], [], [], [], [], json_encode($params));

        // Create constraint violation for invalid VIN
        $violation = new ConstraintViolation(
            'This value is not a valid VIN.',
            null,
            [],
            null,
            'vin',
            'INVALID'
        );

        $violationList = new ConstraintViolationList([$violation]);

        // Mock the userService to return validation errors
        $this->userService->expects($this->once())
            ->method('validateUserData')
            ->with($params)
            ->willReturn($violationList);

        // We don't need to create a response data structure
        // as we're using the real controller implementation

        // Instead of mocking the static method, we'll use a real controller
        // and test the full flow
        $userController = new UserController(
            $this->userManager,
            $this->userService,
            $this->validator
        );

        $userController->setContainer($this->container);

        $response = $userController->register($request);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('validation_failed', $responseContent['error']['message']);
        $this->assertArrayHasKey('errors', $responseContent['error']);
        $this->assertArrayHasKey('vin', $responseContent['error']['errors']);
    }

    public function testRegisterWithServerError()
    {
        // Create valid user registration data
        $params = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => 12345,
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'vin' => 'ABC123456789DEF'
        ];

        $request = new Request([], [], [], [], [], [], json_encode($params));

        // Mock the userService to return no validation errors
        $this->userService->expects($this->once())
            ->method('validateUserData')
            ->with($params)
            ->willReturn(new ConstraintViolationList());

        // Create a server error response
        $responseData = [
            'content' => [
                'error' => [
                    'message' => 'Internal server error occurred'
                ]
            ],
            'code' => Response::HTTP_INTERNAL_SERVER_ERROR
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($responseData);

        // Mock the userManager to return an error response
        $this->userManager->expects($this->once())
            ->method('register')
            ->with($params)
            ->willReturn($responseArrayFormat);

        $response = $this->userController->register($request);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('Internal server error occurred', $responseContent['error']['message']);
    }

    public function testRegisterWithDuplicateEmail()
    {
        // Create user registration data with email that already exists
        $params = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => 12345,
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'vin' => 'ABC123456789DEF'
        ];

        $request = new Request([], [], [], [], [], [], json_encode($params));

        // Mock the userService to return no validation errors
        $this->userService->expects($this->once())
            ->method('validateUserData')
            ->with($params)
            ->willReturn(new ConstraintViolationList());

        // Create a conflict error response
        $responseData = [
            'content' => [
                'error' => [
                    'message' => 'User with this email already exists'
                ]
            ],
            'code' => Response::HTTP_CONFLICT
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($responseData);

        // Mock the userManager to return a conflict response
        $this->userManager->expects($this->once())
            ->method('register')
            ->with($params)
            ->willReturn($responseArrayFormat);

        $response = $this->userController->register($request);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(409, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('User with this email already exists', $responseContent['error']['message']);
    }

    public function testRegisterWithInvalidClientCredentials()
    {
        // Create valid user registration data
        $params = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => 12345,
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'vin' => 'ABC123456789DEF'
        ];

        $request = new Request([], [], [], [], [], [], json_encode($params));

        // Mock the userService to return no validation errors
        $this->userService->expects($this->once())
            ->method('validateUserData')
            ->with($params)
            ->willReturn(new ConstraintViolationList());

        // Create a forbidden error response
        $responseData = [
            'content' => [
                'error' => [
                    'message' => 'Access denied due to invalid client credentials'
                ]
            ],
            'code' => Response::HTTP_FORBIDDEN
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($responseData);

        // Mock the userManager to return a forbidden response
        $this->userManager->expects($this->once())
            ->method('register')
            ->with($params)
            ->willReturn($responseArrayFormat);

        $response = $this->userController->register($request);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(403, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('Access denied due to invalid client credentials', $responseContent['error']['message']);
    }

    // ============== Refresh Token Tests =================

    public function testRefreshTokenReturnsSuccessfulResponse()
    {
        // Create valid refresh token data
        $params = [
            'refreshToken' => 'valid-refresh-token',
            'role' => 'user',
            'grantType' => 'refresh_token'
        ];

        $request = new Request([], [], [], [], [], [], json_encode($params));

        // Mock the userService to return no validation errors
        $this->userService->expects($this->once())
            ->method('validateRefreshTokenData')
            ->with($params)
            ->willReturn(new ConstraintViolationList());

        // Create a properly structured successful response
        $responseContent = [
            'accessToken' => 'new-access-token-123',
            'expiresIn' => 3600,
            'idToken' => 'new-id-token-123',
            'tokenType' => 'Bearer',
            'consent' => 'granted'
        ];

        $responseData = [
            'content' => $responseContent,
            'code' => Response::HTTP_OK
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($responseData);

        // Mock the userManager to return a successful response
        $this->userManager->expects($this->once())
            ->method('refreshToken')
            ->with($params)
            ->willReturn($responseArrayFormat);

        $response = $this->userController->refreshToken($request);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('accessToken', $responseContent);
        $this->assertEquals('new-access-token-123', $responseContent['accessToken']);
        $this->assertArrayHasKey('expiresIn', $responseContent);
        $this->assertEquals(3600, $responseContent['expiresIn']);
    }

    public function testRefreshTokenWithMissingRefreshToken()
    {
        // Create invalid refresh token data (missing required field)
        $params = [
            // Missing refreshToken
            'role' => 'user',
            'grantType' => 'refresh_token'
        ];

        $request = new Request([], [], [], [], [], [], json_encode($params));

        // Create constraint violation for missing refresh token
        $violation = new ConstraintViolation(
            'Refresh token is required.',
            null,
            [],
            null,
            'refreshToken',
            null
        );

        $violationList = new ConstraintViolationList([$violation]);

        // Mock the userService to return validation errors
        $this->userService->expects($this->once())
            ->method('validateRefreshTokenData')
            ->with($params)
            ->willReturn($violationList);

        $response = $this->userController->refreshToken($request);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('validation_failed', $responseContent['error']['message']);
        $this->assertArrayHasKey('errors', $responseContent['error']);
        $this->assertArrayHasKey('refreshToken', $responseContent['error']['errors']);
    }

    public function testRefreshTokenWithInvalidToken()
    {
        // Create refresh token data with invalid token
        $params = [
            'refreshToken' => 'invalid-refresh-token',
            'role' => 'user',
            'grantType' => 'refresh_token'
        ];

        $request = new Request([], [], [], [], [], [], json_encode($params));

        // Mock the userService to return no validation errors
        $this->userService->expects($this->once())
            ->method('validateRefreshTokenData')
            ->with($params)
            ->willReturn(new ConstraintViolationList());

        // Create an unauthorized error response
        $responseData = [
            'content' => [
                'error' => [
                    'message' => 'Invalid refresh token'
                ]
            ],
            'code' => Response::HTTP_UNAUTHORIZED
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($responseData);

        // Mock the userManager to return an error response
        $this->userManager->expects($this->once())
            ->method('refreshToken')
            ->with($params)
            ->willReturn($responseArrayFormat);

        $response = $this->userController->refreshToken($request);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('Invalid refresh token', $responseContent['error']['message']);
    }

    // ============== Ishowroom Eligibility Tests =================

    public function testIshowroomEligibilityReturnsSuccessfulResponse()
    {
        // Create valid request data
        $email = '<EMAIL>';
        $token = 'valid-access-token';

        $content = ['email' => $email];
        $request = new Request([], [], [], [], [], ['HTTP_accessToken' => $token], json_encode($content));
        $request->headers->set('accessToken', $token);

        // Mock the validator to validate the session data
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Create a properly structured successful response
        $responseContent = [
            'success' => [
                [
                    'id' => 'credit-123',
                    'connectionOfferId' => 'offer-456',
                    'email' => '<EMAIL>',
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'status' => 'ACTIVE',
                    'benefitType' => 'CREDIT',
                    'fixedAmount' => '100'
                ]
            ]
        ];

        $responseData = [
            'content' => $responseContent,
            'code' => Response::HTTP_OK
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($responseData);

        // Mock the userManager to return a successful response
        $this->userManager->expects($this->once())
            ->method('getIshowroomEligibility')
            ->with($token, $email)
            ->willReturn($responseArrayFormat);

        $response = $this->userController->ishowroomEligibility($request, $this->userManager);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseContent);
        $this->assertIsArray($responseContent['success']);
        $this->assertCount(1, $responseContent['success']);
        $this->assertEquals('<EMAIL>', $responseContent['success'][0]['email']);
        $this->assertEquals('100', $responseContent['success'][0]['fixedAmount']);
    }

    public function testIshowroomEligibilityWithMissingToken()
    {
        // Create request with missing token
        $email = '<EMAIL>';
        $content = ['email' => $email];
        $request = new Request([], [], [], [], [], [], json_encode($content));
        // No token in headers

        // Create constraint violation for missing token
        $violation = new ConstraintViolation(
            'Token is required.',
            null,
            [],
            null,
            'token',
            null
        );

        $violationList = new ConstraintViolationList([$violation]);

        // Mock the validator to return validation errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        $response = $this->userController->ishowroomEligibility($request, $this->userManager);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('validation_failed', $responseContent['error']['message']);
        $this->assertArrayHasKey('errors', $responseContent['error']);
        $this->assertArrayHasKey('token', $responseContent['error']['errors']);
    }

    public function testIshowroomEligibilityWithInvalidEmail()
    {
        // Create request with invalid email
        $email = 'invalid-email';
        $token = 'valid-access-token';

        $content = ['email' => $email];
        $request = new Request([], [], [], [], [], ['HTTP_accessToken' => $token], json_encode($content));
        $request->headers->set('accessToken', $token);

        // Create constraint violation for invalid email
        $violation = new ConstraintViolation(
            'This value is not a valid email address.',
            null,
            [],
            null,
            'email',
            $email
        );

        $violationList = new ConstraintViolationList([$violation]);

        // Mock the validator to return validation errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        $response = $this->userController->ishowroomEligibility($request, $this->userManager);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('validation_failed', $responseContent['error']['message']);
        $this->assertArrayHasKey('errors', $responseContent['error']);
        $this->assertArrayHasKey('email', $responseContent['error']['errors']);
    }

    // ============== Apply Ishowroom Credit Tests =================

    public function testApplyIshowroomCreditReturnsSuccessfulResponse()
    {
        // Create valid request data
        $id = 'credit-123';
        $token = 'valid-access-token';

        $content = ['id' => $id];
        $request = new Request([], [], [], [], [], ['HTTP_accessToken' => $token], json_encode($content));
        $request->headers->set('accessToken', $token);

        // Mock the validator to validate the data
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Create a properly structured successful response
        $responseContent = [
            'success' => [
                'message' => 'The credit applied successfully.',
                'name' => 'APPLY_CREDIT_SUCCESS'
            ]
        ];

        $responseData = [
            'content' => $responseContent,
            'code' => Response::HTTP_OK
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($responseData);

        // Mock the userManager to return a successful response
        $this->userManager->expects($this->once())
            ->method('applyIshowroomCredit')
            ->with($token, $id)
            ->willReturn($responseArrayFormat);

        $response = $this->userController->applyIshowroomCredit($request, $this->userManager, $this->validator);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseContent);
        $this->assertEquals('The credit applied successfully.', $responseContent['success']['message']);
        $this->assertEquals('APPLY_CREDIT_SUCCESS', $responseContent['success']['name']);
    }

    public function testApplyIshowroomCreditWithMissingId()
    {
        // Create request with missing ID
        $token = 'valid-access-token';

        $content = []; // No ID
        $request = new Request([], [], [], [], [], ['HTTP_accessToken' => $token], json_encode($content));
        $request->headers->set('accessToken', $token);

        // Create constraint violation for missing ID
        $violation = new ConstraintViolation(
            'ID is required.',
            null,
            [],
            null,
            'id',
            null
        );

        $violationList = new ConstraintViolationList([$violation]);

        // Mock the validator to return validation errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        $response = $this->userController->applyIshowroomCredit($request, $this->userManager, $this->validator);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('validation_failed', $responseContent['error']['message']);
        $this->assertArrayHasKey('errors', $responseContent['error']);
        $this->assertArrayHasKey('id', $responseContent['error']['errors']);
    }

    public function testApplyIshowroomCreditWithException()
    {
        // Create valid request data
        $id = 'credit-123';
        $token = 'valid-access-token';

        $content = ['id' => $id];
        $request = new Request([], [], [], [], [], ['HTTP_accessToken' => $token], json_encode($content));
        $request->headers->set('accessToken', $token);

        // Mock the validator to validate the data
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock the userManager to throw an exception
        $this->userManager->expects($this->once())
            ->method('applyIshowroomCredit')
            ->with($token, $id)
            ->willThrowException(new \Exception('Service unavailable'));

        $response = $this->userController->applyIshowroomCredit($request, $this->userManager, $this->validator);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('Service unavailable', $responseContent['error']);
    }

    // ============== Account Link Tests =================

    public function testAccountLinkReturnsSuccessfulResponse()
    {
        // Create valid request data
        $providerShortCode = 'provider-123';
        $accessToken = 'valid-access-token';

        $request = new Request([], [], [], [], [], ['HTTP_accessToken' => $accessToken]);
        $request->headers->set('accessToken', $accessToken);

        // Mock the validator to validate the session data
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Create a properly structured successful response
        $responseContent = [
            'success' => [
                'url' => 'https://example.com/link/provider-123'
            ]
        ];

        $responseData = [
            'content' => $responseContent,
            'code' => Response::HTTP_OK
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($responseData);

        // Mock the userManager to return a successful response
        $this->userManager->expects($this->once())
            ->method('getAccountLink')
            ->with($providerShortCode, $accessToken)
            ->willReturn($responseArrayFormat);

        $response = $this->userController->accountLink($providerShortCode, $request, $this->userManager);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseContent);
        $this->assertArrayHasKey('url', $responseContent['success']);
        $this->assertEquals('https://example.com/link/provider-123', $responseContent['success']['url']);
    }

    public function testAccountLinkWithMissingAccessToken()
    {
        // Create request with missing access token
        $providerShortCode = 'provider-123';
        $request = new Request(); // No access token in headers

        // Create constraint violation for missing access token
        $violation = new ConstraintViolation(
            'Access token is required.',
            null,
            [],
            null,
            'accessToken',
            null
        );

        $violationList = new ConstraintViolationList([$violation]);

        // Mock the validator to return validation errors
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        $response = $this->userController->accountLink($providerShortCode, $request, $this->userManager);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('validation_failed', $responseContent['error']['message']);
        $this->assertArrayHasKey('errors', $responseContent['error']);
        $this->assertArrayHasKey('accessToken', $responseContent['error']['errors']);
    }

    public function testAccountLinkWithInvalidProvider()
    {
        // Create valid request data but with invalid provider
        $providerShortCode = 'invalid-provider';
        $accessToken = 'valid-access-token';

        $request = new Request([], [], [], [], [], ['HTTP_accessToken' => $accessToken]);
        $request->headers->set('accessToken', $accessToken);

        // Mock the validator to validate the session data
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Create an error response
        $responseContent = [
            'error' => [
                'message' => 'Provider not found'
            ]
        ];

        $responseData = [
            'content' => $responseContent,
            'code' => Response::HTTP_NOT_FOUND
        ];

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn($responseData);

        // Mock the userManager to return an error response
        $this->userManager->expects($this->once())
            ->method('getAccountLink')
            ->with($providerShortCode, $accessToken)
            ->willReturn($responseArrayFormat);

        $response = $this->userController->accountLink($providerShortCode, $request, $this->userManager);

        // Assert response is as expected
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('Provider not found', $responseContent['error']['message']);
    }
}
