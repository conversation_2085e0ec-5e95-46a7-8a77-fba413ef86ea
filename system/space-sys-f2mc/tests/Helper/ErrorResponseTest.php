<?php

namespace App\Tests\Helper;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use stdClass;

class ErrorResponseTest extends TestCase
{
    /**
     * Test that ErrorResponse implements ResponseArrayFormat interface
     */
    public function testImplementsResponseArrayFormat(): void
    {
        $errorResponse = new ErrorResponse('Test error');
        $this->assertInstanceOf(ResponseArrayFormat::class, $errorResponse);
    }

    /**
     * Test constructor with string error message
     */
    public function testConstructorWithStringError(): void
    {
        $errorMessage = 'Test error message';
        $errorResponse = new ErrorResponse($errorMessage);

        $result = $errorResponse->toArray();

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertEquals($errorMessage, $result['content']['error']['message']);
        $this->assertArrayNotHasKey('errors', $result['content']['error']);
    }

    /**
     * Test constructor with array of errors
     */
    public function testConstructorWithArrayErrors(): void
    {
        $errors = [
            'field1' => 'Error in field 1',
            'field2' => 'Error in field 2'
        ];
        $errorResponse = new ErrorResponse($errors);

        $result = $errorResponse->toArray();

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertEquals('Bad Request', $result['content']['error']['message']);
        $this->assertEquals($errors, $result['content']['error']['errors']);
    }

    /**
     * Test constructor with custom HTTP code
     */
    public function testConstructorWithCustomCode(): void
    {
        $errorMessage = 'Not Found';
        $customCode = Response::HTTP_NOT_FOUND;
        $errorResponse = new ErrorResponse($errorMessage, $customCode);

        $result = $errorResponse->toArray();

        $this->assertEquals($customCode, $result['code']);
        $this->assertEquals($errorMessage, $result['content']['error']['message']);
    }

    /**
     * Test constructor with object that will be JSON encoded
     */
    public function testConstructorWithObject(): void
    {
        $errorObject = new stdClass();
        $errorObject->message = 'Object error message';
        $errorObject->code = 123;

        $errorResponse = new ErrorResponse($errorObject);

        $result = $errorResponse->toArray();

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertEquals(json_encode($errorObject), $result['content']['error']['message']);
    }

    /**
     * Test setCode method
     */
    public function testSetCode(): void
    {
        $errorResponse = new ErrorResponse('Test error');
        $newCode = Response::HTTP_UNAUTHORIZED;

        $returnValue = $errorResponse->setCode($newCode);
        $result = $errorResponse->toArray();

        // Test method chaining return value
        $this->assertSame($errorResponse, $returnValue);
        // Test the code was updated
        $this->assertEquals($newCode, $result['code']);
    }

    /**
     * Test setMessage method
     */
    public function testSetMessage(): void
    {
        $errorResponse = new ErrorResponse('Original message');
        $newMessage = 'Updated error message';

        $returnValue = $errorResponse->setMessage($newMessage);
        $result = $errorResponse->toArray();

        // Test method chaining return value
        $this->assertSame($errorResponse, $returnValue);
        // Test the message was updated
        $this->assertEquals($newMessage, $result['content']['error']['message']);
    }

    /**
     * Test setErrors method
     */
    public function testSetErrors(): void
    {
        $errorResponse = new ErrorResponse('Test error');
        $errors = ['field' => 'Field error'];

        $returnValue = $errorResponse->setErrors($errors);
        $result = $errorResponse->toArray();

        // Test method chaining return value
        $this->assertSame($errorResponse, $returnValue);
        // Test the errors were updated
        $this->assertEquals($errors, $result['content']['error']['errors']);
    }

    /**
     * Test toArray method with both message and errors
     */
    public function testToArrayWithMessageAndErrors(): void
    {
        $errorMessage = 'Validation failed';
        $errors = ['field1' => 'Invalid value'];

        $errorResponse = new ErrorResponse($errorMessage);
        $errorResponse->setErrors($errors);

        $result = $errorResponse->toArray();

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertEquals($errorMessage, $result['content']['error']['message']);
        $this->assertEquals($errors, $result['content']['error']['errors']);
    }

    /**
     * Test code correction for values less than 400
     */
    public function testCodeCorrectionForInvalidValues(): void
    {
        $errorResponse = new ErrorResponse('Test error', 200);
        $result = $errorResponse->toArray();

        // Code should be corrected to 400 (BAD_REQUEST) if it's less than 400
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result['code']);
    }

    /**
     * Test with empty error message
     */
    public function testWithEmptyErrorMessage(): void
    {
        // if ($this->message) which will be false for an empty string
        $errorResponse = new ErrorResponse('');
        $result = $errorResponse->toArray();

        // The message key should not be present in the response for empty strings
        $this->assertEmpty($result['content']['error']);
    }

    /**
     * Test with empty errors array
     */
    public function testWithEmptyErrorsArray(): void
    {
        // if ($this->errors) which will be false for an empty array
        $errorResponse = new ErrorResponse([]);
        $result = $errorResponse->toArray();
        
        // But the default message should be set
        $this->assertEquals('Bad Request', $result['content']['error']['message']);
        $this->assertArrayNotHasKey('errors', $result['content']['error']);
    }

    /**
     * Test with null values
     */
    public function testWithNullValues(): void
    {
        $errorResponse = new ErrorResponse('Test error');
        $errorResponse->setMessage(null);
        $errorResponse->setErrors(null);

        $result = $errorResponse->toArray();

        // Null message and errors should not be included in the response
        $this->assertArrayNotHasKey('message', $result['content']['error']);
        $this->assertArrayNotHasKey('errors', $result['content']['error']);
    }

    /**
     * Test with extreme HTTP code values
     */
    public function testWithExtremeHttpCodes(): void
    {
        // Test with minimum valid HTTP error code
        $errorResponse1 = new ErrorResponse('Test error', 400);
        $result1 = $errorResponse1->toArray();
        $this->assertEquals(400, $result1['code']);

        // Test with maximum HTTP code
        $errorResponse2 = new ErrorResponse('Test error', 599);
        $result2 = $errorResponse2->toArray();
        $this->assertEquals(599, $result2['code']);
    }

    /**
     * Test with complex nested error arrays
     */
    public function testWithComplexNestedErrorArrays(): void
    {
        $complexErrors = [
            'user' => [
                'name' => 'Name is required',
                'email' => 'Invalid email format'
            ],
            'payment' => [
                'card' => [
                    'number' => 'Invalid card number',
                    'expiry' => 'Card has expired'
                ]
            ]
        ];

        $errorResponse = new ErrorResponse($complexErrors);
        $result = $errorResponse->toArray();

        $this->assertEquals($complexErrors, $result['content']['error']['errors']);
    }

    /**
     * Test with special characters in error messages
     */
    public function testWithSpecialCharactersInErrorMessages(): void
    {
        $specialCharsMessage = 'Error with special chars: áéíóú ñ & < > " \' / \\ ';
        $errorResponse = new ErrorResponse($specialCharsMessage);

        $result = $errorResponse->toArray();

        $this->assertEquals($specialCharsMessage, $result['content']['error']['message']);
    }
}
