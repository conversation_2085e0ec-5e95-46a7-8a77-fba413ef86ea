<?php

namespace App\Tests\Helper;

use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class WSResponseTest extends TestCase
{
    /**
     * Test constructor with valid parameters
     */
    public function testConstructor(): void
    {
        $code = Response::HTTP_OK;
        $data = ['key' => 'value'];
        
        $response = new WSResponse($code, $data);
        
        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }
    
    /**
     * Test setCode and getCode methods
     */
    public function testSetAndGetCode(): void
    {
        $response = new WSResponse(Response::HTTP_OK, []);
        
        $newCode = Response::HTTP_CREATED;
        $returnValue = $response->setCode($newCode);
        
        // Test method chaining return value
        $this->assertSame($response, $returnValue);
        // Test the code was actually changed
        $this->assertEquals($newCode, $response->getCode());
    }
    
    /**
     * Test setData and getData methods
     */
    public function testSetAndGetData(): void
    {
        $initialData = ['initial' => 'data'];
        $response = new WSResponse(Response::HTTP_OK, $initialData);
        
        // Verify initial data
        $this->assertEquals($initialData, $response->getData());
        
        // Test with array data
        $arrayData = ['key1' => 'value1', 'key2' => 'value2'];
        $returnValue = $response->setData($arrayData);
        
        // Test method chaining return value
        $this->assertSame($response, $returnValue);
        // Test the data was actually changed
        $this->assertEquals($arrayData, $response->getData());
        
        // Test with string data
        $stringData = 'string data';
        $response->setData($stringData);
        $this->assertEquals($stringData, $response->getData());
        
        // Test with null data
        $response->setData(null);
        $this->assertNull($response->getData());
        
        // Test with object data
        $objectData = new \stdClass();
        $objectData->property = 'value';
        $response->setData($objectData);
        $this->assertEquals($objectData, $response->getData());
    }
    
    /**
     * Test with different HTTP status codes
     */
    public function testWithDifferentStatusCodes(): void
    {
        $testCases = [
            Response::HTTP_OK,
            Response::HTTP_CREATED,
            Response::HTTP_ACCEPTED,
            Response::HTTP_BAD_REQUEST,
            Response::HTTP_UNAUTHORIZED,
            Response::HTTP_FORBIDDEN,
            Response::HTTP_NOT_FOUND,
            Response::HTTP_INTERNAL_SERVER_ERROR
        ];
        
        foreach ($testCases as $code) {
            $response = new WSResponse($code, []);
            $this->assertEquals($code, $response->getCode());
        }
    }
    
    /**
     * Test with complex nested data structures
     */
    public function testWithComplexData(): void
    {
        $complexData = [
            'user' => [
                'id' => 123,
                'name' => 'Test User',
                'roles' => ['ROLE_USER', 'ROLE_ADMIN']
            ],
            'vehicles' => [
                [
                    'id' => 1,
                    'make' => 'Tesla',
                    'model' => 'Model 3'
                ],
                [
                    'id' => 2,
                    'make' => 'Jeep',
                    'model' => 'Grand Cherokee 4xe'
                ]
            ],
            'metadata' => (object)[
                'timestamp' => time(),
                'version' => '1.0.0'
            ]
        ];
        
        $response = new WSResponse(Response::HTTP_OK, $complexData);
        $this->assertEquals($complexData, $response->getData());
    }
}