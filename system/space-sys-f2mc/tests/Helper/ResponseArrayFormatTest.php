<?php

namespace App\Tests\Helper;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

/**
 * Tests for the ResponseArrayFormat interface and its implementations.
 */
class ResponseArrayFormatTest extends TestCase
{
    /**
     * Test that all implementations of ResponseArrayFormat return an array from toArray()
     */
    public function testToArrayReturnsArray(): void
    {
        // Test ErrorResponse implementation
        $errorResponse = new ErrorResponse('Test error');
        $this->assertIsArray($errorResponse->toArray());

        // Test SuccessResponse implementation
        $successResponse = new SuccessResponse(['data' => 'test']);
        $this->assertIsArray($successResponse->toArray());
    }

    /**
     * Test that all implementations include 'code' and 'content' keys in their array
     */
    public function testArrayStructure(): void
    {
        // Test ErrorResponse implementation
        $errorResponse = new ErrorResponse('Test error');
        $errorArray = $errorResponse->toArray();
        $this->assertArrayHasKey('code', $errorArray);
        $this->assertArrayHasKey('content', $errorArray);

        // Test SuccessResponse implementation
        $successResponse = new SuccessResponse(['data' => 'test']);
        $successArray = $successResponse->toArray();
        $this->assertArrayHasKey('code', $successArray);
        $this->assertArrayHasKey('content', $successArray);
    }

    /**
     * Test that implementations have the correct content structure
     */
    public function testContentStructure(): void
    {
        // Test ErrorResponse implementation
        $errorResponse = new ErrorResponse('Test error');
        $errorArray = $errorResponse->toArray();
        $this->assertArrayHasKey('error', $errorArray['content']);

        // Test SuccessResponse implementation
        $successResponse = new SuccessResponse(['data' => 'test']);
        $successArray = $successResponse->toArray();
        $this->assertArrayHasKey('success', $successArray['content']);
    }

    /**
     * Test that implementations handle different data types correctly
     */
    public function testHandlingDifferentDataTypes(): void
    {
        // Test with string
        $successString = new SuccessResponse('string data');
        $errorString = new ErrorResponse('string error');
        $this->assertIsArray($successString->toArray());
        $this->assertIsArray($errorString->toArray());

        // Test with array
        $successArray = new SuccessResponse(['key' => 'value']);
        $errorArray = new ErrorResponse(['field' => 'error']);
        $this->assertIsArray($successArray->toArray());
        $this->assertIsArray($errorArray->toArray());

        // Test with boolean
        $successBool = new SuccessResponse(true);
        $this->assertIsArray($successBool->toArray());

        // Test with null
        $successNull = new SuccessResponse('null');
        $this->assertIsArray($successNull->toArray());

        // Test with integer
        $successInt = new SuccessResponse(123);
        $this->assertIsArray($successInt->toArray());

        // Test with float
        $successFloat = new SuccessResponse(123.45);
        $this->assertIsArray($successFloat->toArray());
    }

    /**
     * Test that implementations handle custom HTTP codes correctly
     */
    public function testCustomHttpCodes(): void
    {
        // Test ErrorResponse with custom code
        $errorCustomCode = new ErrorResponse('Error', Response::HTTP_NOT_FOUND);
        $errorArray = $errorCustomCode->toArray();
        $this->assertEquals(Response::HTTP_NOT_FOUND, $errorArray['code']);

        // Test SuccessResponse with custom code
        $successCustomCode = new SuccessResponse(['data' => 'test'], Response::HTTP_CREATED);
        $successArray = $successCustomCode->toArray();
        $this->assertEquals(Response::HTTP_CREATED, $successArray['code']);
    }

    /**
     * Test that implementations handle empty data correctly
     */
    public function testEmptyData(): void
    {
        // Test SuccessResponse with empty array
        $successEmpty = new SuccessResponse([]);
        $successArray = $successEmpty->toArray();
        $this->assertEquals([], $successArray['content']['success']);

        // Test SuccessResponse with empty string
        // Note: In the actual implementation, an empty string might be treated as null
        // after json_decode attempts to parse it
        $successEmptyString = new SuccessResponse('');
        $successStringArray = $successEmptyString->toArray();
        // Just verify we get a response, without asserting the exact value
        $this->assertArrayHasKey('success', $successStringArray['content']);
    }

    /**
     * Test that implementations handle JSON strings correctly
     */
    public function testJsonStrings(): void
    {
        // Test SuccessResponse with JSON string
        $jsonString = '{"key":"value","nested":{"nestedKey":"nestedValue"}}';
        $successJson = new SuccessResponse($jsonString);
        $successArray = $successJson->toArray();

        // JSON string should be decoded to array
        $this->assertIsArray($successArray['content']['success']);
        $this->assertArrayHasKey('key', $successArray['content']['success']);
        $this->assertArrayHasKey('nested', $successArray['content']['success']);
        $this->assertEquals('value', $successArray['content']['success']['key']);
        $this->assertIsArray($successArray['content']['success']['nested']);
        $this->assertEquals('nestedValue', $successArray['content']['success']['nested']['nestedKey']);
    }

    /**
     * Test that implementations handle invalid JSON strings correctly
     */
    public function testInvalidJsonStrings(): void
    {
        // Test SuccessResponse with invalid JSON string
        $invalidJsonString = '{"key":"value",broken}';
        $successInvalidJson = new SuccessResponse($invalidJsonString);
        $successArray = $successInvalidJson->toArray();

        // Invalid JSON string should be handled gracefully
        // Just verify we get a response with the success key
        $this->assertArrayHasKey('success', $successArray['content']);
    }

    /**
     * Test method chaining on implementations
     */
    public function testMethodChaining(): void
    {
        // Test ErrorResponse method chaining
        $errorResponse = new ErrorResponse('Initial error');
        $chainedError = $errorResponse->setCode(Response::HTTP_BAD_REQUEST)
                                      ->setMessage('Updated error')
                                      ->setErrors(['field' => 'error']);

        $this->assertSame($errorResponse, $chainedError);

        // Test SuccessResponse method chaining
        $successResponse = new SuccessResponse('Initial data');
        $chainedSuccess = $successResponse->setCode(Response::HTTP_OK)
                                         ->setData('Updated data');

        $this->assertSame($successResponse, $chainedSuccess);
    }

    /**
     * Test that implementations handle complex nested data structures correctly
     */
    public function testComplexNestedData(): void
    {
        // Create a complex nested data structure
        $complexData = [
            'level1' => [
                'level2' => [
                    'level3' => [
                        'string' => 'value',
                        'int' => 123,
                        'bool' => true,
                        'array' => [1, 2, 3]
                    ]
                ],
                'sibling2' => 'value'
            ],
            'sibling1' => 'value'
        ];

        // Test SuccessResponse with complex data
        $successComplex = new SuccessResponse($complexData);
        $successArray = $successComplex->toArray();

        // Complex data should be preserved
        $this->assertEquals($complexData, $successArray['content']['success']);

        // Test ErrorResponse with complex data
        $errorComplex = new ErrorResponse($complexData);
        $errorArray = $errorComplex->toArray();

        // Complex data should be preserved in errors
        $this->assertEquals($complexData, $errorArray['content']['error']['errors']);
    }
}
