<?php

namespace App\Tests\Helper;

use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class SuccessResponseTest extends TestCase
{
    /**
     * Test that SuccessResponse implements ResponseArrayFormat interface
     */
    public function testImplementsResponseArrayFormat(): void
    {
        $successResponse = new SuccessResponse('Test data');
        $this->assertInstanceOf(ResponseArrayFormat::class, $successResponse);
    }

    /**
     * Test constructor with string data
     */
    public function testConstructorWithStringData(): void
    {
        $data = 'Test data';
        $successResponse = new SuccessResponse($data);

        $result = $successResponse->toArray();

        $this->assertEquals(Response::HTTP_OK, $result['code']);
        // String data should be attempted to be JSON decoded
        $this->assertNotEquals($data, $result['content']['success']);
    }

    /**
     * Test constructor with array data
     */
    public function testConstructorWithArrayData(): void
    {
        $data = ['key' => 'value', 'nested' => ['nestedKey' => 'nestedValue']];
        $successResponse = new SuccessResponse($data);

        $result = $successResponse->toArray();

        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertEquals($data, $result['content']['success']);
    }

    /**
     * Test constructor with custom HTTP code
     */
    public function testConstructorWithCustomCode(): void
    {
        $data = ['data' => 'test'];
        $customCode = Response::HTTP_CREATED;
        $successResponse = new SuccessResponse($data, $customCode);

        $result = $successResponse->toArray();

        $this->assertEquals($customCode, $result['code']);
        $this->assertEquals($data, $result['content']['success']);
    }

    /**
     * Test constructor with object
     */
    public function testConstructorWithObject(): void
    {
        // Since the SuccessResponse class tries to json_decode the data if it's not an array,
        // and objects can't be directly passed to json_decode, we'll use a JSON string instead
        $jsonString = '{"property":"value","nested":{"nestedProperty":"nestedValue"}}';
        $expectedData = [
            'property' => 'value',
            'nested' => [
                'nestedProperty' => 'nestedValue'
            ]
        ];

        $successResponse = new SuccessResponse($jsonString);

        $result = $successResponse->toArray();

        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertEquals($expectedData, $result['content']['success']);
    }

    /**
     * Test setCode method
     */
    public function testSetCode(): void
    {
        $successResponse = new SuccessResponse('Test data');
        $newCode = Response::HTTP_CREATED;

        $returnValue = $successResponse->setCode($newCode);

        // Test method chaining return value
        $this->assertSame($successResponse, $returnValue);
        // Test the code was updated
        $this->assertEquals($newCode, $successResponse->getCode());
    }

    /**
     * Test getCode method
     */
    public function testGetCode(): void
    {
        $code = Response::HTTP_ACCEPTED;
        $successResponse = new SuccessResponse('Test data', $code);

        $this->assertEquals($code, $successResponse->getCode());
    }

    /**
     * Test setData method
     */
    public function testSetData(): void
    {
        $successResponse = new SuccessResponse('Original data');
        $newData = ['updated' => 'data'];

        $returnValue = $successResponse->setData($newData);

        // Test method chaining return value
        $this->assertSame($successResponse, $returnValue);
        // Test the data was updated
        $this->assertEquals($newData, $successResponse->getData());
    }

    /**
     * Test getData method
     */
    public function testGetData(): void
    {
        $data = ['test' => 'data'];
        $successResponse = new SuccessResponse($data);

        $this->assertEquals($data, $successResponse->getData());
    }

    /**
     * Test toArray method with array data
     */
    public function testToArrayWithArrayData(): void
    {
        $data = ['key' => 'value'];
        $successResponse = new SuccessResponse($data);

        $result = $successResponse->toArray();

        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertEquals($data, $result['content']['success']);
    }

    /**
     * Test toArray method with JSON string data
     */
    public function testToArrayWithJsonStringData(): void
    {
        $jsonString = '{"key":"value","nested":{"nestedKey":"nestedValue"}}';
        $expectedData = [
            'key' => 'value',
            'nested' => [
                'nestedKey' => 'nestedValue'
            ]
        ];

        $successResponse = new SuccessResponse($jsonString);

        $result = $successResponse->toArray();

        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertEquals($expectedData, $result['content']['success']);
    }

    /**
     * Test toArray method with invalid JSON string data
     */
    public function testToArrayWithInvalidJsonStringData(): void
    {
        $invalidJsonString = '{"key":"value",broken}';
        $successResponse = new SuccessResponse($invalidJsonString);

        $result = $successResponse->toArray();

        $this->assertEquals(Response::HTTP_OK, $result['code']);
        
        // Invalid JSON should be handled gracefully
        $this->assertArrayHasKey('success', $result['content']);
    }

    /**
     * Test toArray method with boolean data
     */
    public function testToArrayWithBooleanData(): void
    {
        // Test with true
        $successResponseTrue = new SuccessResponse(true);
        $resultTrue = $successResponseTrue->toArray();
        $this->assertEquals(Response::HTTP_OK, $resultTrue['code']);

        // Test with false
        $successResponseFalse = new SuccessResponse(false);
        $resultFalse = $successResponseFalse->toArray();
        $this->assertEquals(Response::HTTP_OK, $resultFalse['code']);
    }

    /**
     * Test toArray method with null data
     */
    public function testToArrayWithNullData(): void
    {
        // Note: Passing null to json_decode is deprecated in newer PHP versions
        // Use a string representation of null instead
        $successResponse = new SuccessResponse('null');
        $result = $successResponse->toArray();

        $this->assertEquals(Response::HTTP_OK, $result['code']);
        // The string 'null' should be parsed as JSON null
        $this->assertNull($result['content']['success']);
    }

    /**
     * Test toArray method with numeric data
     */
    public function testToArrayWithNumericData(): void
    {
        // Test with integer
        $successResponseInt = new SuccessResponse(123);
        $resultInt = $successResponseInt->toArray();
        $this->assertEquals(Response::HTTP_OK, $resultInt['code']);

        // Test with float
        $successResponseFloat = new SuccessResponse(123.45);
        $resultFloat = $successResponseFloat->toArray();
        $this->assertEquals(Response::HTTP_OK, $resultFloat['code']);
    }

    /**
     * Test with complex nested data structures
     */
    public function testWithComplexNestedData(): void
    {
        // Create a complex nested data structure
        $complexData = [
            'level1' => [
                'level2' => [
                    'level3' => [
                        'string' => 'value',
                        'int' => 123,
                        'bool' => true,
                        'array' => [1, 2, 3]
                    ]
                ],
                'sibling2' => 'value'
            ],
            'sibling1' => 'value'
        ];

        $successResponse = new SuccessResponse($complexData);
        $result = $successResponse->toArray();

        $this->assertEquals(Response::HTTP_OK, $result['code']);
        $this->assertEquals($complexData, $result['content']['success']);
    }
}
