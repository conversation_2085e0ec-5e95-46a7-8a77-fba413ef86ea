<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\ChargingLocationService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ChargingLocationServiceTest extends TestCase
{
    private $f2mConnector;
    private $chargingLocationService;

    protected function setUp(): void
    {
        $this->f2mConnector = $this->createMock(F2mConnector::class);
        $this->chargingLocationService = new ChargingLocationService($this->f2mConnector);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->f2mConnector);
        unset($this->chargingLocationService);
    }

    /**
     * Test getLocations method with successful response
     */
    public function testGetLocationsCreatesSuccessfulResponse()
    {
        // Test data
        $payload = [
            'country' => 'US',
            'city' => 'New York'
        ];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2023-01-01';
        $toDate = '2023-12-31';

        // Expected response data
        $responseData = [
            'data' => [
                [
                    'id' => 'loc1',
                    'name' => 'Charging Station 1',
                    'address' => '123 Main St',
                    'city' => 'New York',
                    'country' => 'US',
                    'coordinates' => [
                        'latitude' => 40.7128,
                        'longitude' => -74.0060
                    ]
                ],
                [
                    'id' => 'loc2',
                    'name' => 'Charging Station 2',
                    'address' => '456 Broadway',
                    'city' => 'New York',
                    'country' => 'US',
                    'coordinates' => [
                        'latitude' => 40.7589,
                        'longitude' => -73.9851
                    ]
                ]
            ]
        ];

        // Mock the connector to return a successful response
        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'ocpi/v1/locations/all',
                [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ],
                    'query' => [
                        'sortBy' => $sortBy,
                        'orderBy' => $orderBy,
                        'offset' => $offset,
                        'limit' => $limit,
                        'fromDate' => $fromDate,
                        'toDate' => $toDate
                    ],
                    'json' => $payload
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        // Call the method
        $result = $this->chargingLocationService->getLocations(
            $payload,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test getLocations method with successful response but no data
     */
    public function testGetLocationsWithNoDataInResponse()
    {
        // Test data
        $payload = [
            'country' => 'US',
            'city' => 'New York'
        ];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2023-01-01';
        $toDate = '2023-12-31';

        // Response with no data field
        $responseData = [
            'message' => 'No locations found'
        ];

        // Mock the connector to return a response with no data
        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        // Call the method
        $result = $this->chargingLocationService->getLocations(
            $payload,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test getLocations method with error response
     */
    public function testGetLocationsHandlesErrorResponse()
    {
        // Test data
        $payload = [
            'country' => 'US',
            'city' => 'New York'
        ];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2023-01-01';
        $toDate = '2023-12-31';

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid request',
            'message' => 'The request parameters are invalid'
        ];

        // Mock the connector to return an error response
        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        // Call the method
        $result = $this->chargingLocationService->getLocations(
            $payload,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test getLocations method with null parameters
     */
    public function testGetLocationsWithNullParameters()
    {
        // Test data with null parameters
        $payload = [
            'country' => 'US'
        ];
        $sortBy = null;
        $orderBy = null;
        $offset = null;
        $limit = null;
        $fromDate = null;
        $toDate = null;

        // Expected response data
        $responseData = [
            'data' => [
                [
                    'id' => 'loc1',
                    'name' => 'Charging Station 1',
                    'country' => 'US'
                ]
            ]
        ];

        // Mock the connector to return a successful response
        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'ocpi/v1/locations/all',
                [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ],
                    'query' => [
                        'sortBy' => null,
                        'orderBy' => null,
                        'offset' => null,
                        'limit' => null,
                        'fromDate' => null,
                        'toDate' => null
                    ],
                    'json' => $payload
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        // Call the method
        $result = $this->chargingLocationService->getLocations(
            $payload,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test getLocations method with empty payload
     */
    public function testGetLocationsWithEmptyPayload()
    {
        // Empty payload
        $payload = [];
        $sortBy = 'name';
        $orderBy = 'asc';
        $offset = 0;
        $limit = 10;
        $fromDate = '2023-01-01';
        $toDate = '2023-12-31';

        // Expected response data
        $responseData = [
            'data' => [
                [
                    'id' => 'loc1',
                    'name' => 'Charging Station 1'
                ],
                [
                    'id' => 'loc2',
                    'name' => 'Charging Station 2'
                ]
            ]
        ];

        // Mock the connector to return a successful response
        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        // Call the method
        $result = $this->chargingLocationService->getLocations(
            $payload,
            $sortBy,
            $orderBy,
            $offset,
            $limit,
            $fromDate,
            $toDate
        );

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }
}
