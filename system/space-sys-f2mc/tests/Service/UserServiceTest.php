<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class UserServiceTest extends TestCase
{
    private $connector;
    private $validator;
    private $userService;
    private $clientId = 'test-client-id';
    private $clientSecret = 'test-client-secret';

    protected function setUp(): void
    {
        $this->connector = $this->createMock(F2mConnector::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->userService = new UserService(
            $this->connector,
            $this->validator,
            $this->clientId,
            $this->clientSecret
        );
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->connector);
        unset($this->validator);
        unset($this->userService);
    }

    // =============== REGISTER TESTS ===============

    /**
     * Test register method with successful response
     */
    public function testRegisterCreatesSuccessfulResponse()
    {
        // Test data
        $payload = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => 12345,
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'vin' => 'ABC123456789DEF12'
        ];

        // Expected response data
        $responseData = [
            'data' => [
                'accessToken' => 'access-token-123',
                'expiresIn' => 3600,
                'idToken' => 'id-token-123',
                'refreshToken' => 'refresh-token-123',
                'tokenType' => 'Bearer',
                'profile' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>'
                ]
            ]
        ];

        // Mock the connector to return a successful response
        $this->connector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_POST,
                'api/v1/auth/register',
                [
                    'headers' => [
                        'clientId' => $this->clientId,
                        'clientSecret' => $this->clientSecret,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => $payload
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        // Call the method
        $result = $this->userService->register($payload);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test register method with successful response but no access token
     */
    public function testRegisterWithNoAccessTokenInResponse()
    {
        // Test data
        $payload = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => 12345,
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco'
        ];

        // Response with no access token
        $responseData = [
            'data' => [
                'message' => 'Registration successful'
            ]
        ];

        // Mock the connector to return a response with no access token
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        // Call the method
        $result = $this->userService->register($payload);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test register method with error response
     */
    public function testRegisterHandlesErrorResponse()
    {
        // Test data
        $payload = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>'
            // Missing required fields
        ];

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid request',
            'message' => 'Missing required fields'
        ];

        // Mock the connector to return an error response
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        // Call the method
        $result = $this->userService->register($payload);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test validateUserData method with valid data
     */
    public function testValidateUserDataWithValidData()
    {
        // Valid user data
        $userData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => 12345,
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'vin' => 'ABC123456789DEF12'
        ];

        // Mock the validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($userData, $this->anything())
            ->willReturn(new ConstraintViolationList());

        // Call the method
        $result = $this->userService->validateUserData($userData);

        // Assert the result
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $result);
        $this->assertEquals(0, $result->count());
    }

    /**
     * Test validateUserData method with invalid data
     */
    public function testValidateUserDataWithInvalidData()
    {
        // Invalid user data
        $userData = [
            'firstName' => '', // Empty first name
            'lastName' => 'Doe',
            'email' => 'invalid-email', // Invalid email
            'zipCode' => 123, // Too short
            'country' => 'United States',
            'countryCode' => 'USA', // Too long
            'state' => 'California',
            'city' => 'San Francisco',
            'vin' => 'ABC123' // Too short
        ];

        // Create constraint violations
        $violations = new ConstraintViolationList([
            new ConstraintViolation('First name cannot be blank', null, [], null, 'firstName', null),
            new ConstraintViolation('Invalid email format', null, [], null, 'email', null),
            new ConstraintViolation('Zip code must be at least 5 characters', null, [], null, 'zipCode', null),
            new ConstraintViolation('Country code must be exactly 2 characters', null, [], null, 'countryCode', null),
            new ConstraintViolation('VIN must be exactly 17 characters', null, [], null, 'vin', null)
        ]);

        // Mock the validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($userData, $this->anything())
            ->willReturn($violations);

        // Call the method
        $result = $this->userService->validateUserData($userData);

        // Assert the result
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $result);
        $this->assertEquals(5, $result->count());
    }

    // =============== REFRESH TOKEN TESTS ===============

    /**
     * Test refreshToken method with successful response
     */
    public function testRefreshTokenCreatesSuccessfulResponse()
    {
        // Test data
        $payload = [
            'accessToken' => 'old-access-token',
            'refreshToken' => 'old-refresh-token'
        ];

        // Expected request body
        $expectedBody = [
            'refreshToken' => 'old-refresh-token',
            'role' => 'user',
            'grantType' => 'refresh_token'
        ];

        // Expected response data
        $responseData = [
            'data' => [
                'accessToken' => 'new-access-token',
                'expiresIn' => 3600,
                'idToken' => 'new-id-token',
                'refreshToken' => 'new-refresh-token',
                'tokenType' => 'Bearer'
            ]
        ];

        // Mock the connector to return a successful response
        $this->connector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_POST,
                'api/v1/auth/token',
                [
                    'headers' => [
                        'Authorization' => 'Bearer old-access-token',
                        "Accept: application/json",
                        'Content-Type' => 'application/json',
                        'clientId' => $this->clientId,
                        'clientSecret' => $this->clientSecret
                    ],
                    'json' => $expectedBody
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        // Call the method
        $result = $this->userService->refreshToken($payload);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData['data'], $result->getData());
    }

    /**
     * Test refreshToken method with successful response but no access token
     */
    public function testRefreshTokenWithNoAccessTokenInResponse()
    {
        // Test data
        $payload = [
            'accessToken' => 'old-access-token',
            'refreshToken' => 'old-refresh-token'
        ];

        // Response with no access token
        $responseData = [
            'data' => [
                'message' => 'Token refresh successful'
            ]
        ];

        // Mock the connector to return a response with no access token
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        // Call the method
        $result = $this->userService->refreshToken($payload);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test refreshToken method with error response
     */
    public function testRefreshTokenHandlesErrorResponse()
    {
        // Test data
        $payload = [
            'accessToken' => 'invalid-access-token',
            'refreshToken' => 'invalid-refresh-token'
        ];

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid token',
            'message' => 'The refresh token is invalid or expired'
        ];

        // Mock the connector to return an error response
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponseData));

        // Call the method
        $result = $this->userService->refreshToken($payload);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test validateRefreshTokenData method with valid data
     */
    public function testValidateRefreshTokenDataWithValidData()
    {
        // Valid refresh token data
        $refreshTokenData = [
            'refreshToken' => 'valid-refresh-token',
            'role' => 'user',
            'grantType' => 'refresh_token'
        ];

        // Mock the validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($refreshTokenData, $this->anything())
            ->willReturn(new ConstraintViolationList());

        // Call the method
        $result = $this->userService->validateRefreshTokenData($refreshTokenData);

        // Assert the result
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $result);
        $this->assertEquals(0, $result->count());
    }

    /**
     * Test validateRefreshTokenData method with invalid data
     */
    public function testValidateRefreshTokenDataWithInvalidData()
    {
        // Invalid refresh token data
        $refreshTokenData = [
            'refreshToken' => '', // Empty refresh token
            'role' => 'user',
            'grantType' => 'refresh_token'
        ];

        // Create constraint violations
        $violations = new ConstraintViolationList([
            new ConstraintViolation('Refresh token cannot be blank', null, [], null, 'refreshToken', null)
        ]);

        // Mock the validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($refreshTokenData, $this->anything())
            ->willReturn($violations);

        // Call the method
        $result = $this->userService->validateRefreshTokenData($refreshTokenData);

        // Assert the result
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $result);
        $this->assertEquals(1, $result->count());
    }

    /**
     * Test validateRefreshTokenData method with missing optional fields
     */
    public function testValidateRefreshTokenDataWithMissingOptionalFields()
    {
        // Refresh token data with only required field
        $refreshTokenData = [
            'refreshToken' => 'valid-refresh-token'
            // Missing optional fields: role, grantType
        ];

        // Mock the validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($refreshTokenData, $this->anything())
            ->willReturn(new ConstraintViolationList());

        // Call the method
        $result = $this->userService->validateRefreshTokenData($refreshTokenData);

        // Assert the result
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $result);
        $this->assertEquals(0, $result->count());
    }

    // =============== GET ISHOWROOM ELIGIBILITY TESTS ===============

    /**
     * Test getIshowroomEligibility method with successful response
     */
    public function testGetIshowroomEligibilityCreatesSuccessfulResponse()
    {
        // Test data
        $token = 'valid-token';
        $email = '<EMAIL>';

        // Expected response data
        $responseData = [
            'data' => [
                'eligible' => true,
                'credits' => [
                    [
                        'id' => 'credit-123',
                        'amount' => 100,
                        'expiryDate' => '2023-12-31'
                    ]
                ]
            ]
        ];

        // Mock the connector to return a successful response
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'api/v1/integration/ishowroom/eligibility',
                [
                    'headers' => [
                        'token' => $token,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => ['email' => $email]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        // Call the method
        $result = $this->userService->getIshowroomEligibility($token, $email);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test getIshowroomEligibility method with error response
     */
    public function testGetIshowroomEligibilityHandlesErrorResponse()
    {
        // Test data
        $token = 'invalid-token';
        $email = '<EMAIL>';

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid token',
            'message' => 'The token is invalid or expired'
        ];

        // Mock the connector to return an error response
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponseData));

        // Call the method
        $result = $this->userService->getIshowroomEligibility($token, $email);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    // =============== APPLY ISHOWROOM CREDIT TESTS ===============

    /**
     * Test applyIshowroomCredit method with successful response
     */
    public function testApplyIshowroomCreditCreatesSuccessfulResponse()
    {
        // Test data
        $token = 'valid-token';
        $id = 'credit-123';

        // Expected response data
        $responseData = [
            'data' => [
                'applied' => true,
                'amount' => 100,
                'message' => 'Credit applied successfully'
            ]
        ];

        // Mock the connector to return a successful response
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'api/v1/integration/ishowroom/credit/',
                [
                    'headers' => [
                        'token' => $token,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => ['id' => $id]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        // Call the method
        $result = $this->userService->applyIshowroomCredit($token, $id);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test applyIshowroomCredit method with error response
     */
    public function testApplyIshowroomCreditHandlesErrorResponse()
    {
        // Test data
        $token = 'valid-token';
        $id = 'invalid-credit-id';

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid credit ID',
            'message' => 'The credit ID does not exist or has already been used'
        ];

        // Mock the connector to return an error response
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        // Call the method
        $result = $this->userService->applyIshowroomCredit($token, $id);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    // =============== GET ACCOUNT LINK TESTS ===============

    /**
     * Test getAccountLink method with successful response
     */
    public function testGetAccountLinkCreatesSuccessfulResponse()
    {
        // Test data
        $providerShortCode = 'provider-123';
        $token = 'valid-token';

        // Expected response data
        $responseData = [
            'data' => [
                'url' => 'https://example.com/link/provider-123',
                'expiresIn' => 3600
            ]
        ];

        // Mock the connector to return a successful response
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'api/v2/auth/account-link/provider-123',
                [
                    'headers' => [
                        'token' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        // Call the method
        $result = $this->userService->getAccountLink($providerShortCode, $token);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test getAccountLink method with error response
     */
    public function testGetAccountLinkHandlesErrorResponse()
    {
        // Test data
        $providerShortCode = 'invalid-provider';
        $token = 'valid-token';

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid provider',
            'message' => 'The provider short code does not exist'
        ];

        // Mock the connector to return an error response
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_NOT_FOUND, $errorResponseData));

        // Call the method
        $result = $this->userService->getAccountLink($providerShortCode, $token);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test getAccountLink method with empty provider short code
     */
    public function testGetAccountLinkWithEmptyProviderShortCode()
    {
        // Test data
        $providerShortCode = '';
        $token = 'valid-token';

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid provider',
            'message' => 'Provider short code cannot be empty'
        ];

        // Mock the connector to return an error response
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        // Call the method
        $result = $this->userService->getAccountLink($providerShortCode, $token);

        // Assert the result
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }
}
