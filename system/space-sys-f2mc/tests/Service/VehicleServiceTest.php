<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\VehicleService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class VehicleServiceTest extends TestCase
{
    private $f2mConnector;
    private $vehicleService;

    protected function setUp(): void
    {
        $this->f2mConnector = $this->createMock(F2mConnector::class);
        $this->vehicleService = new VehicleService($this->f2mConnector, 'clientId', 'clientSecret');
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->f2mConnector);
        unset($this->vehicleService);
    }

    // =============== UPDATE ===============

    /**
     * Test update method with successful response
     */
    public function testUpdateCreatesSuccessfulResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179',
            'nickname' => 'My Jeep',
            'modelYear' => 2023
        ];
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        // Expected response data
        $responseData = [
            'data' => [
                'id' => 123,
                'vin' => 'FCABEF567IND00179',
                'nickname' => 'My Jeep',
                'modelYear' => 2023,
                'make' => 'Jeep',
                'model' => 'Grand Cherokee'
            ]
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_PATCH,
                "api/v1/user/vehicles/{$vehicleId}/default",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->vehicleService->update($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test update method with successful response but no data
     */
    public function testUpdateWithNoDataInResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179'
        ];
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        // Response with no data field
        $responseData = [
            'message' => 'Vehicle updated successfully'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->vehicleService->update($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test update method with error response
     */
    public function testUpdateHandlesErrorResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179'
        ];
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid request',
            'message' => 'The request parameters are invalid'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->vehicleService->update($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test update method with unauthorized response
     */
    public function testUpdateHandlesUnauthorizedResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179'
        ];
        $accessToken = "invalid-token";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        // Unauthorized response data
        $errorResponseData = [
            'error' => 'Unauthorized',
            'message' => 'Invalid access token'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponseData));

        $result = $this->vehicleService->update($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    // =============== DELETE ===============

    /**
     * Test delete method with successful response
     */
    public function testDeleteCreatesSuccessfulResponse()
    {
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken
        ];

        // Expected response data
        $responseData = [
            'data' => [
                'id' => 123,
                'deleted' => true,
                'message' => 'Vehicle deleted successfully'
            ]
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_DELETE,
                "api/v1/user/vehicles/{$vehicleId}",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->vehicleService->delete($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test delete method with successful response but no data
     */
    public function testDeleteWithNoDataInResponse()
    {
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken
        ];

        // Response with no data field
        $responseData = [
            'message' => 'Vehicle deleted successfully'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->vehicleService->delete($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test delete method with error response
     */
    public function testDeleteHandlesErrorResponse()
    {
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken
        ];

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid request',
            'message' => 'The vehicle ID does not exist'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->vehicleService->delete($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test delete method with unauthorized response
     */
    public function testDeleteHandlesUnauthorizedResponse()
    {
        $accessToken = "invalid-token";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken
        ];

        // Unauthorized response data
        $errorResponseData = [
            'error' => 'Unauthorized',
            'message' => 'Invalid access token'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponseData));

        $result = $this->vehicleService->delete($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    // =============== GET LIST ===============

    /**
     * Test getList method with successful response containing vehicles
     */
    public function testGetListCreatesSuccessfulResponse()
    {
        $accessToken = "accessToken";

        // Expected response data with multiple vehicles
        $responseData = [
            "data" => [
                [
                    "id" => 757,
                    "make" => ["id" => 5, "name" => "Jeep"],
                    "model" => ["id" => 5, "name" => "Grand Cherokee 4xe"],
                    "modelYear" => 2024,
                    "plugType" => [["id" => 1, "name" => "J1772"]],
                    "vin" => "3F9HBEEG4R90116ER",
                    "nickname" => "My Jeep"
                ],
                [
                    "id" => 758,
                    "make" => ["id" => 6, "name" => "Fiat"],
                    "model" => ["id" => 10, "name" => "500e"],
                    "modelYear" => 2023,
                    "plugType" => [["id" => 1, "name" => "J1772"]],
                    "vin" => "8A2CDEFG5H60789IJ",
                    "nickname" => "City Car"
                ]
            ]
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                "api/v1/user/vehicles",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->vehicleService->getList($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test getList method with successful response but empty vehicle list
     */
    public function testGetListWithEmptyVehicleList()
    {
        $accessToken = "accessToken";

        // Response with empty data array
        $responseData = [
            "data" => []
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->vehicleService->getList($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test getList method with error response
     */
    public function testGetListHandlesErrorResponse()
    {
        $accessToken = "accessToken";

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid request',
            'message' => 'The request parameters are invalid'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->vehicleService->getList($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test getList method with unauthorized response
     */
    public function testGetListHandlesUnauthorizedResponse()
    {
        $accessToken = "invalid-token";

        // Unauthorized response data
        $errorResponseData = [
            'error' => 'Unauthorized',
            'message' => 'Invalid access token'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponseData));

        $result = $this->vehicleService->getList($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    // =============== ADD / NEW ===============

    /**
     * Test add method with successful response
     */
    public function testAddCreatesSuccessfulResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179',
            'nickname' => 'My New Jeep',
            'modelYear' => 2023
        ];
        $accessToken = "accessToken";
        $data = [
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        // Expected response data
        $responseData = [
            'data' => [
                'id' => 759,
                'vin' => 'FCABEF567IND00179',
                'nickname' => 'My New Jeep',
                'modelYear' => 2023,
                'make' => 'Jeep',
                'model' => 'Grand Cherokee'
            ]
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_POST,
                "api/v1/user/vehicles",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->vehicleService->add($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test add method with successful response but no data
     */
    public function testAddWithNoDataInResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179'
        ];
        $accessToken = "accessToken";
        $data = [
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        // Response with no data field
        $responseData = [
            'message' => 'Vehicle added successfully'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->vehicleService->add($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test add method with error response for duplicate VIN
     */
    public function testAddHandlesDuplicateVinError()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179'
        ];
        $accessToken = "accessToken";
        $data = [
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        // Error response data for duplicate VIN
        $errorResponseData = [
            'error' => 'Duplicate VIN',
            'message' => 'A vehicle with this VIN already exists'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_CONFLICT, $errorResponseData));

        $result = $this->vehicleService->add($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CONFLICT, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test add method with error response for invalid VIN
     */
    public function testAddHandlesInvalidVinError()
    {
        $payload = [
            'vin' => 'INVALID-VIN' // Invalid VIN format
        ];
        $accessToken = "accessToken";
        $data = [
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        // Error response data for invalid VIN
        $errorResponseData = [
            'error' => 'Invalid VIN',
            'message' => 'The VIN format is invalid'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->vehicleService->add($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test add method with unauthorized response
     */
    public function testAddHandlesUnauthorizedResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179'
        ];
        $accessToken = "invalid-token";
        $data = [
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        // Unauthorized response data
        $errorResponseData = [
            'error' => 'Unauthorized',
            'message' => 'Invalid access token'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponseData));

        $result = $this->vehicleService->add($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

}
