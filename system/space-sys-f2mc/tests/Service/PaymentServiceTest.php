<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\PaymentService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PaymentServiceTest extends TestCase
{
    private $f2mConnector;
    private $paymentService;

    protected function setUp(): void
    {
        $this->f2mConnector = $this->createMock(F2mConnector::class);
        $this->paymentService = new PaymentService($this->f2mConnector);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->f2mConnector);
        unset($this->paymentService);
    }

    // =============== ADD URL TESTS ===============

    /**
     * Test addUrl method with successful response containing data
     */
    public function testAddUrlCreatesSuccessfulResponse()
    {
        $accessToken = "accessToken";

        // Expected response data
        $responseData = [
            'data' => [
                'url' => 'https://example.com/payment',
                'expiresIn' => 3600
            ]
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                "api/v3/payment-method",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Authorization' => 'Bearer accessToken'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->paymentService->addUrl($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test addUrl method with successful response but no data
     */
    public function testAddUrlWithNoDataInResponse()
    {
        $accessToken = "accessToken";

        // Response with no data field
        $responseData = [
            'message' => 'Operation successful'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->paymentService->addUrl($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test addUrl method with error response
     */
    public function testAddUrlHandlesErrorResponse()
    {
        $accessToken = "accessToken";

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid request',
            'message' => 'The request parameters are invalid'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->paymentService->addUrl($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test addUrl method with unauthorized response
     */
    public function testAddUrlHandlesUnauthorizedResponse()
    {
        $accessToken = "invalid-token";

        // Unauthorized response data
        $errorResponseData = [
            'error' => 'Unauthorized',
            'message' => 'Invalid access token'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponseData));

        $result = $this->paymentService->addUrl($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    // =============== GET PAYMENT HISTORY TESTS ===============

    /**
     * Test getPaymentHistory method with successful response containing data
     */
    public function testGetPaymentHistoryCreatesSuccessfulResponse()
    {
        $accessToken = "accessToken";

        // Expected response data
        $responseData = [
            'data' => [
                [
                    'id' => 'transaction-123',
                    'amount' => 25.50,
                    'currency' => 'USD',
                    'status' => 'COMPLETED',
                    'timestamp' => '2023-06-01T12:30:00Z',
                    'description' => 'Charging session payment'
                ],
                [
                    'id' => 'transaction-456',
                    'amount' => 18.75,
                    'currency' => 'USD',
                    'status' => 'COMPLETED',
                    'timestamp' => '2023-05-28T15:45:00Z',
                    'description' => 'Charging session payment'
                ]
            ]
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                "api/v1/payment/transactions",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Authorization' => 'Bearer accessToken'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->paymentService->getPaymentHistory($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData['data'], $result->getData());
    }

    /**
     * Test getPaymentHistory method with successful response but no data
     */
    public function testGetPaymentHistoryWithNoDataInResponse()
    {
        $accessToken = "accessToken";

        // Response with no data field
        $responseData = [
            'message' => 'No transactions found'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->paymentService->getPaymentHistory($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    /**
     * Test getPaymentHistory method with error response
     */
    public function testGetPaymentHistoryHandlesErrorResponse()
    {
        $accessToken = "accessToken";

        // Error response data
        $errorResponseData = [
            'error' => 'Invalid request',
            'message' => 'The request parameters are invalid'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->paymentService->getPaymentHistory($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test getPaymentHistory method with unauthorized response
     */
    public function testGetPaymentHistoryHandlesUnauthorizedResponse()
    {
        $accessToken = "invalid-token";

        // Unauthorized response data
        $errorResponseData = [
            'error' => 'Unauthorized',
            'message' => 'Invalid access token'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, $errorResponseData));

        $result = $this->paymentService->getPaymentHistory($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    /**
     * Test getPaymentHistory method with empty response data
     */
    public function testGetPaymentHistoryWithEmptyResponseData()
    {
        $accessToken = "accessToken";

        // Empty response data
        $responseData = [
            'data' => []
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_OK, $responseData));

        $result = $this->paymentService->getPaymentHistory($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals([], $result->getData());
    }
}
