<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\ChargingSessionService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ChargingSessionServiceTest extends TestCase
{
    private $f2mConnector;
    private $ChargingSessionService;

    protected function setUp(): void
    {
        $this->f2mConnector = $this->createMock(F2mConnector::class);
        $this->ChargingSessionService = new ChargingSessionService($this->f2mConnector);
    }

    protected function  tearDown(): void
    {
        parent::tearDown();
        unset($this->f2mConnector);
        unset($this->ChargingSessionService);
    }

    public function testStartSessionCreatesSuccessfulResponse()
    {
        $payload = [
            'locationId' => 'loc1',
            'evseId' => 'evse1',
            'coordinate' => ['lat' => 12.34, 'lng' => 56.78],
            'connectorId' => 'conn1',
            'response_url' => 'http://example.com'
        ];
        $accessToken = "accessToken";

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'api/v1/charging-sessions/start',
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ["data" => "idSession"]));

        $result = $this->ChargingSessionService->startSession($payload, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals(["data" => "idSession"], $result->getData());
    }

    public function testStartSessionHandlesErrorResponse()
    {
        $payload = [
            'locationId' => 'loc1',
            'evseId' => 'evse1',
            'coordinate' => ['lat' => 12.34, 'lng' => 56.78],
            'connectorId' => 'conn1',
            'response_url' => 'http://example.com'
        ];
        $accessToken = "accessToken";

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'api/v1/charging-sessions/start',
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Invalid request']));

        $result = $this->ChargingSessionService->startSession($payload, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals(['error' => 'Invalid request'], $result->getData());
    }

    public function testGetCdrDataCreatesSuccessfulResponse()
    {
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $accessToken = "accessToken";

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                "api/v1/charging-sessions/$sessionId/cdr",
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ["data" => "idSession"]));

        $result = $this->ChargingSessionService->getCdrData($sessionId, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals("idSession", $result->getData());
    }

    public function testGetCdrDataHandlesErrorResponse()
    {
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $accessToken = "accessToken";

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                "api/v1/charging-sessions/$sessionId/cdr",
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Invalid request']));

        $result = $this->ChargingSessionService->getCdrData($sessionId, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals(['error' => 'Invalid request'], $result->getData());
    }

    public function testAccountDeletionSuccess()
    {
        $payload = ['reasonId' => '123', 'otherReason' => 'Testing'];
        $accessToken = 'valid-token';

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_CREATED);
        $mockResponse->method('getData')->willReturn(["data" => 'true']);

        $this->f2mConnector->expects($this->once())
        ->method('callF2mc')
        ->with(
            Request::METHOD_PUT,
            "api/v1/user/delete",
            [
                'headers' => [
                    'token' => $accessToken,
                    'Content-Type' => 'application/json'
                ],
                'json' => $payload,
            ]
        )
        ->willReturn($mockResponse);

        $response = $this->ChargingSessionService->accountDeletion($payload, $accessToken);

        // Assert
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_CREATED, $response->getCode());
        $this->assertEquals(true, $response->getData());
    }
    public function testAccountDeletionHandlesErrorResponse()
    {
        $payload = ['reasonId' => '123', 'otherReason' => 'Testing'];
        $accessToken = 'valid-token';

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_UNAUTHORIZED);
        $mockResponse->method('getData')->willReturn(["message" => "Invalid token."]);

        $this->f2mConnector->expects($this->once())
        ->method('callF2mc')
        ->with(
            Request::METHOD_PUT,
            "api/v1/user/delete",
            [
                'headers' => [
                    'token' => $accessToken,
                    'Content-Type' => 'application/json'
                ],
                'json' => $payload,
            ]
        )
        ->willReturn($mockResponse);

        $response = $this->ChargingSessionService->accountDeletion($payload, $accessToken);
        // Assert
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getCode());
        $this->assertEquals(["message" => "Invalid token."], $response->getData());
    }

    public function testWalletDetailsSuccess()
    {
        $accessToken = 'valid-token';
        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn(["data" => ['totalCredits' => 0]]);
        $this->f2mConnector->expects($this->once())
        ->method('callF2mc')
        ->with(
            Request::METHOD_GET,
            "api/v1/payment/wallet/summary",
            [
                'headers' => [
                    'token' => $accessToken,
                    'Content-Type' => 'application/json'
                ]
            ]
        )
        ->willReturn($mockResponse);
        $response = $this->ChargingSessionService->walletDetails( $accessToken);
        // Assert
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals(['totalCredits' => 0], $response->getData());
    }

    public function testWalletDetailsHandlesErrorResponse()
    {
        $accessToken = 'valid-token';
        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_UNAUTHORIZED);
        $mockResponse->method('getData')->willReturn(["message" => "Invalid token."]);
        $this->f2mConnector->expects($this->once())
        ->method('callF2mc')
        ->with(
            Request::METHOD_GET,
            "api/v1/payment/wallet/summary",
            [
                'headers' => [
                    'token' => $accessToken,
                    'Content-Type' => 'application/json'
                ]
            ]
        )
        ->willReturn($mockResponse);
        $response = $this->ChargingSessionService->walletDetails($accessToken);
        // Assert
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getCode());
        $this->assertEquals(["message" => "Invalid token."], $response->getData());
    }

    // =============== STOP SESSION TESTS ===============

    public function testStopSessionCreatesSuccessfulResponse()
    {
        $payload = [
            'sessionId' => 'session-123',
            'reason' => 'USER_REQUESTED'
        ];
        $accessToken = "accessToken";

        $responseData = [
            'data' => [
                'id' => 'session-123',
                'status' => 'COMPLETED',
                'stopTime' => '2023-06-01T12:30:00Z'
            ]
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'api/v1/charging-sessions/STOP',
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->ChargingSessionService->stopSession($payload, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($responseData['data'], $result->getData());
    }

    public function testStopSessionWithNoDataInResponse()
    {
        $payload = [
            'sessionId' => 'session-123',
            'reason' => 'USER_REQUESTED'
        ];
        $accessToken = "accessToken";

        $responseData = [
            'message' => 'Session stopped successfully'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->ChargingSessionService->stopSession($payload, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testStopSessionHandlesErrorResponse()
    {
        $payload = [
            'sessionId' => 'session-123',
            'reason' => 'USER_REQUESTED'
        ];
        $accessToken = "accessToken";

        $errorResponseData = [
            'error' => 'Invalid session ID',
            'message' => 'The session ID does not exist'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->ChargingSessionService->stopSession($payload, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    // =============== GET HISTORY TESTS ===============

    public function testGetHistoryCreatesSuccessfulResponse()
    {
        $params = [
            'fromDate' => '2023-01-01',
            'toDate' => '2023-06-30',
            'limit' => 10,
            'offset' => 0
        ];
        $accessToken = "accessToken";

        $responseData = [
            'data' => [
                [
                    'id' => 'session-123',
                    'startTime' => '2023-06-01T10:00:00Z',
                    'stopTime' => '2023-06-01T12:30:00Z',
                    'status' => 'COMPLETED',
                    'location' => 'Charging Station 1'
                ],
                [
                    'id' => 'session-456',
                    'startTime' => '2023-06-02T14:00:00Z',
                    'stopTime' => '2023-06-02T16:45:00Z',
                    'status' => 'COMPLETED',
                    'location' => 'Charging Station 2'
                ]
            ]
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'api/v2/charging/history',
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ],
                    'query' => $params,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->ChargingSessionService->getHistory($params, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($responseData['data'], $result->getData());
    }

    public function testGetHistoryWithNoDataInResponse()
    {
        $params = [
            'fromDate' => '2023-01-01',
            'toDate' => '2023-06-30'
        ];
        $accessToken = "accessToken";

        $responseData = [
            'message' => 'No history found'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->ChargingSessionService->getHistory($params, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testGetHistoryHandlesErrorResponse()
    {
        $params = [
            'fromDate' => '2023-01-01',
            'toDate' => '2023-06-30'
        ];
        $accessToken = "accessToken";

        $errorResponseData = [
            'error' => 'Invalid date format',
            'message' => 'The date format is invalid'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->ChargingSessionService->getHistory($params, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }

    public function testGetHistoryWithNullToken()
    {
        $params = [
            'fromDate' => '2023-01-01',
            'toDate' => '2023-06-30'
        ];
        $accessToken = null;

        $responseData = [
            'data' => [
                [
                    'id' => 'session-123',
                    'startTime' => '2023-06-01T10:00:00Z',
                    'stopTime' => '2023-06-01T12:30:00Z'
                ]
            ]
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'api/v2/charging/history',
                [
                    'headers' => [
                        'token' => null,
                        'Content-Type' => 'application/json'
                    ],
                    'query' => $params,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->ChargingSessionService->getHistory($params, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($responseData['data'], $result->getData());
    }

    // =============== CHARGE SESSION DATA TESTS ===============

    public function testChargeSessionDataCreatesSuccessfulResponse()
    {
        $sessionId = 'session-123';
        $accessToken = "accessToken";

        $responseData = [
            'data' => [
                'id' => 'session-123',
                'startTime' => '2023-06-01T10:00:00Z',
                'status' => 'IN_PROGRESS',
                'location' => 'Charging Station 1',
                'evseId' => 'evse-456',
                'connectorId' => 'conn-789',
                'meterValues' => [
                    [
                        'timestamp' => '2023-06-01T10:15:00Z',
                        'value' => 5.2
                    ],
                    [
                        'timestamp' => '2023-06-01T10:30:00Z',
                        'value' => 10.8
                    ]
                ]
            ]
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                "api/v1/charging-sessions/{$sessionId}",
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->ChargingSessionService->chargeSessionData($sessionId, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testChargeSessionDataWithNoDataInResponse()
    {
        $sessionId = 'session-123';
        $accessToken = "accessToken";

        $responseData = [
            'message' => 'Session not found'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_CREATED, $responseData));

        $result = $this->ChargingSessionService->chargeSessionData($sessionId, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testChargeSessionDataHandlesErrorResponse()
    {
        $sessionId = 'invalid-session';
        $accessToken = "accessToken";

        $errorResponseData = [
            'error' => 'Invalid session ID',
            'message' => 'The session ID does not exist'
        ];

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorResponseData));

        $result = $this->ChargingSessionService->chargeSessionData($sessionId, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorResponseData, $result->getData());
    }
}
