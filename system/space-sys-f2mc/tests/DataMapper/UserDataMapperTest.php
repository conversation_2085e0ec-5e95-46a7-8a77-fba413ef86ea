<?php

namespace App\Tests\DataMapper;

use App\DataMapper\UserDataMapper;
use App\Model\ProfileModel;
use App\Model\UserDataModel;
use App\Model\VehicleModel;
use PHPUnit\Framework\TestCase;
use <PERSON>ymfony\Component\Serializer\SerializerInterface;

/**
 * Custom serializer for testing
 */
class TestSerializer implements SerializerInterface
{
    private $userDataModel;
    private $profileModel;
    private $vehicleModel;

    public function __construct($userDataModel, $profileModel, $vehicleModel)
    {
        $this->userDataModel = $userDataModel;
        $this->profileModel = $profileModel;
        $this->vehicleModel = $vehicleModel;
    }

    public function serialize(mixed $data, string $format, array $context = []): string
    {
        return '';
    }

    public function deserialize(mixed $data, string $type, string $format, array $context = []): mixed
    {
        return null;
    }

    public function denormalize(mixed $data, string $type, string $format = null, array $context = []): mixed
    {
        if ($type === UserDataModel::class) {
            return $this->userDataModel;
        } elseif ($type === ProfileModel::class) {
            return $this->profileModel;
        } elseif ($type === VehicleModel::class) {
            return $this->vehicleModel;
        }
        return null;
    }
}

class UserDataMapperTest extends TestCase
{
    private $serializer;
    private $userDataMapper;
    private $userDataModel;
    private $profileModel;
    private $vehicleModel;

    protected function setUp(): void
    {
        // Create mocks for the models first
        $this->userDataModel = $this->createMock(UserDataModel::class);
        $this->profileModel = $this->createMock(ProfileModel::class);
        $this->vehicleModel = $this->createMock(VehicleModel::class);

        // Create a custom serializer that implements SerializerInterface
        $this->serializer = new TestSerializer(
            $this->userDataModel,
            $this->profileModel,
            $this->vehicleModel
        );

        // Create the mapper with the serializer
        $this->userDataMapper = new UserDataMapper($this->serializer);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->serializer);
        unset($this->userDataMapper);
        unset($this->userDataModel);
        unset($this->profileModel);
        unset($this->vehicleModel);
    }

    /**
     * Test mapping with complete user data including profile and vehicle
     */
    public function testMapWithCompleteData()
    {
        // Complete user data with profile and vehicle
        $userData = [
            'accessToken' => 'access-token-123',
            'expiresIn' => 3600,
            'idToken' => 'id-token-123',
            'refreshToken' => 'refresh-token-123',
            'tokenType' => 'Bearer',
            'consent' => 'accepted',
            'isShowRoomRecords' => ['record1', 'record2'],
            'profile' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'email' => '<EMAIL>',
                'zipCode' => '12345',
                'country' => 'United States',
                'countryCode' => 'US',
                'state' => 'California',
                'city' => 'San Francisco',
                'isVinAdded' => true,
                'isNewUser' => false,
                'id' => 'user-123'
            ],
            'vehicle' => [
                'id' => 123,
                'make' => ['id' => 5, 'name' => 'Tesla'],
                'model' => ['id' => 3, 'name' => 'Model 3'],
                'modelYear' => 2023,
                'plugType' => [['id' => 1, 'name' => 'Type 2']],
                'vin' => 'ABC123456789XYZ'
            ]
        ];
       
        // Mock setProfile and setVehicle methods
        $this->userDataModel->expects($this->once())
            ->method('setProfile')
            ->with($this->profileModel);

        $this->userDataModel->expects($this->once())
            ->method('setVehicle')
            ->with($this->vehicleModel);

        $result = $this->userDataMapper->map($userData);

        $this->assertSame($this->userDataModel, $result);
    }

    /**
     * Test mapping with user data that has no profile
     */
    public function testMapWithNoProfile()
    {
        // User data without profile
        $userData = [
            'accessToken' => 'access-token-123',
            'expiresIn' => 3600,
            'idToken' => 'id-token-123',
            'refreshToken' => 'refresh-token-123',
            'tokenType' => 'Bearer',
            'vehicle' => [
                'id' => 123,
                'make' => ['id' => 5, 'name' => 'Tesla'],
                'model' => ['id' => 3, 'name' => 'Model 3'],
                'modelYear' => 2023,
                'plugType' => [['id' => 1, 'name' => 'Type 2']],
                'vin' => 'ABC123456789XYZ'
            ]
        ];

        // The serializer is already configured to return our mock models

        // Mock setProfile and setVehicle methods
        $this->userDataModel->expects($this->once())
            ->method('setProfile')
            ->with(null);

        $this->userDataModel->expects($this->once())
            ->method('setVehicle')
            ->with($this->vehicleModel);

        // Call the method
        $result = $this->userDataMapper->map($userData);

        // Assert the result
        $this->assertSame($this->userDataModel, $result);
    }

    /**
     * Test mapping with user data that has no vehicle
     */
    public function testMapWithNoVehicle()
    {
        // User data without vehicle
        $userData = [
            'accessToken' => 'access-token-123',
            'expiresIn' => 3600,
            'idToken' => 'id-token-123',
            'refreshToken' => 'refresh-token-123',
            'tokenType' => 'Bearer',
            'profile' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'email' => '<EMAIL>',
                'zipCode' => '12345',
                'country' => 'United States',
                'countryCode' => 'US',
                'state' => 'California',
                'city' => 'San Francisco',
                'isVinAdded' => false,
                'isNewUser' => true,
                'id' => 'user-123'
            ]
        ];

        // Expected profile data to be denormalized
        $expectedProfileData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => '12345',
            'country' => 'United States',
            'countryCode' => 'US',
            'state' => 'California',
            'city' => 'San Francisco',
            'isVinAdded' => false,
            'isNewUser' => true,
            'id' => 'user-123'
        ];

        // Expected user data to be denormalized
        $expectedUserData = [
            'accessToken' => 'access-token-123',
            'expiresIn' => 3600,
            'idToken' => 'id-token-123',
            'refreshToken' => 'refresh-token-123',
            'tokenType' => 'Bearer',
            'consent' => null,
            'isShowRoomRecords' => null,
            'vehicle' => null
        ];

        // Mock setProfile and setVehicle methods
        $this->userDataModel->expects($this->once())
            ->method('setProfile')
            ->with($this->profileModel);

        $this->userDataModel->expects($this->once())
            ->method('setVehicle')
            ->with(null);

        // Call the method
        $result = $this->userDataMapper->map($userData);

        // Assert the result
        $this->assertSame($this->userDataModel, $result);
    }

    /**
     * Test mapping with minimal user data (only required fields)
     */
    public function testMapWithMinimalData()
    {
        // Minimal user data with only required fields
        $userData = [
            'accessToken' => 'access-token-123',
            'refreshToken' => 'refresh-token-123',
        ];

        // Expected user data to be denormalized
        $expectedUserData = [
            'accessToken' => 'access-token-123',
            'expiresIn' => null,
            'idToken' => null,
            'refreshToken' => 'refresh-token-123',
            'tokenType' => null,
            'consent' => null,
            'isShowRoomRecords' => null,
            'vehicle' => null
        ];

        // No need to mock serializer behavior - it's already configured in setUp

        // Mock setProfile and setVehicle methods
        $this->userDataModel->expects($this->once())
            ->method('setProfile')
            ->with(null);

        $this->userDataModel->expects($this->once())
            ->method('setVehicle')
            ->with(null);

        // Call the method
        $result = $this->userDataMapper->map($userData);

        // Assert the result
        $this->assertSame($this->userDataModel, $result);
    }

    /**
     * Test mapping with incomplete profile data (missing some fields)
     */
    public function testMapWithIncompleteProfileData()
    {
        // User data with incomplete profile
        $userData = [
            'accessToken' => 'access-token-123',
            'expiresIn' => 3600,
            'refreshToken' => 'refresh-token-123',
            'profile' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'email' => '<EMAIL>',
                // Missing zipCode, country, countryCode, state, city
                'id' => 'user-123'
            ]
        ];

        // Expected profile data to be denormalized with null values for missing fields
        $expectedProfileData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'zipCode' => null,
            'country' => null,
            'countryCode' => null,
            'state' => null,
            'city' => null,
            'isVinAdded' => false, // Default value
            'isNewUser' => false,  // Default value
            'id' => 'user-123'
        ];

        // Expected user data to be denormalized
        $expectedUserData = [
            'accessToken' => 'access-token-123',
            'expiresIn' => 3600,
            'idToken' => null,
            'refreshToken' => 'refresh-token-123',
            'tokenType' => null,
            'consent' => null,
            'isShowRoomRecords' => null,
            'vehicle' => null
        ];

        // Mock setProfile and setVehicle methods
        $this->userDataModel->expects($this->once())
            ->method('setProfile')
            ->with($this->profileModel);

        $this->userDataModel->expects($this->once())
            ->method('setVehicle')
            ->with(null);

        // Call the method
        $result = $this->userDataMapper->map($userData);

        // Assert the result
        $this->assertSame($this->userDataModel, $result);
    }

    /**
     * Test mapping with incomplete vehicle data (missing some fields)
     */
    public function testMapWithIncompleteVehicleData()
    {
        // User data with incomplete vehicle
        $userData = [
            'accessToken' => 'access-token-123',
            'refreshToken' => 'refresh-token-123',
            'vehicle' => [
                'id' => 123,
                'make' => ['id' => 5, 'name' => 'Tesla'],
                // Missing model, modelYear, plugType
                'vin' => 'ABC123456789XYZ'
            ]
        ];

        // Expected vehicle data to be denormalized with empty arrays for missing fields
        $expectedVehicleData = [
            'id' => 123,
            'make' => ['id' => 5, 'name' => 'Tesla'],
            'model' => [],
            'modelYear' => null,
            'plugType' => [],
            'vin' => 'ABC123456789XYZ'
        ];

        // Expected user data to be denormalized
        $expectedUserData = [
            'accessToken' => 'access-token-123',
            'expiresIn' => null,
            'idToken' => null,
            'refreshToken' => 'refresh-token-123',
            'tokenType' => null,
            'consent' => null,
            'isShowRoomRecords' => null,
            'vehicle' => $userData['vehicle']
        ];

        // Mock setProfile and setVehicle methods
        $this->userDataModel->expects($this->once())
            ->method('setProfile')
            ->with(null);

        $this->userDataModel->expects($this->once())
            ->method('setVehicle')
            ->with($this->vehicleModel);

        // Call the method
        $result = $this->userDataMapper->map($userData);

        // Assert the result
        $this->assertSame($this->userDataModel, $result);
    }

    /**
     * Test mapping with empty array input
     */
    public function testMapWithEmptyArray()
    {
        $userData = [];

        // Expected user data to be denormalized with all null values
        $expectedUserData = [
            'accessToken' => null,
            'expiresIn' => null,
            'idToken' => null,
            'refreshToken' => null,
            'tokenType' => null,
            'consent' => null,
            'isShowRoomRecords' => null,
            'vehicle' => null
        ];

        // Mock setProfile and setVehicle methods
        $this->userDataModel->expects($this->once())
            ->method('setProfile')
            ->with(null);

        $this->userDataModel->expects($this->once())
            ->method('setVehicle')
            ->with(null);

        // Call the method
        $result = $this->userDataMapper->map($userData);

        // Assert the result
        $this->assertSame($this->userDataModel, $result);
    }
}
