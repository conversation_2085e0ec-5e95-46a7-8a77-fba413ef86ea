<?php

namespace App\Tests\DataMapper;

use App\DataMapper\VehicleDataMapper;
use App\Model\VehicleModel;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Custom serializer for testing
 */
class TestVehicleSerializer implements SerializerInterface
{
    private $vehicleModel;

    public function __construct($vehicleModel)
    {
        $this->vehicleModel = $vehicleModel;
    }

    public function serialize(mixed $data, string $format, array $context = []): string
    {
        return '';
    }

    public function deserialize(mixed $data, string $type, string $format, array $context = []): mixed
    {
        return null;
    }

    public function denormalize(mixed $data, string $type, string $format = null, array $context = []): mixed
    {
        if ($type === VehicleModel::class) {
            return $this->vehicleModel;
        }
        return null;
    }
}

class VehicleDataMapperTest extends TestCase
{
    private $serializer;
    private $vehicleDataMapper;
    private $vehicleModel;

    protected function setUp(): void
    {
        // Create a mock for the vehicle model
        $this->vehicleModel = $this->createMock(VehicleModel::class);

        // Create a custom serializer that implements SerializerInterface
        $this->serializer = new TestVehicleSerializer($this->vehicleModel);

        // Create the mapper with the serializer
        $this->vehicleDataMapper = new VehicleDataMapper($this->serializer);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->serializer);
        unset($this->vehicleDataMapper);
        unset($this->vehicleModel);
    }

    /**
     * Test mapping with complete vehicle data array
     */
    public function testMapWithCompleteVehicleDataArray()
    {
        $vehicleData = [
            [
                'vehicleId' => 123,
                'make' => ['id' => 5, 'name' => 'Tesla'],
                'model' => ['id' => 3, 'name' => 'Model 3'],
                'year' => 2023,
                'plugTypes' => [['id' => 1, 'name' => 'Type 2']],
                'vin' => 'ABC123456789XYZ'
            ],
            [
                'vehicleId' => 456,
                'make' => ['id' => 6, 'name' => 'Chevrolet'],
                'model' => ['id' => 7, 'name' => 'Bolt EV'],
                'year' => 2022,
                'plugTypes' => [['id' => 2, 'name' => 'CCS']],
                'vin' => 'DEF456789012UVW'
            ]
        ];

        // Call the method
        $result = $this->vehicleDataMapper->map($vehicleData);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertSame($this->vehicleModel, $result[0]);
        $this->assertSame($this->vehicleModel, $result[1]);
    }

    /**
     * Test mapping with a single vehicle data item
     */
    public function testMapWithSingleVehicleData()
    {
        // Single vehicle data item
        $vehicleData = [
            [
                'vehicleId' => 123,
                'make' => ['id' => 5, 'name' => 'Tesla'],
                'model' => ['id' => 3, 'name' => 'Model 3'],
                'year' => 2023,
                'plugTypes' => [['id' => 1, 'name' => 'Type 2']],
                'vin' => 'ABC123456789XYZ'
            ]
        ];

        // Call the method
        $result = $this->vehicleDataMapper->map($vehicleData);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertSame($this->vehicleModel, $result[0]);
    }

    /**
     * Test mapping with empty vehicle data array
     */
    public function testMapWithEmptyVehicleDataArray()
    {
        // Empty vehicle data array
        $vehicleData = [];

        // Call the method
        $result = $this->vehicleDataMapper->map($vehicleData);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Test mapping with incomplete vehicle data (missing some fields)
     */
    public function testMapWithIncompleteVehicleData()
    {
        // Incomplete vehicle data with missing fields
        $vehicleData = [
            [
                'vehicleId' => 123,
                // Missing make
                'model' => ['id' => 3, 'name' => 'Model 3'],
                // Missing year
                // Missing plugTypes
                'vin' => 'ABC123456789XYZ'
            ]
        ];

        // Call the method
        $result = $this->vehicleDataMapper->map($vehicleData);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertSame($this->vehicleModel, $result[0]);
    }

    /**
     * Test mapping with empty array
     */
    public function testMapWithEmptyArray()
    {
        // Empty array
        $vehicleData = [];

        // Call the method
        $result = $this->vehicleDataMapper->map($vehicleData);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Test mapping with vehicle data containing null values
     */
    public function testMapWithVehicleDataContainingNullValues()
    {
        // Vehicle data with null values
        $vehicleData = [
            [
                'vehicleId' => null,
                'make' => null,
                'model' => null,
                'year' => null,
                'plugTypes' => null,
                'vin' => null
            ]
        ];

        // Call the method
        $result = $this->vehicleDataMapper->map($vehicleData);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertSame($this->vehicleModel, $result[0]);
    }

    /**
     * Test mapping with vehicle data containing extra fields
     */
    public function testMapWithVehicleDataContainingExtraFields()
    {
        // Vehicle data with extra fields
        $vehicleData = [
            [
                'vehicleId' => 123,
                'make' => ['id' => 5, 'name' => 'Tesla'],
                'model' => ['id' => 3, 'name' => 'Model 3'],
                'year' => 2023,
                'plugTypes' => [['id' => 1, 'name' => 'Type 2']],
                'vin' => 'ABC123456789XYZ',
                'extraField1' => 'value1',
                'extraField2' => 'value2'
            ]
        ];
        
        // Call the method
        $result = $this->vehicleDataMapper->map($vehicleData);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertSame($this->vehicleModel, $result[0]);
    }
}
