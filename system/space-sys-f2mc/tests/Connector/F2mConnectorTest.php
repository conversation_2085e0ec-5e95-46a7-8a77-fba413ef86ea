<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class F2mConnectorTest extends TestCase
{
    private $customHttpClient;
    private $logger;
    private $f2mConnector;
    private $url = 'https://api.example.com';
    private $clientId = 'test-client-id';
    private $clientSecret = 'test-client-secret';

    protected function setUp(): void
    {
        $this->customHttpClient = $this->createMock(CustomHttpClient::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->f2mConnector = new F2mConnector(
            $this->customHttpClient,
            $this->url,
            $this->clientId,
            $this->clientSecret
        );
        
        $this->f2mConnector->setLogger($this->logger);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->customHttpClient);
        unset($this->logger);
        unset($this->f2mConnector);
    } 

    // =============== call() method tests ===============

    public function testCallSuccessfulRequest()
    {
        // Test data
        $method = 'GET';
        $uri = 'api/v1/resource';
        $options = ['headers' => ['Content-Type' => 'application/json']];
        $responseData = ['key' => 'value'];
        $responseCode = Response::HTTP_OK;
        
        // Configure mocks
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [$this->stringContains('F2mApiConnector::call request')],
                [$this->stringContains('F2mApiConnector::call response')]
            );
            
        $this->customHttpClient->expects($this->once())
            ->method('request')
            ->with($method, $this->url . '/' . $uri, $options)
            ->willReturn(new WSResponse($responseCode, $responseData));
        
        // Execute method
        $result = $this->f2mConnector->call($method, $uri, $options);
        
        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($responseCode, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testCallWithException()
    {
        // Test data
        $method = 'POST';
        $uri = 'api/v1/resource';
        $options = ['json' => ['name' => 'test']];
        $exceptionMessage = 'Connection timeout';
        $exceptionCode = 408;
        
        // Configure mocks
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('F2mApiConnector::call request'));
            
        $this->customHttpClient->expects($this->once())
            ->method('request')
            ->with($method, $this->url . '/' . $uri, $options)
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
            
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('error occured while calling ' . $uri));
        
        // Execute method
        $result = $this->f2mConnector->call($method, $uri, $options);
        
        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }

    // =============== callF2mc() method tests ===============

    public function testCallF2mcSuccessfulRequest()
    {
        // Test data
        $method = 'GET';
        $uri = 'api/v1/resource';
        $options = ['headers' => ['Content-Type' => 'application/json']];
        $responseData = ['key' => 'value'];
        $responseCode = Response::HTTP_OK;
        
        $expectedOptions = [
            'headers' => [
                'Content-Type' => 'application/json',
                'clientId' => $this->clientId,
                'clientSecret' => $this->clientSecret
            ]
        ];
        
        // Configure mocks
        $this->logger->expects($this->exactly(2))
            ->method('info')
            ->withConsecutive(
                [$this->stringContains('F2mApiConnector::call response')],
                [$this->stringContains('F2mConnector::callF2mc:: response')]
            );
            
        $this->customHttpClient->expects($this->once())
            ->method('request')
            ->with($method, $this->url . '/' . $uri, $expectedOptions)
            ->willReturn(new WSResponse($responseCode, $responseData));
        
        // Execute method
        $result = $this->f2mConnector->callF2mc($method, $uri, $options);
        
        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($responseCode, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testCallF2mcWithAccessToken()
    {
        // Test data
        $method = 'GET';
        $uri = 'api/v1/resource';
        $accessToken = 'test-access-token';
        $options = ['headers' => ['token' => $accessToken]];
        $responseData = ['key' => 'value'];
        $responseCode = Response::HTTP_OK;
        
        // Expected options with token converted to Authorization header
        $expectedOptions = [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'clientId' => $this->clientId,
                'clientSecret' => $this->clientSecret
            ]
        ];
        
        // Configure mocks
        $this->customHttpClient->expects($this->once())
            ->method('request')
            ->with($method, $this->url . '/' . $uri, $expectedOptions)
            ->willReturn(new WSResponse($responseCode, $responseData));
        
        // Execute method
        $result = $this->f2mConnector->callF2mc($method, $uri, $options);
        
        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($responseCode, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testCallF2mcWithException()
    {
        // Test data
        $method = 'POST';
        $uri = 'api/v1/resource';
        $options = ['json' => ['name' => 'test']];
        $exceptionMessage = 'Connection timeout';
        $exceptionCode = 408;
        
        // Configure mocks
        $this->logger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('F2mApiConnector::call response'));
            
        $this->customHttpClient->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
            
        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('F2mConnector::callF2mc:: error occured while calling ' . $uri));
        
        // Execute method
        $result = $this->f2mConnector->callF2mc($method, $uri, $options);
        
        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }

    public function testCallF2mcWithNoOptions()
    {
        // Test data
        $method = 'GET';
        $uri = 'api/v1/resource';
        $responseData = ['key' => 'value'];
        $responseCode = Response::HTTP_OK;
        
        // Expected options with only client credentials
        $expectedOptions = [
            'headers' => [
                'clientId' => $this->clientId,
                'clientSecret' => $this->clientSecret
            ]
        ];
        
        // Configure mocks
        $this->customHttpClient->expects($this->once())
            ->method('request')
            ->with($method, $this->url . '/' . $uri, $expectedOptions)
            ->willReturn(new WSResponse($responseCode, $responseData));
        
        // Execute method
        $result = $this->f2mConnector->callF2mc($method, $uri);
        
        // Assert results
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($responseCode, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }
}