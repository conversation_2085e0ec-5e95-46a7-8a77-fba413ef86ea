<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class CustomHttpClientTest extends TestCase
{
    private $httpClient;
    private $logger;
    private $customHttpClient;
    private $response;

    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->response = $this->createMock(ResponseInterface::class);
        
        $this->customHttpClient = new CustomHttpClient($this->httpClient);
        $this->customHttpClient->setLogger($this->logger);
    }

    protected function tearDown(): void
    {
        unset($this->httpClient);
        unset($this->logger);
        unset($this->customHttpClient);
        unset($this->response);
    }

    public function testSuccessfulRequest()
    {
        $method = 'GET';
        $url = 'https://api.example.com/endpoint';
        $options = ['headers' => ['Content-Type' => 'application/json']];
        $responseData = ['key' => 'value'];
        
        // Configure mocks
        $this->logger->expects($this->once())
            ->method('info')
            ->with("Call URL: {$url} {$method}");
            
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($this->response);
            
        $this->response->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_OK);
            
        $this->response->expects($this->once())
            ->method('toArray')
            ->with(false)
            ->willReturn($responseData);
        
        $result = $this->customHttpClient->request($method, $url, $options);
        
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }
    
    public function testNoContentResponse()
    {
        $method = 'DELETE';
        $url = 'https://api.example.com/resource/123';
        $options = [];
        
        $this->logger->expects($this->once())
            ->method('info')
            ->with("Call URL: {$url} {$method}");
            
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($this->response);
            
        $this->response->expects($this->once())
            ->method('getStatusCode')
            ->willReturn(Response::HTTP_NO_CONTENT);
            
        $this->response->expects($this->never())
            ->method('toArray');
        
        $result = $this->customHttpClient->request($method, $url, $options);
        
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NO_CONTENT, $result->getCode());
        $this->assertEquals([], $result->getData());
    }
    
    public function testExceptionHandling()
    {
        $method = 'POST';
        $url = 'https://api.example.com/resource';
        $options = ['json' => ['name' => 'test']];
        $exceptionMessage = 'Connection timeout';
        $exceptionCode = 408;
        
        $this->logger->expects($this->once())
            ->method('info')
            ->with("Call URL: {$url} {$method}");
            
        $this->httpClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willThrowException(new \Exception($exceptionMessage, $exceptionCode));
            
        $this->logger->expects($this->once())
            ->method('error')
            ->with('Cached Exception : CustomHttpClient::request ' . $exceptionMessage);
       
        $result = $this->customHttpClient->request($method, $url, $options);
       
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($exceptionCode, $result->getCode());
        $this->assertEquals($exceptionMessage, $result->getData());
    }

    public function testSuccessfulRequestWithDifferentStatusCodes()
    {
        $testCases = [
            ['status' => Response::HTTP_CREATED, 'data' => ['id' => 123]],
            ['status' => Response::HTTP_ACCEPTED, 'data' => ['status' => 'processing']],
            ['status' => Response::HTTP_OK, 'data' => ['result' => 'success']]
        ];
        
        foreach ($testCases as $testCase) {
            $this->setUp();
            
            $method = 'POST';
            $url = 'https://api.example.com/resource';
            $options = [];
            $responseData = $testCase['data'];
            $statusCode = $testCase['status'];
            
            // Configure mocks
            $this->logger->expects($this->once())
                ->method('info')
                ->with("Call URL: {$url} {$method}");
                
            $this->httpClient->expects($this->once())
                ->method('request')
                ->with($method, $url, $options)
                ->willReturn($this->response);
                
            $this->response->expects($this->once())
                ->method('getStatusCode')
                ->willReturn($statusCode);
                
            $this->response->expects($this->once())
                ->method('toArray')
                ->with(false)
                ->willReturn($responseData);
            
            // Execute method
            $result = $this->customHttpClient->request($method, $url, $options);
            
            // Assert results
            $this->assertInstanceOf(WSResponse::class, $result);
            $this->assertEquals($statusCode, $result->getCode());
            $this->assertEquals($responseData, $result->getData());
        }
    }
}