<?php

namespace App\Tests\Connector;

use App\Connector\CorvetApiConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class CorvetApiConnectorTest extends TestCase
{
    private $httpClient;
    private $logger;
    private $corvetApiConnector;
    private $url = 'https://api-basic-preprod.groupe-psa.com/applications/corvet/v1/extraction';
    private $clientId = 'test-client-id';
    private $basicAuth = 'test-basic-auth';

    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->corvetApiConnector = new CorvetApiConnector(
            $this->httpClient,
            $this->url,
            $this->clientId,
            $this->basicAuth
        );
        $this->corvetApiConnector->setLogger($this->logger);
    }

    public function testCallSuccess(): void
    {
        $vin = 'VYEATTEN0SPU00068';
        $brand = 'JE';
        
        $responseContent = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<MESSAGE>
  <VEHICULE>
    <VIN>VYEATTEN0SPU00068</VIN>
    <LCDV_BASE>1PK0NNRKAQC0A0B0</LCDV_BASE>
    <LISTE_ATTRIBUTES_7>
      <ATTRIBUT>DXD04CD</ATTRIBUT>
      <ATTRIBUT>ABCDEF</ATTRIBUT>
    </LISTE_ATTRIBUTES_7>
  </VEHICULE>
</MESSAGE>
XML;

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')->willReturn(200);
        $response->method('getContent')->willReturn($responseContent);

        $this->httpClient->expects($this->once())
            ->method('request')
            ->with(
                'POST',
                $this->url,
                $this->callback(function ($options) use ($vin, $brand) {
                    // Verify that the request contains the expected data
                    $this->assertArrayHasKey('headers', $options);
                    $this->assertArrayHasKey('query', $options);
                    $this->assertArrayHasKey('body', $options);
                    
                    $this->assertArrayHasKey('Content-Type', $options['headers']);
                    $this->assertArrayHasKey('Authorization', $options['headers']);
                    
                    $this->assertArrayHasKey('client_id', $options['query']);
                    
                    // Check that VIN parts are in the XML payload
                    $wmi = substr($vin, 0, 3);
                    $vds = substr($vin, 3, 6);
                    $vis = substr($vin, 9);
                    
                    $this->assertStringContainsString("<WMI>{$wmi}</WMI>", $options['body']);
                    $this->assertStringContainsString("<VDS>{$vds}</VDS>", $options['body']);
                    $this->assertStringContainsString("<VIS>{$vis}</VIS>", $options['body']);
                    
                    return true;
                })
            )
            ->willReturn($response);

        $result = $this->corvetApiConnector->call($vin, $brand);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(200, $result->getCode());
        $this->assertTrue($result->isSuccess());
        
        $data = $result->getData();
        $this->assertArrayHasKey('success', $data);
        $this->assertArrayHasKey('VEHICULE', $data['success']);
        $this->assertArrayHasKey('VIN', $data['success']['VEHICULE']);
        $this->assertEquals($vin, $data['success']['VEHICULE']['VIN']);
    }

    public function testCallError(): void
    {
        $vin = 'VYEATTEN0SPU00068';
        $brand = 'JE';

        $this->httpClient->expects($this->once())
            ->method('request')
            ->willThrowException(new \Exception('API Error', 500));

        $result = $this->corvetApiConnector->call($vin, $brand);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(500, $result->getCode());
        $this->assertFalse($result->isSuccess());
        
        $data = $result->getData();
        $this->assertArrayHasKey('error', $data);
    }
}
