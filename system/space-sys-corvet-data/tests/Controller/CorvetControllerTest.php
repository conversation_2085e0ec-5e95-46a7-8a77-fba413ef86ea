<?php

namespace App\Tests\Controller;

use App\Connector\CorvetApiConnector;
use App\Controller\CorvetController;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;

class CorvetControllerTest extends TestCase
{
    private $corvetApiConnector;
    private $controller;

    protected function setUp(): void
    {
        $this->corvetApiConnector = $this->createMock(CorvetApiConnector::class);
        $this->controller = new CorvetController($this->corvetApiConnector);
    }

    public function testGetCorvetDataSuccess(): void
    {
        $vin = 'VYEATTEN0SPU00068';
        $brand = 'JE';
        
        $request = new Request(['brand' => $brand]);
        
        $responseData = [
            'success' => [
                'VEHICULE' => [
                    'VIN' => $vin,
                    'LCDV_BASE' => '1PK0NNRKAQC0A0B0',
                    'LISTE_ATTRIBUTES_7' => [
                        'ATTRIBUT' => ['DXD04CD', 'ABCDEF']
                    ]
                ]
            ]
        ];
        
        $wsResponse = new WSResponse(200, $responseData);
        
        $this->corvetApiConnector->expects($this->once())
            ->method('call')
            ->with($vin, $brand)
            ->willReturn($wsResponse);
        
        $response = $this->controller->getCorvetData($vin, $request);
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(json_encode($responseData), $response->getContent());
    }
    
    public function testGetCorvetDataMissingBrand(): void
    {
        $vin = 'VYEATTEN0SPU00068';
        $request = new Request();
        
        $response = $this->controller->getCorvetData($vin, $request);
        
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Brand parameter is required', $response->getContent());
    }
    
    public function testGetCorvetDataApiError(): void
    {
        $vin = 'VYEATTEN0SPU00068';
        $brand = 'JE';
        
        $request = new Request(['brand' => $brand]);
        
        $wsResponse = new WSResponse(500, ['error' => 'API Error']);
        
        $this->corvetApiConnector->expects($this->once())
            ->method('call')
            ->with($vin, $brand)
            ->willReturn($wsResponse);
        
        $response = $this->controller->getCorvetData($vin, $request);
        
        $this->assertEquals(500, $response->getStatusCode());
        $this->assertStringContainsString('Failed to retrieve data from Corvet API', $response->getContent());
    }
}
