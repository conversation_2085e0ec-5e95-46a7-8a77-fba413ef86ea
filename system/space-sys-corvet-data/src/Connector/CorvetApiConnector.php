<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Exception\CorvetApiException;
use App\Trait\LoggerTrait;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class CorvetApiConnector
{
    use LoggerTrait;

    public function __construct(
        private HttpClientInterface $client,
        private string $url,
        private string $clientId,
        private string $basicAuth,
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    /**
     * Call the Corvet API with XML payload
     */
    public function call(string $vin, string $brand): WSResponse
    {
        try {
            $this->logger->info('CorvetApiConnector::call for VIN: ' . $vin . ' and brand: ' . $brand);

            // Parse VIN into WMI, VDS, and VIS components
            $wmi = substr($vin, 0, 3);
            $vds = substr($vin, 3, 6);
            $vis = substr($vin, 9);

            // Prepare XML payload
            $xmlPayload = $this->buildXmlPayload($wmi, $vds, $vis, $brand);

            // Set up request options
            $options = [
                'headers' => [
                    'Content-Type'  => 'application/xml',
                    'Authorization' => 'Basic ' . $this->basicAuth,
                ],
                'query' => [
                    'client_id' => $this->clientId
                ],
                'body'    => $xmlPayload,
                'timeout' => 20
            ];

            // Make the request
            $response = $this->client->request('POST', $this->url, $options);

            // Process the response
            $statusCode = $response->getStatusCode();
            $content    = $response->getContent(false);

            if ($statusCode >= 200 && $statusCode < 300) {
                // Parse XML response to array
                $parsedResponse = $this->parseXmlResponse($content);
                return new WSResponse($statusCode, $parsedResponse);
            } else {
                $this->logger->error('CorvetApiConnector::call Error: ' . $statusCode . ' - ' . $content);
                return new WSResponse($statusCode, ['error' => 'Failed to retrieve data from Corvet API']);
            }
        } catch (\Exception $e) {
            $this->logger->error('CorvetApiConnector::call Exception: ' . $e->getMessage());
            return new WSResponse($e->getCode() ?: 500, ['error' => $e->getMessage()]);
        }
    }

    /**
     * Build XML payload for Corvet API
     */
    private function buildXmlPayload(string $wmi, string $vds, string $vis, string $brand): string
    {
        return <<<XML
<MESSAGE>
<ENTETE>
<EMETTEUR>MYM_PREPROD</EMETTEUR>
</ENTETE>
<RECHERCHE>
<CRITERE>
<WMI>{$wmi}</WMI>
<VDS>{$vds}</VDS>
<VIS>{$vis}</VIS>
</CRITERE>
</RECHERCHE>
<REPONSE>
<DONNEES_VEHICULE>
<DONNEE>VIN</DONNEE>
<DONNEE>LCDV_BASE</DONNEE>
<DONNEE>N_APV_PR</DONNEE>
<DONNEE>ANNEE_MODELE</DONNEE>
<DONNEE>MARQUE_COMMERCIALE</DONNEE>
<DONNEE>DATE_DEBUT_GARANTIE</DONNEE>
<DONNEE>DATE_ENTREE_COMMERCIALISATION</DONNEE>
<DONNEE>LIGNE_DE_PRODUIT</DONNEE>
</DONNEES_VEHICULE>
<LISTE_ATTRIBUTS>
<ATTRIBUT>%</ATTRIBUT>
</LISTE_ATTRIBUTS>
</REPONSE>
</MESSAGE>
XML;
    }

    /**
     * Parse XML response to array
     */
    private function parseXmlResponse(string $xmlContent): array
    {
        try {
            $xml    = new \SimpleXMLElement($xmlContent);
            $result = [];

            // Convert XML to array
            $result = $this->xmlToArray($xml);

            // Format the response to match the expected structure
            return [
                'success' => [
                    'VEHICULE' => $result['VEHICULE'] ?? []
                ]
            ];
        } catch (\Exception $e) {
            $this->logger->error('Error parsing XML response: ' . $e->getMessage());
            throw new CorvetApiException('Failed to parse Corvet API response: ' . $e->getMessage());
        }
    }

    /**
     * Convert XML to array
     */
    private function xmlToArray(\SimpleXMLElement $xml): array
    {
        $result = [];

        // Convert attributes to array
        foreach ($xml->attributes() as $key => $value) {
            $result['@attributes'][$key] = (string) $value;
        }

        // Convert children to array
        if ($xml->count() === 0) {
            $result['value'] = (string) $xml;
        } else {
            foreach ($xml->children() as $child) {
                $childName = $child->getName();

                if (!isset($result[$childName])) {
                    $result[$childName] = $this->xmlToArray($child);
                } else {
                    if (!is_array($result[$childName]) || !isset($result[$childName][0])) {
                        $result[$childName] = [$result[$childName]];
                    }
                    $result[$childName][] = $this->xmlToArray($child);
                }
            }
        }

        return $result;
    }
}
