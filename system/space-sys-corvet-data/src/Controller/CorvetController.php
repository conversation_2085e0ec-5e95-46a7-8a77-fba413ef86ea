<?php

namespace App\Controller;

use App\Exception\CorvetApiException;
use App\Service\CorvetService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use OpenApi\Attributes as OA;

class CorvetController extends AbstractController
{
    public function __construct(
        private CorvetService $corvetService
    ) {
    }

    #[Route('/v1/corvet/{vin}/data', name: 'corvet_data', methods: ['GET'])]
    #[OA\Tag(name: 'Corvet')]
    #[OA\Parameter(
        name: 'vin',
        description: 'Vehicle Identification Number',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        description: 'Vehicle brand code (e.g., JE, AP, AC, DS)',
        in: 'query',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]

    #[OA\Response(
        response: 200,
        description: 'Returns vehicle data from Corvet API',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'success', type: 'object', properties: [
                    new OA\Property(property: 'VEHICULE', type: 'object', properties: [
                        new OA\Property(property: 'DONNEES_VEHICULE', type: 'object'),
                        new OA\Property(property: 'LISTE_ATTRIBUTES_7', type: 'object', properties: [
                            new OA\Property(property: 'ATTRIBUT', type: 'array', items: new OA\Items(type: 'string'))
                        ])
                    ])
                ])
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad request - Missing required parameters',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Brand parameter is required')
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Vehicle not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Vehicle not found')
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: 'Internal server error',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Failed to retrieve data from Corvet API')
            ]
        )
    )]
    public function getCorvetData(string $vin, Request $request): JsonResponse
    {
        // Get brand parameter from query
        $brand = $request->query->get('brand');

        if (!$brand) {
            return new JsonResponse(['error' => 'Brand parameter is required'], Response::HTTP_BAD_REQUEST);
        }

        try {
            // Call Corvet API through the service
            $response = $this->corvetService->getVehicleData($vin, $brand);

            if (!$response->isSuccess()) {
                return new JsonResponse(
                    ['error' => 'Failed to retrieve data from Corvet API'],
                    $response->getCode()
                );
            }

            $responseData = $response->getData();

            // Check if vehicle exists
            if (empty($responseData['success']['VEHICULE']) ||
                ($responseData['success']['VEHICULE']['@attributes']['Existe'] ?? '') !== 'O') {
                return new JsonResponse(['error' => 'Vehicle not found'], Response::HTTP_NOT_FOUND);
            }

            // Format the response to match the expected structure for space-proc-car and space-proc-user-veh
            $formattedResponse = $this->corvetService->formatResponseForClients($responseData);

            return new JsonResponse($formattedResponse);
        } catch (CorvetApiException $e) {
            return new JsonResponse(['error' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'An unexpected error occurred'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
