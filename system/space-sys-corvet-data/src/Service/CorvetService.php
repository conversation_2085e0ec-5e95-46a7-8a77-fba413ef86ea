<?php

namespace App\Service;

use App\Connector\CorvetApiConnector;
use App\Exception\CorvetApiException;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

/**
 * Service for handling Corvet API operations
 */
class CorvetService
{
    use LoggerTrait;

    public function __construct(
        private CorvetApiConnector $corvetApiConnector,
        private string $emetteur,
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    /**
     * Get vehicle data from Corvet API
     *
     * @param string $vin   Vehicle Identification Number
     * @param string $brand Vehicle brand code
     *
     * @return WSResponse Response containing vehicle data
     */
    public function getVehicleData(string $vin, string $brand): WSResponse
    {
        $this->logger->info('CorvetService::getVehicleData - Getting data for VIN: ' . $vin . ', Brand: ' . $brand);

        try {
            return $this->corvetApiConnector->call($vin, $brand);
        } catch (CorvetApiException $e) {
            $this->logger->error('CorvetService::getVehicleData - Error: ' . $e->getMessage());
            return new WSResponse($e->getCode() ?: 500, ['error' => $e->getMessage()]);
        } catch (\Exception $e) {
            $this->logger->error('CorvetService::getVehicleData - Unexpected error: ' . $e->getMessage());
            return new WSResponse(500, ['error' => 'An unexpected error occurred']);
        }
    }

    /**
     * Format the Corvet API response to match the expected structure for client microservices
     *
     * @param array $responseData The raw response data from Corvet API
     *
     * @return array Formatted response data
     */
    public function formatResponseForClients(array $responseData): array
    {
        $vehicle = $responseData['success']['VEHICULE'] ?? [];

        // Ensure we have the required data
        if (empty($vehicle) || empty($vehicle['DONNEES_VEHICULE'])) {
            return $responseData; // Return original response if structure is unexpected
        }

        // Get vehicle data and extract values from nested structure
        $vehicleData = $vehicle['DONNEES_VEHICULE'];
        $extractedVehicleData = [];
        foreach ($vehicleData as $key => $data) {
            // Convert empty values to empty arrays to match expected format
            if (empty($data['value'])) {
                $extractedVehicleData[$key] = [];
            } else {
                $extractedVehicleData[$key] = $data['value'];
            }
        }

        // Get attributes and ensure it's an array
        $attributes = $vehicle['LISTE_ATTRIBUTS']['ATTRIBUT'] ?? [];
        if (!is_array($attributes) || !isset($attributes[0])) {
            $attributes = [$attributes];
        }

        // Extract attribute values and add 'CD' suffix to match expected format
        $attributeValues = [];
        foreach ($attributes as $attribute) {
            if (isset($attribute['value'])) {
                $attrValue = $attribute['value'];

                // Check if the attribute already ends with 'CD' or 'G '
                if (!preg_match('/(CD|G )$/', $attrValue)) {
                    // Add 'CD' suffix for attributes starting with D or 'G ' for attributes starting with T
                    if (substr($attrValue, 0, 1) === 'D') {
                        $attrValue .= 'CD';
                    } elseif (substr($attrValue, 0, 1) === 'T') {
                        $attrValue .= 'G ';
                    }
                }

                $attributeValues[] = $attrValue;
            }
        }

        // Format the response to match the expected structure
        $formattedResponse = [
            'success' => [
                'ENTETE' => [
                    'EMETTEUR' => $this->emetteur
                ],
                'VEHICULE' => [
                    '@attributes' => $vehicle['@attributes'] ?? ['Existe' => 'O'],
                    'DONNEES_VEHICULE' => $extractedVehicleData,
                    'LISTE_ATTRIBUTES_7' => [
                        'ATTRIBUT' => $attributeValues
                    ]
                ]
            ]
        ];

        return $formattedResponse;
    }
}
