<?php

namespace App\Helper;

class WSResponse
{
    private int $code;
    private mixed $data;

    public function __construct(int $code, mixed $data)
    {
        $this->code = $code;
        $this->data = $data;
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function getData(bool $asArray = true): mixed
    {
        if ($asArray && is_string($this->data)) {
            return json_decode($this->data, true);
        }

        return $this->data;
    }

    public function isSuccess(): bool
    {
        return $this->code >= 200 && $this->code < 300;
    }
}
